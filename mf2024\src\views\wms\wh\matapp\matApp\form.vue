<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'wms/wh:matapp:matApp:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm">
      <template #matAppsList>
        <BasicTable
          @register="registerMatAppsTable"
          @row-click="handleMatAppsRowClick"
        />
        <a-button class="mt-2" @click="handleMatAppsAdd" v-auth="'wms/wh:matapp:matApp:edit'">
          <Icon icon="i-ant-design:plus-circle-outlined" /> {{ t('新增') }}
        </a-button>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsWmsWhMatappMatAppForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { MatApp, matAppSave, matAppForm } from '/@/api/wms/wh/matapp/matApp';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('wms/wh.matapp.matApp');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<MatApp>({} as MatApp);

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增领料申请') : t('编辑领料申请'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('id'),
      field: 'id',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
      required: true,
    },
    {
      label: t('单据号'),
      field: 'ccode',
      component: 'Input',
      componentProps: {
        maxlength: 30,
      },
      required: true,
    },
    {
      label: t('单据日期'),
      field: 'ddate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        showTime: false,
      },
      required: true,
    },
    {
      label: t('出库类别'),
      field: 'crdCode',
      component: 'Input',
      componentProps: {
        maxlength: 5,
      },
    },
    {
      label: t('出库类别名称'),
      field: 'crdName',
      component: 'Input',
      componentProps: {
        maxlength: 12,
      },
    },
    {
      label: t('部门编码'),
      field: 'depCode',
      component: 'Input',
      componentProps: {
        maxlength: 12,
      },
    },
    {
      label: t('部门名称'),
      field: 'depName',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('备注'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 255,
      },
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('close_by'),
      field: 'closeBy',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('verif_by'),
      field: 'verifBy',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('cdefine1'),
      field: 'cdefine1',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('cdefine2'),
      field: 'cdefine2',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('cdefine3'),
      field: 'cdefine3',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('cdefine4'),
      field: 'cdefine4',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('cdefine5'),
      field: 'cdefine5',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('cdefine6'),
      field: 'cdefine6',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('cdefine7'),
      field: 'cdefine7',
      component: 'Input',
      componentProps: {
        maxlength: 51,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('cdefine8'),
      field: 'cdefine8',
      component: 'Input',
      componentProps: {
        maxlength: 4,
      },
    },
    {
      label: t('cdefine9'),
      field: 'cdefine9',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
    },
    {
      label: t('cdefine10'),
      field: 'cdefine10',
      component: 'Input',
      componentProps: {
        maxlength: 60,
      },
    },
    {
      label: t('cdefine11'),
      field: 'cdefine11',
      component: 'Input',
      componentProps: {
        maxlength: 120,
      },
    },
    {
      label: t('cdefine12'),
      field: 'cdefine12',
      component: 'Input',
      componentProps: {
        maxlength: 120,
      },
    },
    {
      label: t('cdefine13'),
      field: 'cdefine13',
      component: 'Input',
      componentProps: {
        maxlength: 120,
      },
    },
    {
      label: t('cdefine14'),
      field: 'cdefine14',
      component: 'Input',
      componentProps: {
        maxlength: 120,
      },
    },
    {
      label: t('cdefine15'),
      field: 'cdefine15',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('cdefine16'),
      field: 'cdefine16',
      component: 'Input',
      componentProps: {
        maxlength: 51,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('领料申请明细'),
      field: 'matAppsList',
      component: 'Input',
      colProps: { lg: 24, md: 24 },
      slot: 'matAppsList',
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerMatAppsTable, matAppsTable] = useTable({
    actionColumn: {
      width: 60,
      actions: (record: Recordable) => [
        {
          icon: 'i-ant-design:delete-outlined',
          color: 'error',
          popConfirm: {
            title: '是否确认删除',
            confirm: handleMatAppsDelete.bind(this, record),
          },
          auth: 'wms/wh:matapp:matApp:edit',
        },
      ],
    },
    rowKey: 'id',
    pagination: false,
    bordered: true,
    size: 'small',
    inset: true,
  });

  async function setMatAppsTableData(_res: Recordable) {
    matAppsTable.setColumns([
      {
        title: t('行号'),
        dataIndex: 'irowno',
        width: 130,
        align: 'center',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 8,
        },
        editRule: false,
      },
      {
        title: t('存货编码'),
        dataIndex: 'invCode',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 60,
        },
        editRule: false,
      },
      {
        title: t('存货名称'),
        dataIndex: 'invName',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 255,
        },
        editRule: false,
      },
      {
        title: t('规格型号'),
        dataIndex: 'invStd',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 255,
        },
        editRule: false,
      },
      {
        title: t('单位编号'),
        dataIndex: 'unitCode',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 35,
        },
        editRule: false,
      },
      {
        title: t('单位'),
        dataIndex: 'unitName',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 20,
        },
        editRule: false,
      },
      {
        title: t('批次'),
        dataIndex: 'cbatch',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 60,
        },
        editRule: false,
      },
      {
        title: t('仓库编码'),
        dataIndex: 'whCode',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 10,
        },
        editRule: false,
      },
      {
        title: t('仓库'),
        dataIndex: 'whName',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 20,
        },
        editRule: false,
      },
      {
        title: t('cdefine22'),
        dataIndex: 'cdefine22',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 60,
        },
        editRule: false,
      },
      {
        title: t('cdefine23'),
        dataIndex: 'cdefine23',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 60,
        },
        editRule: false,
      },
      {
        title: t('cdefine24'),
        dataIndex: 'cdefine24',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 60,
        },
        editRule: false,
      },
      {
        title: t('cdefine25'),
        dataIndex: 'cdefine25',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 60,
        },
        editRule: false,
      },
      {
        title: t('cdefine26'),
        dataIndex: 'cdefine26',
        width: 130,
        align: 'right',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 51,
        },
        editRule: false,
      },
      {
        title: t('cdefine27'),
        dataIndex: 'cdefine27',
        width: 130,
        align: 'right',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 51,
        },
        editRule: false,
      },
      {
        title: t('cdefine28'),
        dataIndex: 'cdefine28',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 120,
        },
        editRule: false,
      },
      {
        title: t('cdefine29'),
        dataIndex: 'cdefine29',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 120,
        },
        editRule: false,
      },
      {
        title: t('cdefine30'),
        dataIndex: 'cdefine30',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 120,
        },
        editRule: false,
      },
      {
        title: t('cdefine31'),
        dataIndex: 'cdefine31',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 120,
        },
        editRule: false,
      },
      {
        title: t('cdefine32'),
        dataIndex: 'cdefine32',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 120,
        },
        editRule: false,
      },
      {
        title: t('cdefine33'),
        dataIndex: 'cdefine33',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 120,
        },
        editRule: false,
      },
      {
        title: t('cdefine34'),
        dataIndex: 'cdefine34',
        width: 130,
        align: 'center',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 8,
        },
        editRule: false,
      },
      {
        title: t('cdefine35'),
        dataIndex: 'cdefine35',
        width: 130,
        align: 'center',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 8,
        },
        editRule: false,
      },
      {
        title: t('cdefine36'),
        dataIndex: 'cdefine36',
        width: 215,
        align: 'center',
        editRow: true,
        editComponent: 'DatePicker',
        editComponentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
        editRule: false,
      },
      {
        title: t('cdefine37'),
        dataIndex: 'cdefine37',
        width: 215,
        align: 'center',
        editRow: true,
        editComponent: 'DatePicker',
        editComponentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
        editRule: false,
      },
      {
        title: t('数量'),
        dataIndex: 'iqty',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 28,
        },
        editRule: false,
      },
      {
        title: t('件数'),
        dataIndex: 'inum',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 28,
        },
        editRule: false,
      },
      {
        title: t('关闭人'),
        dataIndex: 'closeBy',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 20,
        },
        editRule: false,
      },
      {
        title: t('出库数'),
        dataIndex: 'outQty',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 28,
        },
        editRule: false,
      },
      {
        title: t('剩余出库'),
        dataIndex: 'syOutQty',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 29,
        },
        editRule: false,
      },
      {
        title: t('部门'),
        dataIndex: 'extDefine3',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 20,
        },
        editRule: false,
      },
    ]);
    matAppsTable.setTableData(record.value.matAppsList || []);
  }

  function handleMatAppsRowClick(record: Recordable) {
    record.onEdit?.(true, false);
  }

  function handleMatAppsAdd() {
    matAppsTable.insertTableDataRecord({
      id: new Date().getTime(),
      isNewRecord: true,
      editable: true,
    });
  }

  function handleMatAppsDelete(record: Recordable) {
    matAppsTable.deleteTableDataRecord(record);
  }

  async function getMatAppsList() {
    let matAppsListValid = true;
    let matAppsList: Recordable[] = [];
    for (const record of matAppsTable.getDataSource()) {
      if (!(await record.onEdit?.(false, true))) {
        matAppsListValid = false;
      }
      matAppsList.push({
        ...record,
        id: !!record.isNewRecord ? '' : record.id,
      });
    }
    for (const record of matAppsTable.getDelDataSource()) {
      if (!!record.isNewRecord) continue;
      matAppsList.push({
        ...record,
        status: '1',
      });
    }
    if (!matAppsListValid) {
      throw { errorFields: [{ name: ['matAppsList'] }] };
    }
    return matAppsList;
  }

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await matAppForm(data);
    record.value = (res.matApp || {}) as MatApp;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setMatAppsTableData(res);
    updateSchema([
      {
        field: 'id',
        componentProps: {
          disabled: !record.value.isNewRecord,
        },
      },
    ]);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      data.matAppsList = await getMatAppsList();
      // console.log('submit', params, data, record);
      const res = await matAppSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
