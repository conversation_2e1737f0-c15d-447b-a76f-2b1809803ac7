<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'layout:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherFieldsForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import {
    LayVoucherFields,
    layVoucherFieldsSave,
    layVoucherFieldsForm,
  } from '../../../../api/layout/vouch/layVoucherFields';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('layout.layVoucherFields');
  const { showMessage } = useMessage();
  const record = ref<LayVoucherFields>({} as LayVoucherFields);
  const getTitle = computed(() => ({
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增字段扩展') : t('编辑字段扩展'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('单据标志'),
      field: 'vouchCode',
      component: 'Input',
      dynamicDisabled: true,
      componentProps: {
        maxlength: 64,
      },
      required: true,
    },

    {
      label: t('字段名称'),
      field: 'physicalName',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      rules: [
        { required: true },
        { pattern: /^[a-zA-Z0-9_]*$/, message: t('请输入字母数字下划线') },
      ],
    },
    {
      label: t('显示名称'),
      field: 'logicalName',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
    },
    {
      label: t('字段类型'),
      field: 'type',
      component: 'Select',
      componentProps: {
        dictType: 'extend_column_type',
      },
      // componentProps: ({ formModel }) => {
      //   return {
      //     dictType: 'extend_column_type',
      //     onChange: (v) => {
      //       console.log(formModel['type']);
      //       console.log(formModel);
      //       // formModel['type'] = v.target.checked ? 1 : 0;
      //     },
      //   };
      // },
      required: true,
    },
    {
      label: t('字段长度'),
      field: 'length',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      ifShow: (obj) => {
        // console.log(obj,'=======');
        if(obj.values.type == 'VARCHAR' || obj.values.type == 'DECIMAL' ){
          return true;
        }else{
          return false;
        }
      },
      rules: [{required: true, pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('小数位数'),
      field: 'decimal',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      ifShow: (obj) => {
        // console.log(obj,'=======');
        if(obj.values.type == 'DECIMAL' ){
          return true;
        }else{
          return false;
        }
      },
      rules: [{required: true, pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('默认值'),
      field: 'defaultValue',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('是否插入字段'),
      field: 'isInsert',
      component: 'CheckboxGroup',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      // componentProps: ({ formModel }) => {
      //   return {
      //     options: [{ label: '', value: '1' }],
      //     onChange: (v) => {
      //       // console.log(v.target);
      //       // console.log(v);
      //       formModel['isInsert'] = v.target.checked ? 1 : 0;
      //     },
      //   };
      // },
      // rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否更新字段'),
      field: 'isUpdate',
      component: 'CheckboxGroup',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      // componentProps: ({ formModel }) => {
      //   return {
      //     options: [{ label: '', value: '1' }],
      //     onChange: (v) => {
      //       // console.log(v.target);
      //       // console.log(v);
      //       formModel['isUpdate'] = v.target.checked ? 1 : 0;
      //     },
      //   };
      // },
      // rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否强制更新'),
      field: 'isUpdateForce',
      component: 'CheckboxGroup',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      // componentProps: ({ formModel }) => {
      //   return {
      //     options: [{ label: '', value: '1' }],
      //     onChange: (v) => {
      //       // console.log(v.target);
      //       // console.log(v);
      //       formModel['isUpdateForce'] = v.target.checked ? 1 : 0;
      //     },
      //   };
      // },
      // rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否查询字段'),
      field: 'isQuery',
      component: 'CheckboxGroup',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      // componentProps: ({ formModel }) => {
      //   return {
      //     options: [{ label: '', value: '1' }],
      //     onChange: (v) => {
      //       // console.log(v.target);
      //       // console.log(v);
      //       formModel['isQuery'] = v.target.checked ? 1 : 0;
      //     },
      //   };
      // },
      // rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate, updateSchema }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await layVoucherFieldsForm(data);
    record.value = (res.layVoucherFields || {}) as LayVoucherFields;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    updateSchema([
      {
        field: 'physicalName',
        componentProps: {
          disabled: !record.value.isNewRecord,
        },
      },
      {
        field: 'logicalName',
        componentProps: {
          disabled: !record.value.isNewRecord,
        },
      },
    ]);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await layVoucherFieldsSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
