<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'billmanager:bankdirectlink:paydetail:billmanagerReconciliationMiddle:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
        <a-button type="success" ghost @click="handleSync({})" v-auth="'billmanager:bankdirectlink:paydetail:billmanagerReconciliationMiddle:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('同步') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.requestSn }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
    <BasicModal v-bind="$attrs" @register="registerModal" title="请选择对账时间" @ok="handleQr">
      <BasicForm @register="registerForm" />
    </BasicModal>
  </div>
</template>
<script lang="ts" setup name="ViewsBillmanagerBankdirectlinkPaydetailBillmanagerReconciliationMiddleList">
  import { unref, nextTick, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { billmanagerReconciliationMiddleDelete, billmanagerReconciliationMiddleListData, billmanagerReconciliationMiddleSyncDuiZhang } from '/@/api/billmanager/bankdirectlink/paydetail/billmanagerReconciliationMiddle';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { dateUtil } from '/@/utils/dateUtil';

  const { t } = useI18n('billmanager.bankdirectlink.paydetail.billmanagerReconciliationMiddle');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('对账单明细管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('对账起止日期'),
        field: 'dzBeginEndDate',
        component: 'Input',
      },
      {
        label: t('实际交易日期'),
        field: 'realTranDate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          showTime: false,
        },
      },
      {
        label: t('本方账号'),
        field: 'accNo1',
        component: 'Input',
      },
      {
        label: t('本方账号名称'),
        field: 'accName',
        component: 'Input',
      },
      {
        label: t('交易日期'),
        field: 'tranDate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          showTime: false,
        },
      },
      {
        label: t('借贷标志'),
        field: 'flag1',
        component: 'Select',
        componentProps: { dict_type: 'mf_jiedai' },
      },
      {
        label: t('对方账号'),
        field: 'accNo2',
        component: 'Input',
      },
      {
        label: t('对方户名'),
        field: 'accName1',
        component: 'Input',
      },
      {
        label: t('对方账户开户行名称'),
        field: 'cadBankNm',
        component: 'Input',
      },
      {
        label: t('交易流水号'),
        field: 'tranFlow',
        component: 'Input',
      },
      {
        label: t('U8单据号'),
        field: 'u8Djno',
        component: 'Input',
      },
      {
        label: t('推送状态'),
        field: 'pushState',
        component: 'Select',
        componentProps: { dict_type: 'mf_push_state' },
      },
      {
        label: t('推送时间'),
        field: 'pushDate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
        },
      },
      {
        label: t('推送结果'),
        field: 'pushResult',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('对账起止日期'),
      dataIndex: 'dzBeginEndDate',
      key: 'a.dz_begin_end_date',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('实际交易日期'),
      dataIndex: 'realTranDate',
      key: 'a.real_tran_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
        {
      title: t('交易日期'),
      dataIndex: 'tranDate',
      key: 'a.tran_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('交易时间'),
      dataIndex: 'tranTime',
      key: 'a.tran_time',
      sorter: true,
      width: 130,
      align: 'left',
    },
         {
      title: t('发生额'),
      dataIndex: 'amt',
      key: 'a.amt',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('余额'),
      dataIndex: 'amt1',
      key: 'a.amt1',
      sorter: true,
      width: 130,
      align: 'right',
    },
        {
      title: t('交易流水号'),
      dataIndex: 'tranFlow',
      key: 'a.tran_flow',
      sorter: true,
      width: 130,
      align: 'left',
    },
        {
      title: t('对方账号'),
      dataIndex: 'accNo2',
      key: 'a.acc_no2',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('对方户名'),
      dataIndex: 'accName1',
      key: 'a.acc_name1',
      sorter: true,
      width: 130,
      align: 'left',
    },
        {
      title: t('对方账户开户行名称'),
      dataIndex: 'cadBankNm',
      key: 'a.cad_bank_nm',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('本方账号'),
      dataIndex: 'accNo1',
      key: 'a.acc_no1',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('本方账号名称'),
      dataIndex: 'accName',
      key: 'a.acc_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('本方账号开户机构'),
      dataIndex: 'accOrgan',
      key: 'a.acc_organ',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('本方账号状态'),
      dataIndex: 'accState',
      key: 'a.acc_state',
      sorter: true,
      width: 130,
      align: 'left',
    },

    {
      title: t('借贷标志'),
      dataIndex: 'flag1',
      key: 'a.flag1',
      sorter: true,
      width: 130,
      align: 'left',
      dictType: 'mf_jiedai',
    },

    {
      title: t('利率'),
      dataIndex: 'intr',
      key: 'a.intr',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('币种'),
      dataIndex: 'currCod',
      key: 'a.curr_cod',
      sorter: true,
      width: 130,
      align: 'left',
      dictType: 'mf_bizhong',
    },
    {
      title: t('凭证种类'),
      dataIndex: 'creTyp',
      key: 'a.cre_typ',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('凭证号码'),
      dataIndex: 'creNo',
      key: 'a.cre_no',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('摘要'),
      dataIndex: 'message',
      key: 'a.message',
      sorter: true,
      width: 130,
      align: 'left',
    },
   
    {
      title: t('交易钞汇标志'),
      dataIndex: 'flag2',
      key: 'a.flag2',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('企业支付流水'),
      dataIndex: 'bflow',
      key: 'a.bflow',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('活存账户明细号'),
      dataIndex: 'detNo',
      key: 'a.det_no',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('备注'),
      dataIndex: 'det',
      key: 'a.det',
      sorter: true,
      width: 130,
      align: 'left',
    },

    {
      title: t('关联账号'),
      dataIndex: 'rltvAccNo',
      key: 'a.rltv_acc_no',
      sorter: true,
      width: 130,
      align: 'left',
    },

    {
      title: t('全局跟踪号'),
      dataIndex: 'ovrlsttnTrckno',
      key: 'a.ovrlsttn_trckno',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('外系统支付备注'),
      dataIndex: 'exoStmRyRmrk',
      key: 'a.exo_stm_ry_rmrk',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('U8单据号'),
      dataIndex: 'u8Djno',
      key: 'a.u8_djno',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('推送状态'),
      dataIndex: 'pushState',
      key: 'a.push_state',
      sorter: true,
      width: 130,
      align: 'left',
      dictType: 'mf_push_state',
    },
    {
      title: t('推送时间'),
      dataIndex: 'pushDate',
      key: 'a.push_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('推送结果'),
      dataIndex: 'pushResult',
      key: 'a.push_result',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑billmanager_reconciliation_middle'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'billmanager/bankdirectlink:paydetail:billmanagerReconciliationMiddle:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除billmanager_reconciliation_middle'),
        popConfirm: {
          title: t('是否确认删除billmanager_reconciliation_middle'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'billmanager/bankdirectlink:paydetail:billmanagerReconciliationMiddle:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload }] = useTable({
    api: billmanagerReconciliationMiddleListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    // actionColumn: actionColumn,  
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { id: record.id };
    const res = await billmanagerReconciliationMiddleDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
  let date = dateUtil(new Date()).subtract(1, 'day').format('YYYY-MM-DD');
  const [registerForm, { validate, setFieldsValue }] = useForm({
    labelWidth: 100,
    schemas: [
      {
        label: t('交易日期'),
        field: 'realTranDate',
        component: 'RangePicker',
        componentProps: {
          placeholder: [t('开始时间'), t('结束时间')],
        },
        defaultValue: [date, date],
        required: true,
      },
    ],
    baseColProps: { lg: 24, md: 24 },
    fieldMapToTime: [['realTranDate', ['realTranDate_gte', 'realTranDate_lte']]],
  });
  const [registerModal, { openModal, closeModal, setModalProps }] = useModal();

  // 防止重复点击的响应式标志和时间戳
  const isProcessing = ref(false);
  let lastClickTime = 0;

  async function handleQr() {
    const currentTime = Date.now();
    if (isProcessing.value === true || (currentTime - lastClickTime < 500)) {
      console.log('正在处理中或点击过快，忽略重复点击');
      return;
    }

    // 立即设置状态和时间戳
    isProcessing.value = true;
    lastClickTime = currentTime;

    try {
      let dataValidate = await validate();
      setModalProps({ confirmLoading: true });
      const data: any = {
        ...dataValidate,
      };
      const res = await billmanagerReconciliationMiddleSyncDuiZhang(data);
      showMessage(res.message);
      reload();
      await nextTick();
      closeModal();
      // setTableData3([]);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setModalProps({ confirmLoading: false });
      isProcessing.value = false;
      console.log('处理完成，重置 isProcessing 为 false');
    }
  }
  async function handleSync() {
    await openModal(true, {});
  }
</script>
