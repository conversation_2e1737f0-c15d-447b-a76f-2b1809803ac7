<template>
  <div>
    <div>
      <BasicTable @register="registerTable" @row-click="handleTestDataChildRowClick" @selection-change="handleSelectionChange">
        <!-- 表格标题 -->
        <template #tableTitle>
          <Icon :icon="getTitle.icon" class="m-1 pr-1" />
          <span> {{ getTitle.value }} </span>
        </template>
        <!-- 表格右上角自定义按钮（新增...） -->
        <template #toolbar>
          <div>
            <a-button
                type="primary"
                @click="btnPrint()"
                class="ml-2 mr-2"
              >
                <Icon icon="i-ant-design:import-outlined" /> {{ t('磅单导出') }}
              </a-button>
          </div>
        </template>
      </BasicTable>
      <InputForm @register="registerDrawer" @success="handleSuccess" />
    </div>
    <!-- 子表 -->
    <BasicModal
      v-bind="$attrs"
      :showFooter="true"
      :okAuth="'mf:dh:mfCarplanDhH:edit'"
      @register="registerModal"
      @ok="handleOverSubmit"
    >
      <template #title>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> 修改原因：  </span> {{ overData.djNo }}
      </template>
      <BasicForm @register="registerForm" />
    </BasicModal>
  </div>
</template>

<script lang="ts">
  export default defineComponent({
    name: 'ViewsWmsWeighbridgeDhHistoryList',
  });
</script>
<script lang="ts" setup>
  import { Popconfirm } from 'ant-design-vue';
  import { defineComponent, watch, ref, onMounted, unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { useGlobSetting } from '/@/hooks/setting';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import {
    mfCarplanDhHListData, mfCarplanDhCListData, mfCarplanDhHinvalid,
    updatePushStatusByHand, mfCarplanDhHDelete,  mfCarplanDhHupdateCstatus,
    mfCarplanDhCDelete, updateCzStatusByHand, mfCarplanDhCsaveChild, mfCarplanDhHformEdit ,listPushData,pushdata,CarplanDhDelete
    ,printForm
  } from '/@/api/wms/weighbridge/dh';

  import { mfCarplanFjCListData, mfCarplanFjHDeleteC } from '/@/api/wms/weighbridge/fj';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps,BasicForm, FormSchema, useForm } from '/@/components/Form';
  import InputForm from './../form2.vue';
  import { useModal } from '/@/components/Modal';
  import { downloadByUrl } from '/@/utils/file/download';
 

  const emit = defineEmits(['success']);

  const { t } = useI18n('test.testData');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: router.currentRoute.value.meta.title || t('废旧装车历史'),
  };
  const props = defineProps({
    data: { type: Object, default: {} },
  });
  watch(
    () => props.data,
    () => { },
    { immediate: true },
  );
  let overData = ref < any > ({});
  //定义totalWeight 初始值为0.0000
  const [registerModal, { openModal, closeModal,setModalProps }] = useModal();


  //配置表单内容
  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 120,
    schemas: [
      // {
      //   label: t('工艺类型'),
      //   field: 'routeType',
      //   component: 'Select',
      //   // defaultValue: props.data.flag,
      //   // dynamicDisabled: props.data.flag == undefined || props.data.flag == '' ? false : true,
      //   componentProps: {
      //     maxlength: 200,
      //     dictType: 'bas_route_type',
      //   },
      // },

      {
        label: t('车次号'),
        field: 'parent.djNo',
        component: 'Input',
      },
      {
        label: t('车牌号'),
        field: 'parent.carNo',
        component: 'Input',
      },
      {
        label: t('计划发货日期'),
        field: 'planDate',
        component: 'RangePicker',
        // component: 'DatePicker',
        componentProps: {
          // format: 'YYYY-MM-DD HH:mm',
          // valueFormat: 'YYYY-MM-DD HH:mm',
          // showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('司机'),
        field: 'parent.cdriver',
        component: 'Input',
      },
      {
        label: t('司机电话'),
        field: 'parent.driverPhone',
        component: 'Input',
      },
      {
        label: t('客户'),
        field: 'cusName',
        component: 'Input',
      },
      {
        label: t('合同号'),
        field: 'htNo',
        component: 'Input',
      },
      {
        label: t('存货'),
        field: 'invName',
        component: 'Input',
      },
      {
        label: t('状态'),
        field: 'parent.cstatus',
        component: 'Select',
        componentProps: {
          dictType: 'mf_plan_status',
          allowClear: true,
        },
      },
    ],
    fieldMapToTime: [['planDate', ['planDate_gte', 'planDate_lte']]],
  };

  //配置表格表头菜单
  const tableColumns: BasicColumn[] = [
    {
      title: t('车次号'),
      dataIndex: 'parent.djNo',
      key: 'parent.dj_no',
      sorter: true,
      width: 100,
      fixed: 'left',
    },
    {
      title: t('车牌号'),
      dataIndex: 'parent.carNo',
      key: 'parent.car_no',
      sorter: true,
      width: 90,
      align: 'center',
    },
    {
      title: t('状态'),
      dataIndex: 'parent.cstatus',
      key: 'parent.cstatus',
      sorter: true,
      width: 80,
      dictType: 'mf_plan_status',
    },
    {
      title: t('计划发货日期'),
      dataIndex: 'parent.planDate',
      key: 'parent.plan_date',
      sorter: true,
      width: 150,
      align: 'center',
    },
    {
      title: t('皮重'),
      dataIndex: 'parent.pzWeight',
      key: 'parent.pz_weight',
      sorter: true,
      width: 80,
      align: 'right',
    },
    {
      title: t('毛重'),
      dataIndex: 'parent.mzWeight',
      key: 'parent.mz_weight',
      sorter: true,
      width: 80,
      align: 'right',
    },
    {
      title: t('净重'),
      dataIndex: 'parent.jzWeight',
      key: 'parent.jz_weight',
      sorter: true,
      width: 80,
      align: 'right',
    },
    {
      title: t('客户'),
      dataIndex: 'cusName',
      key: 'a.cus_name',
      sorter: true,
      width: 200,
      align: 'left',
    },
    {
      title: t('合同号'),
      dataIndex: 'htNo',
      key: 'a.ht_no',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('存货'),
      dataIndex: 'invName',
      key: 'a.inv_name',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('规格型号'),
      dataIndex: 'invStd',
      key: 'a.inv_std',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('单位'),
      dataIndex: 'invUnit',
      key: 'a.inv_unit',
      sorter: true,
      width: 80,
      align: 'left',
    },
    {
      title: t('司机'),
      dataIndex: 'parent.cdriver',
      key: 'parent.cdriver',
      sorter: true,
      width: 80,
      align: 'left',
    },
    {
      title: t('司机电话'),
      dataIndex: 'parent.driverPhone',
      key: 'parent.driver_phone',
      sorter: true,
      width: 110,
      align: 'left',
    },
    {
      title: t('押运员'),
      dataIndex: 'parent.yyPerson',
      key: 'parent.yy_person',
      sorter: true,
      width: 80,
      align: 'left',
    },
    {
      title: t('押运员电话'),
      dataIndex: 'parent.yyPhone',
      key: 'parent.yy_phone',
      sorter: true,
      width: 110,
      align: 'left',
    },
    {
      title: t('运输单位'),
      dataIndex: 'parent.carVenName',
      key: 'parent.car_venName',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('毛重完成时间'),
      dataIndex: 'parent.wcDate',
      key: 'parent.wc_date',
      sorter: true,
      width: 150,
      align: 'center',
    },
    {
      title: t('皮重完成时间'),
      dataIndex: 'parent.pzDate',
      key: 'parent.pz_date',
      sorter: true,
      width: 150,
      align: 'center',
    },
    {
      title: t('承运目的地'),
      dataIndex: 'cyAddress',
      key: 'a.cy_address',
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: t('承运单价'),
      dataIndex: 'cyPrice',
      key: 'a.cy_price',
      sorter: true,
      width: 150,
      // align: 'center',
    },
    {
      title: t('备注'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: t('打印次数'),
      dataIndex: 'parent.prtCount',
      key: 'parent.prt_count',
      sorter: true,
      width: 100,
      align: 'center',
      fixed: 'right',
    },
  ];


  //配置表格右边操作按钮
  const actionColumn: BasicColumn = {
    width: 120,
    align: 'left',
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'mf:fj:mfCarplanFjH:edit',
        ifShow: () => record.cstatus != '1',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除数据'),
        popConfirm: {
          title: t('是否确认删除数据'),
          confirm: handleDelete.bind(this, { id: record.id }),
        },
        auth: 'mf:fj:mfCarplanFjH:edit',
        ifShow: () => record.cstatus != '1',
      },
    ],
  };

  const inputFormSchemas: FormSchema[] = [
    {
      label: t(''),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 500,
        rows:5
      },
      colProps: { lg: 24, md: 24 },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    // schemas: inputFormSchemas,
    baseColProps: { lg: 24, md: 24 },
  });

  const selectedRowKeysRef = ref<string[]>([]);
  const [registerDrawer, { openDrawer }] = useDrawer();

  const [registerTable, { reload, setProps, getSelectRows, getDataSource ,getForm }] = useTable({
    api: mfCarplanFjCListData,
    beforeFetch: (params) => {
      return params;
    },
    afterFetch: (params) => {
      console.log(params,'params=====');
      return params;
    },
    columns: tableColumns, //配置表格内容数组对象
    formConfig: searchForm, //配置表单内容
    showTableSetting: true, //表格右上角3个默认按钮
    useSearchForm: true, //表单是否展示
    canResize: true, //表格是否flex布局
    // scroll: {
    //   y: 250,
    // },
    rowSelection: {
      type: 'checkbox',
    },
    resizeHeightOffset: 80,
    defaultRowSelection: {
      onChange: (selectedRowKeys: string[], _selectedRows: Recordable[]) => {
        selectedRowKeysRef.value = selectedRowKeys;
      },
    },
    showSummary: true,
    // summaryFunc: handleSummary,
  });

  function handleSummary(tableData: Recordable[]) {
    const totalqrWeight = tableData.reduce((prev, next) => {
      prev += next.qrWeight ? next.qrWeight : 0;
      return prev;
    }, 0);
    const totalPlanWeight = tableData.reduce((prev, next) => {
      prev += next.planWeight ? next.planWeight : 0;
      return prev;
    }, 0);
    return [
      {
        _row: '合计',
        qrWeight: totalqrWeight.toFixed(2),
        planWeight: totalPlanWeight.toFixed(2),
      },
    ];
  }

  onMounted(() => {
    if (!props.data.picno) {
      setProps({
        actionColumn: actionColumn,
      });
    }
  });


  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }
  

  // 鼠标行点击事件，获取上表数据
  function handleTestDataChildRowClick(record: Recordable) {
    // totalWeight.value = record.planWeight;
    // 把选中的数据里面的确认发货重量全部累加给totalWeight
    // for (const record of xqtable3.getDataSource()) {
    //   totalWeight.value += Number(record.planWeight);
    // }
  }

  async function handleOverSubmit(record: Recordable) {
    try {
      const data = await validate();
      data.id = overData.value.id
      data.djNo = overData.value.djNo
      data.cstatus = '2'
      setModalProps({ confirmLoading: true });
      const res = await updateCzStatusByHand(data);
      selectedRowKeysRef.value = [];
      showMessage(res.message);
      handleSuccess();
      closeModal();
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  async function handleDelete(record: Recordable) {
    const res = await mfCarplanFjHDeleteC(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleSuccess() {
    emit('success');
    reload();
  }

  function handleSelectionChange({ keys , rows}) {
    selectedRowKeysRef.value = keys;
    // 统计累加rows 数组的每一项的qrWeight ，totalWeight.value 小数点后保留4位
    // totalWeight.value = rows.reduce((pre, cur) => pre + Number(cur.qrWeight || 0), 0).toFixed(4);
    // for (const record of rows) { 
    //   totalWeight.value += Number(record.qrWeight);
    //   console.log(totalWeight.value,'totalWeight.value====',record.qrWeight)
    // }
  }
  async function btnPrint() {
    // const res = await printForm(data);
    // showMessage(res.message);
    const { ctxAdminPath } = useGlobSetting();
    downloadByUrl({
      url: ctxAdminPath + '/mf/fj/mfCarplanFjH/printForm',
      params: getForm().getFieldsValue(),
    });
  }
</script>
