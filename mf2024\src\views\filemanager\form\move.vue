<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicModal
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'filemanager:filemanagerFolder:edit'"
    @register="registerModal"
    @ok="handleSubmit"
    :min-height="50"
    width="600"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { filemanagerFolderTreeData } from '/@/api/filemanager/filemanagerFolder';
  import {
    Filemanager,
    filemanagerMoveForm,
    filemanagerMove,
  } from '/@/api/filemanager/filemanager';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('filemanager.filemanager');
  const { showMessage } = useMessage();
  const record = ref<Filemanager>({} as Filemanager);
  const getTitle = computed(() => ({
    icon: 'i-ant-design:swap-outlined',
    value: t('移动文件'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('目标文件夹'),
      field: 'folderId',
      fieldLabel: 'fileName',
      component: 'TreeSelect',
      componentProps: {
        allowClear: true,
        placeholder: t('默认为根目录'),
      },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 23, md: 23 },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ loading: true });
    await resetFields();
    const res = await filemanagerMoveForm(data);
    record.value = (res.filemanager || {}) as Filemanager;
    if (data.groupType) {
      record.value.groupType = data.groupType;
    }
    if (record.value.folderId == '0') {
      record.value.folderId = '';
    }
    setFieldsValue(record.value);
    updateSchema([
      {
        field: 'folderId',
        componentProps: {
          api: filemanagerFolderTreeData,
          params: {
            excludeCode: res.excludeCode,
            isShowRawName: true,
          },
        },
      },
    ]);
    setModalProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setModalProps({ confirmLoading: true });
      data.groupType = record.value.groupType;
      data.ids = record.value.ids;
      // console.log('submit', params, data, record);
      const res = await filemanagerMove({}, data);
      showMessage(res.message);
      closeModal();
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
