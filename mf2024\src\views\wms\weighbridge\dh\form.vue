<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'mf:dh:mfCarplanDhH:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm"  @keyup.enter.native="handleEnterKey"/>
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsWmsWeighbridgeForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { carVenTreeData } from '/@/api/wms/weighbridge/carVen';
  import { CarplanDh, mfCarplanDhHForm, mfCarplanDhHSave, getCarInfoByCarNo } from '/@/api/wms/weighbridge/dh';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('sys.dh');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<CarplanDh>({} as CarplanDh);
  const getTitle = computed(() => ({
    icon: meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增采购入库装车计划') : t('编辑采购入库装车计划'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('ID'),
      field: 'id',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
      ifShow: false,
    },
    {
      label: t('基本信息'),
      field: 'info1',
      component: 'FormGroup',
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('到货车次'),
      field: 'djNo',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      dynamicDisabled: true,
    },
    {
      label: t('车牌号'),
      field: 'carNo',
      component: 'Input',
      componentProps: {
        maxlength: 10,
        onChange:async(e)=>{
          let obj = await getFieldsValue()
          if(!obj.cdriver && !obj.driverPhone){
            let res = await getCarInfoByCarNo({carNo:obj.carNo})
            setFieldsValue({
                  ...obj,
                  cdriver:res.data?.cdriver || '',
                  driverPhone:res.data?.driverPhone || '',
            })
          }
        }
      },
      required: true,
    },
    {
      label: t('计划到货日期'),
      field: 'planDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
      required: true,
    },
    {
      label: t('类型'),
      field: 'carType',
      component: 'RadioButtonGroup',
      componentProps: {
        dictType: 'mf_carplan_type',
      },
      defaultValue: '0',
      required: true,
    },
    {
      label: t('U8入库单号'),
      field: 'u8Code',
      component: 'Input',
      componentProps: {
        maxlength: -1,
      },
      dynamicDisabled: true,
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('运输信息'),
      field: 'info2',
      component: 'FormGroup',
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('司机'),
      field: 'cdriver',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('司机电话'),
      field: 'driverPhone',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('运输单位'),
      field: 'carVenCode',
      fieldLabel: 'carVenName',
      component: 'ListSelect',
      componentProps: {
        selectType: 'carVenListSelect',
        checkbox: true,
      },
      required: true,
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('到货说明'),
      field: 'info3',
      component: 'FormGroup',
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('备注信息'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 500,
        rows: 2,
      },
      colProps: { lg: 24, md: 24 },
    },

    {
      label: t('称重信息'),
      field: 'info4',
      component: 'FormGroup',
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('皮重'),
      field: 'pzWeight',
      component: 'InputNumber',
      componentProps: {
        maxlength: 100,
      },
      dynamicDisabled: true,
    },
    {
      label: t('毛重'),
      field: 'mzWeight',
      component: 'InputNumber',
      componentProps: {
        maxlength: 100,
      },
      dynamicDisabled: true,
    },
    {
      label: t('净重'),
      field: 'jzWeight',
      component: 'InputNumber',
      componentProps: {
        maxlength: 100,
      },
      dynamicDisabled: true,
    },
    {
      label: t('称重完成(毛重)'),
      field: 'wcDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
      dynamicDisabled: true,
    },
    {
      label: t('称重完成(皮重)'),
      field: 'pzDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
      dynamicDisabled: true,
    },
    {
      label: t('附件信息'),
      field: 'info5',
      component: 'FormGroup',
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t(''),
      field: 'dataMap',
      component: 'Upload',
      componentProps: {
        loadTime: computed(() => record.value.__t),
        bizKey: computed(() => record.value.id),
        bizType: 'carplanDhH_image',
        uploadType: 'image',
        imageMaxWidth: -1,
        imageMaxHeight: -1,
      },
      colProps: { lg: 24, md: 24 },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, updateSchema, validate, getFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    autoSubmitOnEnter: true,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await mfCarplanDhHForm(data);
    record.value = (res.mfCarplanDhH || {}) as CarplanDh;
    setFieldsValue(record.value);
    setDrawerProps({ loading: false ,showOkBtn: record.value.cstatus != '3' && record.value.cstatus != '4' ? true: false});
    
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      const res = await mfCarplanDhHSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }

  function handleEnterKey() {
    handleSubmit();
  }
</script>
