{"name": "jeesite", "version": "5.8.1", "type": "module", "scripts": {"bootstrap": "yarn install", "serve": "npm run dev", "dev": "cross-env VITE_CJS_TRACE=true vite dev", "build": "vite build --mode production", "build:tomcat": "vite build --mode tomcat --emptyOutDir", "build:preview": "npm run build && npm run preview:dist", "build:no-cache": "npm run clean:cache && npm run build", "preview": "npm run build && npm run preview:dist", "preview:dist": "vite preview --mode development --port 3100", "clean:cache": "rimraf node_modules/.cache/; rimraf node_modules/.vite", "report": "cross-env REPORT=true npm run build", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write \"src/**/*.{json,tsx,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint \"**/*.{vue,css,less,scss}\" --fix --cache --cache-location node_modules/.cache/stylelint/", "lint:all": "npm run type:check && npm run lint:eslint && npm run lint:prettier && npm run lint:stylelint", "reinstall": "rimraf yarn.lock pnpm-lock.yaml package-lock.json node_modules; npm run bootstrap", "update": "npm i npm-check-updates -g --force; ncu -u -x codemirror", "gen:icon": "esno ./build/generate/icon/index.ts"}, "dependencies": {"@ant-design/colors": "7.0.2", "@ant-design/icons-vue": "7.0.1", "@logicflow/core": "1.2.27", "@logicflow/extension": "1.2.27", "@vue/runtime-core": "3.4.30", "@vue/shared": "3.4.30", "@vueuse/core": "10.11.0", "@vueuse/shared": "10.11.0", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "5.1.12", "@zxcvbn-ts/core": "3.0.4", "ant-design-vue": "4.2.3", "axios": "1.7.2", "big.js": "^6.2.2", "codemirror": "5.65.16", "cropperjs": "1.6.2", "crypto-js": "4.2.0", "dayjs": "1.11.11", "echarts": "5.5.1", "intro.js": "7.2.0", "lodash-es": "4.17.21", "nprogress": "0.2.0", "path-to-regexp": "7.0.0", "pinia": "2.1.7", "print-js": "1.6.0", "qrcode": "1.5.3", "qs": "6.12.1", "resize-observer-polyfill": "1.5.1", "showdown": "2.1.0", "sortablejs": "1.15.2", "spark-md5": "3.0.2", "terser": "5.31.1", "vditor": "3.10.4", "vue": "3.4.30", "vue-i18n": "9.13.1", "vue-json-pretty": "2.4.0", "vue-router": "4.4.0", "vue-types": "5.1.2", "vuedraggable": "^4.1.0", "xlsx": "0.18.5"}, "devDependencies": {"@iconify/iconify": "3.1.1", "@iconify/json": "2.2.223", "@iconify/utils": "2.1.25", "@types/codemirror": "5.60.15", "@types/crypto-js": "4.2.2", "@types/fs-extra": "11.0.4", "@types/inquirer": "9.0.7", "@types/intro.js": "5.1.5", "@types/lodash-es": "4.17.12", "@types/node": "20.14.9", "@types/nprogress": "0.2.3", "@types/qrcode": "1.5.5", "@types/qs": "6.9.15", "@types/showdown": "2.0.6", "@types/sortablejs": "1.15.8", "@typescript-eslint/eslint-plugin": "7.14.1", "@typescript-eslint/parser": "7.14.1", "@unocss/eslint-config": "0.61.0", "@vitejs/plugin-legacy": "5.4.1", "@vitejs/plugin-vue": "5.0.5", "@vitejs/plugin-vue-jsx": "4.0.0", "@vue/compiler-sfc": "3.4.30", "@vue/test-utils": "2.4.6", "autoprefixer": "10.4.19", "cross-env": "7.0.3", "cz-git": "1.9.3", "czg": "1.9.3", "dotenv": "16.4.5", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-vue": "9.26.0", "esno": "4.7.0", "fs-extra": "11.2.0", "inquirer": "9.3.1", "less": "4.2.0", "lint-staged": "15.2.7", "npm-run-all": "4.1.5", "picocolors": "1.0.1", "pkg-types": "1.1.1", "postcss": "8.4.38", "postcss-html": "1.7.0", "postcss-less": "6.0.0", "prettier": "3.3.2", "prettier-plugin-packagejson": "2.5.0", "rimraf": "5.0.7", "rollup": "4.18.0", "rollup-plugin-visualizer": "5.12.0", "stylelint": "16.6.1", "stylelint-config-recommended-less": "3.0.1", "stylelint-config-recommended-vue": "1.5.0", "stylelint-config-standard": "36.0.1", "stylelint-config-standard-less": "3.0.1", "stylelint-order": "6.0.4", "stylelint-prettier": "5.0.0", "ts-node": "10.9.2", "typescript": "5.5.2", "unocss": "0.61.0", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-html": "3.2.2", "vite-plugin-mkcert": "1.17.5", "vite-plugin-theme-vite3": "1.0.5", "vite-plugin-vue-setup-extend": "0.4.0", "vue-eslint-parser": "9.4.3", "vue-tsc": "2.0.22"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china"}, "repository": {"type": "git", "url": "git+https://gitee.com/thinkgem/jeesite-vue.git"}, "bugs": {"url": "https://gitee.com/thinkgem/jeesite-vue/issues"}, "homepage": "https://jeesite.com", "engines": {"node": "18 || >=20"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,less,styl,html}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "author": {"name": "ThinkGem", "email": "<EMAIL>", "url": "https://gitee.com/thinkgem/jeesite-vue"}}