<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'bas:inv:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <Tabs v-model:activeKey="activeKey" tabPosition="left">
      <Tabs.TabPane
        v-for="(item, index) in formTabs"
        :key="index"
        :forceRender="true"
        :tab="item.tabName"
      >
        <SingleForm
          v-if="flag && !item.tabAddress"
          :schemas="item.colList"
          :formConfig="formConfig"
          :data="record"
          ref="singleFormArr"
        />
      </Tabs.TabPane>
    </Tabs>
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsBasInvBasInvForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasInv, basInvSave, basInvForm } from '/@/api/bas/inv/basInv';
  import { Tabs } from 'ant-design-vue';
  import { formSet } from '/@/api/test/testData';
  import SingleForm from './singleForm.vue';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('bas.inv.basInv');
  const { showMessage } = useMessage();
  const record = ref<BasInv>({} as BasInv);
  const getTitle = computed(() => ({
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增产品档案') : t('编辑产品档案'),
  }));

  let formTabs = ref<any>([]);
  let formConfig = ref<any>({});
  let flag = ref(false);
  const activeKey = ref<number>(0);
  let singleFormArr = ref<any>(null);

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    flag.value = false;
    activeKey.value = data.activeKey ? data.activeKey : 0;
    setDrawerProps({ loading: true });

    const res11 = await formSet({ viewCode: 'bas_inv_form' });

    formTabs.value = res11.layVoucherView.formTabs;
    formConfig.value = res11.layVoucherView.formConfig;

    const res = await basInvForm(data);
    record.value = (res.basInv || {}) as BasInv;
    record.value.__t = new Date().getTime();
    flag.value = true;

    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    let obj = {};
    let falg = false;
    await singleFormArr.value.forEach((item) => {
      item.validateForm().then((e) => {
        if (!e) {
          falg = true;
        } else {
          obj = { ...obj, ...e };
        }
      });
    });
    setTimeout(async () => {
      if (falg) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      } else {
        try {
          setDrawerProps({ confirmLoading: true });
          const params: any = {
            isNewRecord: record.value.isNewRecord,
            invCode: record.value.invCode,
          };
          const res = await basInvSave(params, obj);
          showMessage(res.message);
          setDrawerProps({ confirmLoading: false });
          setTimeout(closeDrawer);
          emit('success', obj);
        } catch (error: any) {
          if (error && error.errorFields) {
            showMessage('您填写的信息有误，请根据提示修正。');
          }
          console.log('error', error);
        } finally {
          setDrawerProps({ confirmLoading: false });
        }
      }
    }, 0);
  }
</script>
