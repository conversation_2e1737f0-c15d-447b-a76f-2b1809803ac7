import { defineStore } from 'pinia';

import { TABLE_SETTINGS_KEY } from '/@/enums/cacheEnum';
import { Persistent } from '/@/utils/cache/persistent';

interface FormSettingsState {
  tableSettings: Nullable<any>;
}

export const useTableSettingsStore = defineStore({
  id: 'table-settings',
  state: (): FormSettingsState => ({
    tableSettings: Persistent.getLocal(TABLE_SETTINGS_KEY) || {},
  }),
  actions: {
    getTableColumnsSettings(key: string, defaultValue: any = undefined) {
      return this.getTableSettings(key, 'columns', defaultValue);
    },
    setTableColumnsSettings(key: string, value: any) {
      this.setTableSettings(key, 'columns', value);
    },
    getTableSettings(key: string, key2: string, defaultValue: any) {
      if (!key || !key2) return defaultValue;
      if (!this.tableSettings[key]) this.tableSettings[key] = {};
      return this.tableSettings[key][key2] || defaultValue;
    },
    setTableSettings(key: string, key2: string, value: any) {
      if (!key || !key2) return;
      if (!this.tableSettings[key]) this.tableSettings[key] = {};
      this.tableSettings[key][key2] = value;
      Persistent.setLocal(TABLE_SETTINGS_KEY, this.tableSettings, true);
    },
  },
});
