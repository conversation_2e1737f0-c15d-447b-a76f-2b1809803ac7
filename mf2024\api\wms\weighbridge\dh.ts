/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

import { UploadApiResult } from './upload';
import { UploadFileParams } from '/#/axios';
import { AxiosProgressEvent } from 'axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface CarplanDh extends BasicModel<CarplanDh> {}

export const mfCarplanDhHListData = (params?: CarplanDh | any) =>
  defHttp.post<Page<CarplanDh>>({ url: adminPath + '/mf/dh/mfCarplanDhH/listData', params });

export const mfCarplanDhCListData = (params?: CarplanDh | any) =>
  defHttp.post<Page<CarplanDh>>({ url: adminPath + '/mf/dh/mfCarplanDhC/listData', params });

export const mfCarplanDhHinvalid = (params?: CarplanDh | any) =>
  defHttp.post<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhH/invalid', params });

export const updatePushStatusByHand = (params?: CarplanDh | any) =>
  defHttp.post<CarplanDh>({
    url: adminPath + '/mf/dh/mfCarplanDhH/updatePushStatusByHand',
    params,
  });

export const mfCarplanDhHDelete = (params?: CarplanDh | any) =>
  defHttp.get<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhH/delete', params });

export const mfCarplanDhHupdateCstatus = (params?: CarplanDh | any) =>
  defHttp.get<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhH/updateCstatus', params });

export const mfCarplanDhCDelete = (params?: CarplanDh | any) =>
  defHttp.get<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhC/delete', params });

export const updateCzStatusByHand = (params?: CarplanDh | any) =>
  defHttp.post<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhH/updateCzStatusByHand', params });

export const mfCarplanDhHimportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: AxiosProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/mf/dh/mfCarplanDhH/importData',
      onUploadProgress,
    },
    params,
  );

export const mfCarplanDhHForm = (params?: CarplanDh | any) =>
  defHttp.get<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhH/form', params });

export const mfCarplanDhHSave = (params?: any, data?: CarplanDh | any) =>
  defHttp.postJson<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhH/save', params, data });

export const poPodetailsListData = (params?: CarplanDh | any) =>
  defHttp.post<Page<CarplanDh>>({ url: adminPath + '/mf/dh/poPodetails/listData', params });

export const mfCarplanDhCForm = (params?: CarplanDh | any) =>
  defHttp.get<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhC/form', params });

export const mfCarplanDhCSave = (params?: any, data?: CarplanDh | any) =>
  defHttp.postJson<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhC/save', params, data });

export const mfCarplanDhCsaveChild = (params?: CarplanDh | any) =>
  defHttp.postJson<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhC/saveChild', params });

export const mfCarplanDhHformEdit = (params?: CarplanDh | any) =>
  defHttp.post<Page<CarplanDh>>({ url: adminPath + '/mf/dh/mfCarplanDhH/formEdit', params });

export const listPushData = (params?: CarplanDh | any) =>
  defHttp.post<Page<CarplanDh>>({ url: adminPath + '/mf/dh/mfCarplanDhH/listPushData', params });
export const pushdata = (params?: CarplanDh | any) =>
  defHttp.post<Page<CarplanDh>>({ url: adminPath + '/mf/dh/mfCarplanDhH/pushdata', params });

// export const CarplanDhForm = (params?: CarplanDh | any) =>
//   defHttp.get<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhC/form', params });

// export const CarplanDhSave = (params?: any, data?: CarplanDh | any) =>
//   defHttp.postJson<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhC/save', params, data });

// export const CarplanDhDisable = (params?: CarplanDh | any) =>
//   defHttp.get<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhC/disable', params });

// export const CarplanDhEnable = (params?: CarplanDh | any) =>
//   defHttp.get<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhC/enable', params });

export const CarplanDhDelete = (params?: CarplanDh | any) =>
  defHttp.get<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhC/delete', params });

// /mf/dh/mfCarplanDhC/printForm
export const printForm = (params?: CarplanDh | any) =>
  defHttp.postJson<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhC/printForm', params });

export const getCarInfoByCarNo = (params?: CarplanDh | any) =>
  defHttp.get<CarplanDh>({ url: adminPath + '/mf/dh/mfCarplanDhH/getCarInfoByCarNo', params });