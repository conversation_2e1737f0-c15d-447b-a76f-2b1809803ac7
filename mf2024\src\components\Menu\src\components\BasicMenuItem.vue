<template>
  <MenuItem :key="item.path" v-bind="getMenuItem" @contextmenu.prevent="handleContext($event, item)">
    <MenuItemContent v-bind="$props" :item="item" />
  </MenuItem>
</template>
<script lang="ts">
  import { computed, defineComponent } from 'vue';
  import { Menu } from 'ant-design-vue';
  import { itemProps } from '../props';
  import { omit } from 'lodash-es';
  import MenuItemContent from './MenuItemContent.vue';

  import { quickNavSave, findUserNav } from '/@/api/sys/quickNav';
  import { usePermissionStore } from '/@/store/modules/permission';
  import { useContextMenu } from '/@/hooks/web/useContextMenu';
  import { useMessage } from '/@/hooks/web/useMessage';
  const useStore = usePermissionStore();

  export default defineComponent({
    name: 'BasicMenuItem',
    components: { MenuItem: Menu.Item, MenuItemContent },
    props: itemProps,
    setup(props) {
      const getMenuItem = computed(() => {
        return omit(props.item, 'children', 'icon', 'title', 'color', 'extend');
      });

      // 鼠标右键点击事件start
      const [createContextMenu] = useContextMenu();
      const { createMessage } = useMessage();
      function handleContext(e: MouseEvent, item: Menu) {
        console.log(item.children?.length === 0, 'handleContext', e, props.item);
        let newItemName = props.item?.children?.filter((item: any) => {
          return item.name === e.srcElement.innerText;
        });
        console.log(newItemName,'newItemName')

        // 如果递归调用
        if (newItemName.length !== 0 && newItemName[0]?.children.length == 0) {
          createContextMenu({
            event: e,
            items: [
              {
                // label: '添加' + e.srcElement.innerText,
                label: '添加至快捷导航',
                icon: 'ant-design:plus-outlined',
                handler: () => {
                  // 从item.children里面过滤出e.srcElement.innerText
                  let newItem = props.item.children.filter((item: any) => {
                    return item.name === e.srcElement.innerText;
                  });
                  quickNavSave({
                    menuId: newItem[0].id,
                  }).then(async (res: any) => {
                    if (res.result == 'true') {
                      createMessage.success('添加' + e.srcElement.innerText + '成功');
                      const res = await findUserNav();
                      await useStore.setQuickNavList(res.list);
                    } else {
                      createMessage.error(res.message);
                    }
                  });
                },
              },
            ],
          });
        } else if (item.children?.length === 0) {
          createContextMenu({
            event: e,
            items: [
              {
                label: '添加' + e.srcElement.innerText,
                icon: 'ant-design:plus-outlined',
                handler: () => {
                  quickNavSave({
                    menuId: props.item.id,
                  }).then(async (res: any) => {
                    if (res.result == 'true') {
                      createMessage.success('添加' + e.srcElement.innerText + '成功');
                      const res = await findUserNav();
                      await useStore.setQuickNavList(res.list);
                    } else {
                      createMessage.error(res.message);
                    }
                  });
                },
              },
            ],
          });
        }
      }

      return { 
        getMenuItem,
        handleContext,
      };
    },
  });
</script>
