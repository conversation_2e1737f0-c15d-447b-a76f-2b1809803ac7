<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'layout:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherTabConfigForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { LayVoucherTabConfig, layVoucherTabConfigSave, layVoucherTabConfigForm } from '../../../../api/layout/list/layVoucherTabConfig';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('layout.layVoucherTabConfig');
  const { showMessage } = useMessage();
  const record = ref<LayVoucherTabConfig>({} as LayVoucherTabConfig);
  const getTitle = computed(() => ({
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增表格全局配置') : t('编辑表格全局配置'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('布局标志'),
      field: 'viewCode',
      component: 'Input',
      dynamicDisabled: true,
      componentProps: {
        maxlength: 200,
      },
      required: true,
    },
    {
      label: t('点击是否选中行'),
      field: 'clickToRowSelect',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否显示表格设置工具'),
      field: 'showTableSetting',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否显示表格设置工具配置'),
      field: 'tableSetting',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
    },
    {
      label: t('是否显示斑马纹'),
      field: 'striped',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否取消表格默认padding'),
      field: 'inset',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否自动生成key'),
      field: 'autoCreateKey',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否显示合计行'),
      field: 'showSummary',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否显示表格'),
      field: 'emptyDataIsShowTable',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否树表'),
      field: 'isTreeTable',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否延迟加载表格数据'),
      field: 'immediate',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否启用搜索表单'),
      field: 'useSearchForm',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否显示行号'),
      field: 'showIndexColumn',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('文本超过宽度是否显示省略号'),
      field: 'ellipsis',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否自适应高度'),
      field: 'canResize',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('切换页面是否重置勾选状态'),
      field: 'clearSelectOnPageChange',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否显示表格边框'),
      field: 'bordered',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否显示表格loading状态'),
      field: 'loading',
      component: 'Checkbox',
      componentProps: {
        // dictType: 'sys_yes_no',
        options: [{ label: '', value: '1' }],
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('请求接口'),
      field: 'api',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('表格标题'),
      field: 'title',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('表格右侧温馨提示'),
      field: 'titleHelpMessage',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('表格最大高度'),
      field: 'maxHeight',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('自适应高度'),
      field: 'resizeHeightOffset',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 190,
    schemas: inputFormSchemas,
    baseColProps: { lg: 8, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await layVoucherTabConfigForm(data);
    record.value = (res.layVoucherTabConfig || {}) as LayVoucherTabConfig;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    // updateSchema([
    //   {
    //     field: 'viewCode',
    //     componentProps: {
    //       disabled: !record.value.isNewRecord,
    //     },
    //   },
    // ]);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await layVoucherTabConfigSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
