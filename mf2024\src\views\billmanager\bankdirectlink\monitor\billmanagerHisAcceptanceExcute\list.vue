<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'billmanager/bankdirectlink:monitor:billmanagerHisAcceptanceExcute:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.beginDate }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsBillmanagerBankdirectlinkMonitorBillmanagerHisAcceptanceExcuteList">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { billmanagerHisAcceptanceExcuteDelete, billmanagerHisAcceptanceExcuteListData } from '/@/api/billmanager/bankdirectlink/monitor/billmanagerHisAcceptanceExcute';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('billmanager.bankdirectlink.monitor.billmanagerHisAcceptanceExcute');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('billmanager_his_acceptance_excute管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('时间'),
        field: 'beginDate',
        component: 'RangePicker',
        componentProps: {
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          showTime: false,
        },
      },
      // {
      //   label: t('结束时间'),
      //   field: 'endDate',
      //   component: 'DatePicker',
      //   componentProps: {
      //     format: 'YYYY-MM-DD',
      //     valueFormat: 'YYYY-MM-DD',
      //     showTime: false,
      //     // showTime: { format: 'HH:mm' },
      //   },
      // },
      {
        label: t('同步结果描述'),
        field: 'remarks',
        component: 'Input',
      },
      {
        label: t('同步用户信息'),
        field: 'createByName',
        component: 'Input',
      },
    ],
    // fieldMapToTime: ['beginDate', 'endDate'],
    fieldMapToTime: [['beginDate', ['beginDate_gte', 'endDate_lte']]],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('起始时间'),
      dataIndex: 'beginDate',
      key: 'a.begin_date',
      sorter: true,
      width: 100,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('结束时间'),
      dataIndex: 'endDate',
      key: 'a.end_date',
      sorter: true,
      width: 100,
      align: 'center',
    },
    {
      title: t('同步结果描述'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 330,
      align: 'left',
    },
    {
      title: t('同步时间'),
      dataIndex: 'createDate',
      key: 'a.create_date',
      sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('同步用户信息'),
      dataIndex: 'createByName',
      key: 'a.create_by_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑billmanager_his_acceptance_excute'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'billmanager:bankdirectlink:monitor:billmanagerHisAcceptanceExcute:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除billmanager_his_acceptance_excute'),
        popConfirm: {
          title: t('是否确认删除billmanager_his_acceptance_excute'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'billmanager:bankdirectlink:monitor:billmanagerHisAcceptanceExcute:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload }] = useTable({
    api: billmanagerHisAcceptanceExcuteListData,
    beforeFetch: (params) => {
      // 处理日期参数，确保只传递日期部分，不包含时分秒
      return params;
    },
    columns: tableColumns,
    // actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { id: record.id };
    const res = await billmanagerHisAcceptanceExcuteDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
