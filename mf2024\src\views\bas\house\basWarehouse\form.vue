<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'bas:house:basWarehouse:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsBasHouseBasWarehouseForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasWarehouse, basWarehouseSave, basWarehouseForm } from '/@/api/bas/house/basWarehouse';
  import { basPositionTreeData } from '/@/api/bas/pos/basPosition';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('bas.house.basWarehouse');
  const { showMessage } = useMessage();
  const record = ref<BasWarehouse>({} as BasWarehouse);
  const getTitle = computed(() => ({
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增仓库档案') : t('编辑仓库档案'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('仓库编码'),
      field: 'cwhcode',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      required: true,
      dynamicDisabled: true,
    },
    {
      label: t('仓库名称'),
      field: 'cwhname',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      required: true,
      dynamicDisabled: true,
    },
    {
      label: t('仓库地址'),
      field: 'cwhaddress',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
      dynamicDisabled: true,
    },
    {
      label: t('电话'),
      field: 'cwhphone',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
      dynamicDisabled: true,
    },
    {
      label: t('负责人'),
      field: 'cwhperson',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
      dynamicDisabled: true,
    },
    {
      label: t('是否代管'),
      field: 'bproxywh',
      component: 'CheckboxGroup',
      componentProps: {
        options: [{ label: '', value: '1' }],
      },
      dynamicDisabled: true,
    },
    {
      label: t('启用货位'),
      field: 'bwhpos',
      component: 'CheckboxGroup',
      componentProps: {
        options: [{ label: '', value: '1' }],
      },
      dynamicDisabled: true,
    },
    {
      label: t('多货位'),
      field: 'multiplepos',
      component: 'CheckboxGroup',
      componentProps: {
        options: [{ label: '', value: '1' }],
      },
    },
    {
      label: t('默认货位'),
      field: 'deftpos',
      fieldLabel: 'basPosition.posName',
      component: 'TreeSelect',
      componentProps: {
        api: basPositionTreeData,
        params: { isShowCode: true, whcode: record.value.cwhcode },
        canSelectParent: false,
        allowClear: true,
      },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 12 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await basWarehouseForm(data);
    record.value = (res.basWarehouse || {}) as BasWarehouse;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    updateSchema([
      {
        field: 'cwhcode',
        componentProps: {
          disabled: !record.value.isNewRecord,
        },
      },
      {
        label: t('默认货位'),
        field: 'deftpos',
        fieldLabel: 'basPosition.posName',
        component: 'TreeSelect',
        componentProps: {
          api: basPositionTreeData,
          params: { isShowCode: true, whcode: record.value.cwhcode },
          canSelectParent: false,
          allowClear: true,
        },
      },
    ]);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        cwhcode: record.value.cwhcode,
      };
      // console.log('submit', params, data, record);
      const res = await basWarehouseSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
