<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicModal
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'filemanager:filemanagerShared:edit'"
    @register="registerModal"
    @ok="handleSubmit"
    :min-height="100"
    width="600"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup name="ViewsFilemanagerFilemanagerSharedForm">
  import { ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  // import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { officeTreeData } from '/@/api/sys/office';
  import {
    FilemanagerShared,
    filemanagerSharedSave,
    filemanagerSharedForm,
  } from '/@/api/filemanager/filemanagerShared';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('filemanager.filemanagerShared');
  const { showMessage } = useMessage();
  const record = ref<FilemanagerShared>({} as FilemanagerShared);
  const getTitle = computed(() => ({
    icon: 'i-ant-design:share-alt-outlined',
    value: t('文件分享'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('分享给'),
      field: 'receiveUserCode',
      fieldLabel: 'receiveUserName',
      component: 'TreeSelect',
      componentProps: {
        api: officeTreeData,
        params: { isLoadUser: true, userIdPrefix: '', isAll: true },
        canSelectParent: false,
        treeCheckable: true,
        allowClear: true,
      },
      required: true,
    },
    {
      label: t('备注信息'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 500,
      },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 23, md: 23 },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ loading: true });
    await resetFields();
    const res = await filemanagerSharedForm(data);
    record.value = (res.filemanagerShared || {}) as FilemanagerShared;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setModalProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setModalProps({ confirmLoading: true });
      data.ids = record.value.ids;
      // console.log('submit', params, data, record);
      const res = await filemanagerSharedSave({}, data);
      showMessage(res.message);
      closeModal();
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
