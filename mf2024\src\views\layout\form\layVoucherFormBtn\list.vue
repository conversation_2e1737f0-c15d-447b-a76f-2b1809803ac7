<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleForm({ viewCode: props.colpop.code })"
          v-auth="'layout:edit'"
        >
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.viewCode }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherFormBtnList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import {
    layVoucherFormBtnDelete,
    layVoucherFormBtnListData,
  } from '../../../../api/layout/form/layVoucherFormBtn';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('layout.layVoucherFormBtn');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: t('表单按钮管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('布局标志'),
        field: 'viewCode',
        component: 'Input',
      },
      {
        label: t('按钮类型'),
        field: 'btnType',
        component: 'Select',
        componentProps: {
          dictType: 'lay_btn_type',
          allowClear: true,
        },
      },
      {
        label: t('权限'),
        field: 'auth',
        component: 'Input',
      },
      {
        label: t('图标'),
        field: 'icon',
        component: 'Input',
      },
      {
        label: t('按钮标题'),
        field: 'title',
        component: 'Input',
      },
      {
        label: t('按钮ID'),
        field: 'btnId',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('布局标志'),
      dataIndex: 'viewCode',
      key: 'a.view_code',
      sorter: true,
      width: 230,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('按钮类型'),
      dataIndex: 'btnType',
      key: 'a.btn_type',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'lay_btn_type',
    },
    {
      title: t('权限'),
      dataIndex: 'auth',
      key: 'a.auth',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('图标'),
      dataIndex: 'icon',
      key: 'a.icon',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('按钮标题'),
      dataIndex: 'title',
      key: 'a.title',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('按钮ID'),
      dataIndex: 'btnId',
      key: 'a.btn_id',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑按钮'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'layout:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除按钮'),
        popConfirm: {
          title: t('是否确认删除按钮'),
          confirm: handleDelete.bind(this, { id: record.id }),
        },
        auth: 'layout:edit',
      },
    ],
  };

  const props = defineProps({
    colpop: { type: Object, default: {} },
  });

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm }] = useTable({
    api: layVoucherFormBtnListData,
    beforeFetch: (params) => {
      params.viewCode = props.colpop.code;
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: false,
    canResize: true,
  });

  watch(
    () => props.colpop,
    () => {
      reload();
    },
    // { immediate: true },
  );

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const res = await layVoucherFormBtnDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleSuccess() {
    reload();
  }
</script>
