/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BpmTask } from './model';

const { adminPath } = useGlobSetting();

export const bpmGetTask = (params?: BpmTask | any) =>
  defHttp.post<BpmTask>({ url: adminPath + '/bpm/bpmTask/getTask', params });

export const bpmGetNextUser = (params?: Recordable) =>
  defHttp.postJson<Recordable>({ url: adminPath + '/bpm/bpmTask/getNextUser', params });

export const bpmClaim = (params?: BpmTask | any) =>
  defHttp.post<BpmTask>({ url: adminPath + '/bpm/bpmTask/claim', params });

export const bpmComplete = (params?: BpmTask | any) =>
  defHttp.post<BpmTask>({ url: adminPath + '/bpm/bpmTask/complete', params });

export const bpmBack = (params?: BpmTask | any) =>
  defHttp.post<BpmTask>({ url: adminPath + '/bpm/bpmTask/back', params });

export const bpmBackTask = (params?: BpmTask | any) =>
  defHttp.post<BpmTask>({ url: adminPath + '/bpm/bpmTask/backTask', params });

export const bpmTurn = (params?: BpmTask | any) =>
  defHttp.post<BpmTask>({ url: adminPath + '/bpm/bpmTask/turn', params });

export const bpmTurnTask = (params?: BpmTask | any) =>
  defHttp.post<BpmTask>({ url: adminPath + '/bpm/bpmTask/turnTask', params });

export const bpmModifySign = (params?: BpmTask | any) =>
  defHttp.post<BpmTask>({ url: adminPath + '/bpm/bpmTask/modifySign', params });

export const bpmModifySignTask = (params?: BpmTask | any) =>
  defHttp.post<BpmTask>({ url: adminPath + '/bpm/bpmTask/modifySignTask', params });

export const bpmMove = (params?: BpmTask | any) =>
  defHttp.post<BpmTask>({ url: adminPath + '/bpm/bpmTask/move', params });

export const bpmMoveTask = (params?: BpmTask | any) =>
  defHttp.post<BpmTask>({ url: adminPath + '/bpm/bpmTask/moveTask', params });

export const bpmRollback = (params?: BpmTask | any) =>
  defHttp.post<BpmTask>({ url: adminPath + '/bpm/bpmTask/rollback', params });
