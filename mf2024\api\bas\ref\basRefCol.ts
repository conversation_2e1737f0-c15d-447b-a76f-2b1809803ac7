/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page, TreeDataModel } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface BasRefCol extends BasicModel<BasRefCol> {
  refType?: string; // 转换类型
  ckey?: string; // 当前KEY
  cname?: string; // 当前名称
  extkey?: string; // 外部KEY
  extname?: string; // 外部名称
}

export const basRefColList = (params?: BasRefCol | any) =>
  defHttp.get<BasRefCol>({ url: adminPath + '/bas/ref/basRefCol/list', params });

export const basRefColListData = (params?: BasRefCol | any) =>
  defHttp.post<Page<BasRefCol>>({ url: adminPath + '/bas/ref/basRefCol/listData', params });

export const basRefColForm = (params?: BasRefCol | any) =>
  defHttp.get<BasRefCol>({ url: adminPath + '/bas/ref/basRefCol/form', params });

export const basRefColSave = (params?: any, data?: BasRefCol | any) =>
  defHttp.postJson<BasRefCol>({ url: adminPath + '/bas/ref/basRefCol/save', params, data });

export const basRefColImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/bas/ref/basRefCol/importData',
      onUploadProgress,
    },
    params,
  );

export const basRefColDelete = (params?: BasRefCol | any) =>
  defHttp.get<BasRefCol>({ url: adminPath + '/bas/ref/basRefCol/delete', params });

export const basRefColTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/bas/ref/basRefType/treeData', params });
