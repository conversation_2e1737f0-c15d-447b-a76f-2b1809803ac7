<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable" @fetchSuccess="fetchSuccess">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button @click="expandAll" :title="t('展开一级')">
          <Icon icon="bi:chevron-double-down" /> {{ t('展开') }}
        </a-button>
        <a-button @click="collapseAll" :title="t('展开全部')">
          <Icon icon="bi:chevron-double-up" /> {{ t('折叠') }}
        </a-button>
        <!-- <a-button type="primary" @click="handleForm({})" v-auth="'bas:inv:edit'">
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button> -->
      </template>
      <template #firstColumn="{ record }">
        <span class="cursor-pointer" @click="expandCollapse(record)"> ( {{ record.code }} ) </span>
        <a @click="handleForm({ code: record.code })">
          {{ record.name }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsBasInvBasInvClsList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, watch, nextTick, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { basInvClsDelete, basInvClsListData, updateFbStatus } from '/@/api/bas/inv/basInvCls';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const props = defineProps({
    treeCode: String,
  });

  const { t } = useI18n('bas.inv.basInvCls');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: router.currentRoute.value.meta.title || t('产品分类管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('分类编码'),
        field: 'code',
        component: 'Input',
      },
      {
        label: t('分类名称'),
        field: 'name',
        component: 'Input',
      },
      {
        label: t('状态'),
        field: 'status',
        component: 'Select',
        componentProps: {
          dictType: 'sys_search_status',
          allowClear: true,
          onChange: handleSuccess,
        },
      },
      {
        label: t('备注信息'),
        field: 'remarks',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('分类编码'),
      dataIndex: 'code',
      width: 230,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('分类名称'),
      dataIndex: 'name',
      width: 130,
      align: 'left',
    },
    {
      title: t('排序号'),
      dataIndex: 'treeSort',
      width: 130,
      align: 'center',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      width: 130,
      align: 'center',
      dictType: 'sys_search_status',
    },
    {
      title: t('更新时间'),
      dataIndex: 'updateDate',
      width: 130,
      align: 'center',
    },
    {
      title: t('备注信息'),
      dataIndex: 'remarks',
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑产品分类'),
        onClick: handleForm.bind(this, { code: record.code }),
        auth: 'bas:inv:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除产品分类'),
        popConfirm: {
          title: t('是否确认删除产品分类'),
          confirm: handleDelete.bind(this, { code: record.code }),
        },
        auth: 'bas:inv:edit',
      },
      {
        icon: 'fluent:add-circle-24-regular',
        title: t('新建下级产品分类'),
        onClick: handleForm.bind(this, {
          parentCode: record.id,
          parentName: record.name,
        }),
        auth: 'bas:inv:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [
    registerTable,
    { reload, expandAll, collapseAll, expandCollapse, getForm, getSelectRows },
  ] = useTable({
    api: basInvClsListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    //actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    isTreeTable: true,
    pagination: false,
    canResize: true,
  });

  watch(
    () => props.treeCode,
    async () => {
      await getForm().setFieldsValue({
        code: props.treeCode,
      });
      reload();
    },
  );

  function fetchSuccess() {
    if (props.treeCode) {
      nextTick(expandAll);
    }
  }

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const res = await basInvClsDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  async function handleQxFb(ope) {
    if (getSelectRows().length) {
      var selIds = ref('');
      getSelectRows().forEach((item) => {
        selIds.value += item.id + ',';
      });
      const res = await updateFbStatus({ selIds: selIds.value, ope: ope });
      showMessage(res.message);
      handleSuccess();
    } else {
      showMessage('请选择一行数据！！');
    }
  }

  function handleSuccess() {
    reload();
  }
</script>
