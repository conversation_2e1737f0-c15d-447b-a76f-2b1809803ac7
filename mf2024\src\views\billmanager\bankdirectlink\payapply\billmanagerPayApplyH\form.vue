<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'bankdirectlink:payapply:billmanagerPayApplyH:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="70%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm">
      <template #billmanagerPayApplyCList>
        <BasicTable
          @register="registerBillmanagerPayApplyCTable"
          @row-click="handleBillmanagerPayApplyCRowClick"
        />
        <a-button
          class="mt-2"
          @click="handleBillmanagerPayApplyCAdd"
          v-auth="'bankdirectlink:payapply:billmanagerPayApplyH:edit'"
          v-show="record.status !== '4'"
        >
          <Icon icon="i-ant-design:plus-circle-outlined" /> {{ t('新增') }}
        </a-button>
        <a-button
          class="mt-2 ml-2"
          @click="handleBillmanagerPayApplyCReference"
          v-auth="'bankdirectlink:payapply:billmanagerPayApplyH:edit'"
          v-show="record.status !== '4'"
        >
          <Icon icon="i-ant-design:plus-circle-outlined" /> {{ t('参照合同') }}
        </a-button>
      </template>
    </BasicForm>
    <template #footer>
      <BpmButton
        v-model:bpmEntity="record"
        bpmEntityKey="id"
        formKey="peyApply"
        completeText="提交"
        :completeModal="true"
        :loading="loadingRef"
        :auth="'bankdirectlink:payapply:billmanagerPayApplyH:edit'"
        @validate="handleValidate"
        @complete="handleSubmit"
        @success="handleSuccess"
        @close="closeDrawer"
      />
    </template>
    <ListSelect
      ref="listSelectRef"
      selectType="contractListSelect"
      :checkbox="false"
      @select="handleSelect"
      :selectList="selectListRef"
      :queryParams="queryParams"
      v-show="false"
    />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsBankdirectlinkPayapplyBillmanagerPayApplyHForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BillmanagerPayApplyH, billmanagerPayApplyHSave, billmanagerPayApplyHForm } from '/@/api/billmanager/bankdirectlink/payapply/billmanagerPayApplyH';
  import { BpmButton } from '/@/components/Bpm';
  import { ListSelect } from '/@/components/ListSelect';
  import { officeTreeData } from '/@/api/sys/office';
  import { OfficeTypeEnum } from '/@/enums/defEnum';
  import { billmanagerUseOfFundsTreeData } from '/@/api/billmanager/bankdirectlink/useoffunds/billmanagerUseOfFunds';
  import { useDict } from '/@/components/Dict';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('bankdirectlink.payapply.billmanagerPayApplyH');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<BillmanagerPayApplyH>({} as BillmanagerPayApplyH);
  const loadingRef = ref(false);
  const selectListRef = ref<any>([]);
  let queryParams = ref<any>({});
  const listSelectRef = ref<any>(null);
  const { initDict, getDictLabel } = useDict();

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增付款申请单主表') : t('编辑付款申请单主表'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('申请单号'),
      field: 'id',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      dynamicDisabled: true,
      // show: !record.value.isNewRecord,
      ifShow: () => !record.value.isNewRecord,
    },
    {
      label: t('申请日期'),
      field: 'ddate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        showTime: false,
      },
      required: true,
    },
    {
      label: t('申请部门'),
      field: 'deptCode',
      fieldLabel: 'office.officeName',
      component: 'TreeSelect',
      componentProps: {
        // maxlength: 100,
        api: officeTreeData,
        params: { isLoadUser: false, userIdPrefix: '', isAll: true },
        canSelectParent: false,
        // treeCheckable: true,
        allowClear: true,
        maxlength: 64,
      },
      required: true,
    },
    // {
    //   label: t('创建人名称'),
    //   field: 'createByName',
    //   component: 'Input',
    //   componentProps: {
    //     maxlength: 150,
    //   },
    // },
    {
      label: t('资金用途'),
      field: 'useOfFunds',
      fieldLabel: 'billmanagerUseOfFunds.name',
      component: 'TreeSelect',
      componentProps: {
        api: billmanagerUseOfFundsTreeData,
        params: { isAll: true },
        canSelectParent: false,
        // treeCheckable: true,
        allowClear: true,
        maxlength: 64,
      },
      required: true,
    },
    {
      label: t('供应商'),
      field: 'venCode',
      fieldLabel: 'vendor.officeName',
      component: 'TreeSelect',
      componentProps: {
        api: officeTreeData,
        params: { isLoadUser: true, userIdPrefix: '', isAll: true, officeTypes: OfficeTypeEnum.WL },
        canSelectParent: false,
        // treeCheckable: true,
        allowClear: true,
        maxlength: 64,
      },
    },
    {
      label: t('备注'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 4000,
      },
      colProps: { lg: 24, md: 24 },
    },

    {
      label: t('明细'),
      field: 'billmanagerPayApplyCList',
      component: 'Input',
      colProps: { lg: 24, md: 24 },
      slot: 'billmanagerPayApplyCList',
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerBillmanagerPayApplyCTable, billmanagerPayApplyCTable] = useTable({
    actionColumn: {
      width: 60,
      actions: (_record: Recordable) => [
        {
          icon: 'i-ant-design:delete-outlined',
          color: 'error',
          popConfirm: {
            title: '是否确认删除',
            confirm: handleBillmanagerPayApplyCDelete.bind(this, _record),
          },
          auth: 'bankdirectlink:payapply:billmanagerPayApplyH:edit',
          ifShow: () => record.value.status !== '4',
        },
      ],
    },
    rowKey: 'id',
    pagination: false,
    bordered: true,
    size: 'small',
    inset: true,
  });

  async function setBillmanagerPayApplyCTableData(_res: Recordable) {
    billmanagerPayApplyCTable.setColumns([
      {
        title: t('行号'),
        dataIndex: 'irowno',
        width: 60,
        align: 'left',
        editRow: false,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 8,
        },
        editRule: false,
      },
      {
        title: t('款项类型'),
        dataIndex: 'payType',
        width: 130,
        align: 'left',
        dictType: 'mf_payType', // 添加字典类型，用于显示标签值
        editRow: true,
        editComponent: 'Select',
        editComponentProps: {
          maxlength: 10,
          dictType: 'mf_payType',
          required: true,
        },
        editRule: true,
        customRender: ({ record }) => {
          // 优先显示字典标签，如果没有则显示原值
          return record.payTypeLabel || getDictLabel('mf_payType', record.payType) || record.payType;
        },
      },
      // {
      //   title: t('供应商'),
      //   dataIndex: 'venCode',
      //   width: 130,
      //   align: 'left',
      //   editRow: true,
      //   editComponent: 'Input',
      //   editComponentProps: {
      //     maxlength: 100,
      //   },
      //   editRule: false,
      // },
      {
        title: t('币种'),
        dataIndex: 'cexchName',
        width: 130,
        align: 'left',
        dictType: 'mf_bizhong', // 添加字典类型，用于显示标签值
        editRow: true,
        editComponent: 'Select',
        editComponentProps: {
          maxlength: 10,
          dictType: 'mf_bizhong',
          required: true,
        },
        editRule: false,
        customRender: ({ record }) => {
          // 优先显示字典标签，如果没有则显示原值
          return record.cexchNameLabel || getDictLabel('mf_bizhong', record.cexchName) || record.cexchName;
        },
      },
      {
        title: t('原币金额'),
        dataIndex: 'orgAmount',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 16,
          required: true,
        },
        editRule: true,
      },
      {
        title: t('部门'),
        dataIndex: 'deptCode',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'TreeSelect',
        editComponentProps: {
          api: officeTreeData,
          params: { isLoadUser: false, userIdPrefix: '', isAll: true },
          canSelectParent: false,
          // treeCheckable: true,
          allowClear: true,
          // params: { postType: '1' },
          maxlength: 64,
          required: true,
        },
        editRule: false,
      },
      {
        title: t('备注'),
        dataIndex: 'remarks',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'InputTextArea',
        editComponentProps: {
          maxlength: 4000,
        },
        editRule: false,
      },
      // {
      //   title: t('来源id'),
      //   dataIndex: 'sourceId',
      //   width: 130,
      //   align: 'left',
      //   editRow: true,
      //   editComponent: 'Input',
      //   editComponentProps: {
      //     maxlength: 64,
      //   },
      //   editRule: false,
      // },
      {
        title: t('合同号'),
        dataIndex: 'contractId',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 64,
          required: true,
        },
        editRule: false,
      },
      // {
      //   title: t('资金用途'),
      //   dataIndex: 'useOfFunds',
      //   width: 130,
      //   align: 'left',
      //   editRow: true,
      //   editComponent: 'Select',
      //   editComponentProps: {
      //     dictType: 'mf_amount_use',
      //     maxlength: 50,
      //     required: true,
      //   },
      //   editRule: true,
      // },
      {
        title: t('U8生单状态'),
        dataIndex: 'u8Status',
        width: 130,
        align: 'left',
        editRow: false,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 10,
        },
        editRule: false,
      },
    ]);

    // 初始化字典数据
    await initDict(['mf_payType', 'mf_bizhong']);

    // 处理字典字段的显示值
    const tableData = (record.value.billmanagerPayApplyCList || []).map((item: any) => {
      const processedItem = {
        ...item,
      };

      // 为字典字段设置显示标签
      if (item.payType) {
        processedItem.payTypeLabel = getDictLabel('mf_payType', item.payType);
      }
      if (item.cexchName) {
        processedItem.cexchNameLabel = getDictLabel('mf_bizhong', item.cexchName);
      }

      console.log('处理字典字段:', {
        原始: { payType: item.payType, cexchName: item.cexchName },
        处理后: { payTypeLabel: processedItem.payTypeLabel, cexchNameLabel: processedItem.cexchNameLabel }
      });

      return processedItem;
    });

    console.log('设置表格数据:', tableData);
    billmanagerPayApplyCTable.setTableData(tableData);
  }

  function handleBillmanagerPayApplyCRowClick(record: Recordable) {
    record.onEdit?.(true, false);
  }

  // 获取下一个行号
  function getNextRowNo() {
    const dataSource = billmanagerPayApplyCTable.getDataSource();
    if (dataSource.length === 0) {
      return 1;
    }
    // 找到当前最大的行号
    const maxRowNo = Math.max(...dataSource.map(item => parseInt(item.irowno) || 0));
    return maxRowNo + 1;
  }

  function handleBillmanagerPayApplyCAdd() {
    billmanagerPayApplyCTable.insertTableDataRecord({
      id: new Date().getTime(),
      // 行号自动递增
      irowno: getNextRowNo(),
      isNewRecord: true,
      editable: true,
    });
  }

  function handleBillmanagerPayApplyCReference() {
    console.log(listSelectRef.value);
    listSelectRef.value.openSelectModal();
  }

  function handleBillmanagerPayApplyCDelete(record: Recordable) {
    // console.log('handleBillmanagerPayApplyCDelete', record);
    billmanagerPayApplyCTable.deleteTableDataRecord(record);
  }

  async function getBillmanagerPayApplyCList() {
    let billmanagerPayApplyCListValid = true;
    let billmanagerPayApplyCList: Recordable[] = [];
    for (const record of billmanagerPayApplyCTable.getDataSource()) {
      if (!(await record.onEdit?.(false, true))) {
        billmanagerPayApplyCListValid = false;
      }
      billmanagerPayApplyCList.push({
        ...record,
        id: !!record.isNewRecord ? '' : record.id,
      });
    }
    for (const record of billmanagerPayApplyCTable.getDelDataSource()) {
      if (!!record.isNewRecord) continue;
      billmanagerPayApplyCList.push({
        ...record,
        status: '1',
      });
    }
    if (!billmanagerPayApplyCListValid) {
      throw { errorFields: [{ name: ['billmanagerPayApplyCList'] }] };
    }
    return billmanagerPayApplyCList;
  }

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await billmanagerPayApplyHForm(data);
    record.value = (res.billmanagerPayApplyH || {}) as BillmanagerPayApplyH;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setBillmanagerPayApplyCTableData(res);
    setDrawerProps({ loading: false });
  });

  async function handleValidate(_event: any, formData: any) {
    try {
      const data = await validate();
      console.log('data===111', data);
      formData(true, data); // 将表单数据传递给 BpmButton
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    }
  }

  async function handleSubmit(event: any) {
    try {
      loadingRef.value = true;
      // const data = event?.formData || (await validate()); // 接受 BpmButton 传递过来的表单数据
      const data = Object.assign(await validate(), event?.formData);
      // console.log('data===', data, event, await validate());
      data.bpm = Object.assign(data.bpm || {}, record.value.bpm); // 流程信息
      data.status = record.value.status; // 提交状态
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        djno: record.value.djno,
      };
      data.billmanagerPayApplyCList = await getBillmanagerPayApplyCList();
      // console.log('submit', params, data, record);
      const res = await billmanagerPayApplyHSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      loadingRef.value = false;
      setDrawerProps({ confirmLoading: false });
    }
  }

  async function handleSuccess() {
    emit('success');
  }

  async function handleSelect(selectData: any) {
    console.log(selectData);
    // billmanagerPayApplyCTable.updateTableDataRecord(record.value.id, {
    //   ...record.value,
    //   contractId: selectData[0].id,
    // });

    // 为每个选中的合同添加一行，行号递增
    selectData.forEach((item: any) => {
      billmanagerPayApplyCTable.insertTableDataRecord({
        editable: true,
        id: new Date().getTime() + Math.random(), // 确保每行的id唯一
        contractId: item.id,
        irowno: getNextRowNo(), // 使用递增行号
        isNewRecord: true,
      });
    });
  }
</script>
