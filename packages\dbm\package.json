{"name": "@jeesite/dbm", "version": "5.12.1", "private": true, "type": "module", "scripts": {"type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "uninstall": "rimraf node_modules", "update": "ncu -u"}, "dependencies": {"@jeesite/dbm-lib": "5.12.1-rc.1", "qs": "6.14.0"}, "devDependencies": {"@types/qs": "6.9.18"}, "homepage": "https://jeesite.com", "repository": {"type": "git", "url": "https://gitee.com/thinkgem/jeesite-vue.git"}, "bugs": {"url": "https://gitee.com/thinkgem/jeesite-vue/issues"}, "author": {"name": "ThinkGem", "email": "<EMAIL>", "url": "https://gitee.com/thinkgem"}}