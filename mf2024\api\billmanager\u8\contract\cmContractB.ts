/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface CmContractB extends BasicModel<CmContractB> {
  strcontractid?: string; // 合同编号
  guid?: string; // GUID
  strcontractgrp?: string; // 合同分组编码
  strcontracttype?: string; // 合同类型编码
  strcontractkind?: string; // 合同性质
  strcontractname?: string; // 合同名称
  strbisectionunit?: string; // 对方单位
  strparentid?: string; // 所属主合同编码
  strrepair?: string; // 保修期
  strbisectionperson?: string; // 对方负责人
  strcontractorderdate?: string; // 合同签定日期
  strcontractstartdate?: string; // 合同开始日期
  strcontractenddate?: string; // 合同结束日期
  strcontractdesc?: string; // 合同描述
  dblmassassurescale?: number; // 质保金比例
  dblmassassure?: number; // 质保金额度
  cdefine1?: string; // cdefine1
  cdefine2?: string; // cdefine2
  cdefine3?: string; // cdefine3
  cdefine4?: string; // cdefine4
  cdefine5?: number; // cdefine5
  cdefine6?: string; // cdefine6
  cdefine7?: number; // cdefine7
  cdefine8?: string; // cdefine8
  cdefine9?: string; // cdefine9
  cdefine10?: string; // cdefine10
  cdefine11?: string; // cdefine11
  cdefine12?: string; // cdefine12
  cdefine13?: string; // cdefine13
  cdefine14?: string; // cdefine14
  cdefine15?: number; // cdefine15
  cdefine16?: number; // cdefine16
  strsetupperson?: string; // 创建人
  strsetupdate?: string; // 创建日期
  strendcaseperson?: string; // 结案人
  strendcasedate?: string; // 结案日期
  strinureperson?: string; // 生效人
  strinuredate?: string; // 生效日期
  strdeptid?: string; // 部门编码
  strpersonid?: string; // 业务员编码
  intvaryid?: number; // 变更单号
  strvarycauseid?: string; // 变更原因码
  dtvarydate?: string; // 变更日期
  strvarypersonid?: string; // 变更申请人
  strvarypasspersonid?: string; // 变更生效人
  intpre?: number; // 期初标志
  strway?: string; // 收支方向
  strcurrency?: string; // 币种
  dblexchange?: number; // 汇率
  strvaryperson?: string; // 变更制单人
  tstime?: string; // 时间戳
  strspare1?: string; // 备用字段1
  strspare2?: string; // 备用字段2
  strspare3?: string; // 备用字段3
  strsource?: string; // 备用
  dbltotalcurrency?: number; // 备用
  dblexeccurrency?: number; // 备用
  dbltotalquantity?: number; // 备用
  dblexecquqantity?: number; // 备用
  cbustype?: string; // 业务类型
  csccode?: string; // 运输方式
  cgatheringplan?: string; // 收付款计划
  iswfcontrolled?: string; // 是否工作流控制
  iverifystate?: number; // 控制流控制状态
  ireturncount?: number; // 返回工作流审核次数
  intauditsymbol?: number; // 审核标志
  czbjcomputemode?: string; // 质保金计算方式
  dtzbjstartdate?: string; // 质保金开始日期
  dtzbjenddate?: string; // 质保金结束日期
  busestage?: string; // 是否启用阶段
  cstagegroupcode?: string; // 阶段组编码
  dtcreatetime?: string; // 制单时间
  dtmodifytime?: string; // 修改时间
  dtmodifydate?: string; // 修改时间
  dteffecttime?: string; // 生效时间
  cmodifer?: string; // 修改人
  dtvarycreatedate?: string; // 变更单制单时间
  dtvarycreatetime?: string; // 变更单制单时间
  dtvarymodifytime?: string; // 变更单修改时间
  dtvarymodifydate?: string; // 变更单修改日期
  dtvaryeffecttime?: string; // 变更单生效时间
  cvarymodifer?: string; // 变更单修改人
  istatus?: number; // 合同状态
  intexeccontroltype?: number; // 时效控制方式
  cexeccontrolvouch?: string; // 时效控制环节
  iprintcount?: number; // 打印次数
  iincotermid?: number; // 贸易术语
  dlastladedate?: string; // 最迟装船日期
  csportcode?: string; // 装运港
  ctportcode?: string; // 转运港
  caportcode?: string; // 目的港
  centrustcode?: string; // 委托方编码
  cagencycalmethod?: string; // 代理费计算方式
  decagencyfeebillrates?: number; // 代理费计费费率
  decspecficcharges?: number; // 从量收费标准
  decagencyfees?: number; // 代理费
  decentgathering?: number; // 委托方收款金额
  dentgatheringdate?: string; // 委托方收款日期
  ccontactcode?: string; // 联系人编码
  csysbarcode?: string; // 单据条码
  ccurrentauditor?: string; // 当前审核人
  csourcetype?: string; // 来源类别
  ioppid?: string; // 商机编码
  coppcode?: string; // 商机主题
}

export const cmContractBList = (params?: CmContractB | any) =>
  defHttp.get<CmContractB>({ url: adminPath + '/billmanager/u8/contract/cmContractB/list', params });

export const cmContractBListData = (params?: CmContractB | any) =>
  defHttp.post<Page<CmContractB>>({ url: adminPath + '/billmanager/u8/contract/cmContractB/listData', params });

export const cmContractBForm = (params?: CmContractB | any) =>
  defHttp.get<CmContractB>({ url: adminPath + '/billmanager/u8/contract/cmContractB/form', params });

export const cmContractBSave = (params?: any, data?: CmContractB | any) =>
  defHttp.postJson<CmContractB>({ url: adminPath + '/billmanager/u8/contract/cmContractB/save', params, data });

export const cmContractBDelete = (params?: CmContractB | any) =>
  defHttp.get<CmContractB>({ url: adminPath + '/billmanager/u8/contract/cmContractB/delete', params });
