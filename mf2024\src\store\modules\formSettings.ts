import { defineStore } from 'pinia';

import { FORM_SETTINGS_KEY } from '/@/enums/cacheEnum';
import { Persistent } from '/@/utils/cache/persistent';

interface FormSettingsState {
  formSettings: Nullable<any>;
}

export const useFormSettingsStore = defineStore({
  id: 'form-settings',
  state: (): FormSettingsState => ({
    formSettings: Persistent.getLocal(FORM_SETTINGS_KEY) || {},
  }),
  actions: {
    getFormTabsSettings(key: string, defaultValue: any = []) {
      return this.getFormSettings(key, 'tabs', defaultValue);
    },
    setFormTabsSettings(key: string, value: any) {
      this.setFormSettings(key, 'tabs', value);
    },
    getFormSettings(key: string, key2: string, defaultValue: any) {
      if (!key || !key2) return defaultValue;
      if (!this.formSettings[key]) this.formSettings[key] = {};
      return this.formSettings[key][key2] || defaultValue;
    },
    setFormSettings(key: string, key2: string, value: any) {
      if (!key || !key2) return;
      if (!this.formSettings[key]) this.formSettings[key] = {};
      this.formSettings[key][key2] = value;
      Persistent.setLocal(FORM_SETTINGS_KEY, this.formSettings, true);
    },
  },
});
