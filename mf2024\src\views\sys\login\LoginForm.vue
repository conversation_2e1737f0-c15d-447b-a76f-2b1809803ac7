<template>
  <!-- <AppDarkModeToggle class="enter-x absolute right-7 top-3" /> -->
  <LoginFormTitle v-show="getShow" class="enter-x" />
  <div  class="enter-x absolute right-10 bottom-3 text-white">
    <AppDarkModeToggle />
  </div>
  <AppLocalePicker
    class="enter-x absolute right-25 bottom-4 text-white lg:text-gray-600"
    :showText="false"
  />

  <Form
    class="enter-x p-4"
    :model="formData"
    :rules="getFormRules"
    ref="formRef"
    v-show="getShow"
    @keypress.enter="handleLogin"
  >
    <div class="font-size mb-3 text-center font-size-4 text-red">
      {{ query.message }}
    </div>
    <FormItem name="account" class="enter-x">
      <Input
        size="large"
        v-model:value="formData.account"
        :placeholder="t('sys.login.userName')"
        class="fix-auto-fill"
      />
    </FormItem>
    <FormItem name="password" class="enter-x">
      <InputPassword
        size="large"
        visibilityToggle
        v-model:value="formData.password"
        :placeholder="t('sys.login.password')"
        autocomplete="false"
      />
    </FormItem>
    <FormItem v-if="isValidCodeLogin" name="validCode" class="enter-x valid-code">
      <Input
        size="large"
        visibilityToggle
        v-model:value="formData.validCode"
        :placeholder="t('sys.login.validCode')"
      >
        <!-- addonAfter suffix -->
        <template #suffix>
          <img
            :src="getValidCodeImg"
            @click="refreshValidCodeImg"
            class="cursor-pointer"
            width="100"
          />
        </template>
      </Input>
    </FormItem>
    <FormItem v-if="useCorpModel" name="corpCode" class="enter-x">
      <Select
        showSearch
        :options="corpOptions"
        @change="handleSwitchCorp"
        :placeholder="t('sys.login.corpPlaceholder')"
      />
    </FormItem>

    <ARow class="enter-x">
      <ACol :span="12">
        <FormItem>
          <!-- No logic, you need to deal with it yourself -->
          <Checkbox v-model:checked="rememberMe" size="small">
            {{ t('sys.login.rememberMe') }}
          </Checkbox>
        </FormItem>
      </ACol>
      <ACol :span="12" v-if="false">
        <FormItem :style="{ 'text-align': 'right' }">
          <!-- No logic, you need to deal with it yourself -->
          <Button type="link" size="small" @click="setLoginState(LoginStateEnum.RESET_PASSWORD)">
            {{ t('sys.login.forgetPassword') }}
          </Button>
        </FormItem>
      </ACol>
    </ARow>

    <FormItem class="enter-x">
      <Button v-if="demoMode" size="large" block @click="handleLogin" :loading="loading">
        {{ t('sys.login.loginButton') }}
      </Button>
      <Button v-else type="primary" size="large" block @click="handleLogin" :loading="loading">
        {{ t('sys.login.loginButton') }}
      </Button>
      <Button
        v-if="demoMode"
        type="primary"
        size="large"
        block
        :href="`${ctxPath}/oauth2/login/gitee`"
        @click="handleOauth2"
        :loading="loading"
        class="mt-4"
      >
        <i class="iconfont icongitee2 mr-1.5" style="margin-bottom: 2px"></i>
        {{ t('使用 Gitee 账号 Star，免密登录') }}
      </Button>
      <!-- <Button size="large" class="mt-4 enter-x" block @click="handleRegister">
        {{ t('sys.login.registerButton') }}
      </Button> -->
    </FormItem>
    <ARow class="enter-x md:pl-3" v-if="false">
      <ACol :md="7" :xs="24">
        <Button block @click="setLoginState(LoginStateEnum.MOBILE)">
          {{ t('sys.login.mobileSignInFormTitle') }}
        </Button>
      </ACol>
      <ACol :md="8" :xs="24" class="xs:mx-0 !my-2 md:mx-2 !md:my-0">
        <Button block @click="setLoginState(LoginStateEnum.QR_CODE)">
          {{ t('sys.login.qrSignInFormTitle') }}
        </Button>
      </ACol>
      <ACol :md="7" :xs="24" >
        <Button block @click="setLoginState(LoginStateEnum.REGISTER)">
          {{ t('sys.login.registerButton') }}
        </Button>
      </ACol>
    </ARow>

    <Divider v-if="false" class="enter-x">{{ t('sys.login.otherSignIn') }}</Divider>

    <div v-if="false" class="enter-x flex justify-evenly" :class="`${prefixCls}-sign-in-way`">
      <a :href="`${ctxPath}/oauth2/login/qq`" @click="handleOauth2">
        <i class="iconfont iconqq"></i>
      </a>
      <a :href="`${ctxPath}/oauth2/login/gitee`" @click="handleOauth2">
        <i class="iconfont icongitee"></i>
      </a>
      <a :href="`${ctxPath}/oauth2/login/baidu`" @click="handleOauth2">
        <i class="iconfont iconbaidu"></i>
      </a>
      <a :href="`${ctxPath}/oauth2/login/oschina`" @click="handleOauth2">
        <i class="iconfont iconoschina"></i>
      </a>
      <a :href="`${ctxPath}/oauth2/login/github`" @click="handleOauth2">
        <i class="iconfont icongithub"></i>
      </a>
      <a href="https://gitee.com/thinkgem/jeesite-client" target="_blank" style="padding-top: 5px">
        <Icon icon="i-ant-design:windows-filled" size="32" style="vertical-align: middle" />
        <span class="pl-1" style="vertical-align: middle"> {{ t('客户端下载') }}</span>
      </a>
    </div>
  </Form>
</template>
<script lang="ts" setup>
  import { reactive, ref, toRaw, unref, computed, onMounted } from 'vue';

  import { Checkbox, Form, Input, Row, Col, Button, Divider, message } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import LoginFormTitle from './LoginFormTitle.vue';

  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';

  import { useUserStore } from '/@/store/modules/user';
  import { LoginStateEnum, useLoginState, useFormRules, useFormValid } from './useLogin';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useGlobSetting } from '/@/hooks/setting';
  import { userInfoApi } from '/@/api/sys/login';
  // import { onKeyStroke } from '@vueuse/core';
  import { Select } from '/@/components/Form';
  import { corpAdminTreeData } from '/@/api/sys/corpAdmin';
  import { useQuery } from '/@/hooks/web/usePage';
  import { AppLocalePicker, AppDarkModeToggle } from '/@/components/Application';

  const ACol = Col;
  const ARow = Row;
  const FormItem = Form.Item;
  const InputPassword = Input.Password;
  const { t } = useI18n();
  const { showMessage, notification } = useMessage();
  const { prefixCls } = useDesign('login');
  const { ctxPath } = useGlobSetting();
  const userStore = useUserStore();
  const query = useQuery();

  const { setLoginState, getLoginState } = useLoginState();
  const { getFormRules } = useFormRules();

  const formRef = ref();
  const loading = ref(false);
  const rememberMe = ref(false);
  const isValidCodeLogin = ref(false);
  const useCorpModel = ref(false);
  const corpOptions = ref<Recordable[]>([]);
  const demoMode = ref(false);

  const formData = reactive({
    account: 'system',
    password: '',
    validCode: '',
    corpCode: '',
  });

  const { validForm } = useFormValid(formRef);

  //onKeyStroke('Enter', handleLogin);

  const getShow = computed(() => unref(getLoginState) === LoginStateEnum.LOGIN);

  const getValidCodeImg = ref('');

  function refreshValidCodeImg() {
    getValidCodeImg.value =
      ctxPath + '/validCode' + '?__sid=' + userStore.getToken + '&t=' + new Date().getTime();
  }

  // is show jee site valid data.
  function refreshValidCodeStatus(res: Recordable) {
    isValidCodeLogin.value = res.isValidCodeLogin || false;
    if (isValidCodeLogin.value) {
      refreshValidCodeImg();
    }
  }

  onMounted(async () => {
    setTimeout(() => message.destroy());
    try {
      const res = await userInfoApi('none');
      if (res.result == 'true') {
        // 如果已经登录，根据业务需要，是否自动跳转到系统首页
        await userStore.afterLoginAction(res, true);
        return;
      }
      userStore.initPageCache(res);
      refreshValidCodeStatus(res);
      demoMode.value = res.demoMode || false;
      useCorpModel.value = res.useCorpModel || false;
      if (useCorpModel.value) {
        corpOptions.value = (await corpAdminTreeData({ isShowCode: true })).map((item) => ({
          label: item.name,
          value: item.id,
        }));
      }
    } catch (error: any) {
      const err: string = error?.toString?.() ?? '';
      if (error?.code === 'ECONNABORTED' && err.indexOf('timeout of') !== -1) {
        showMessage(t('sys.api.apiTimeoutMessage'));
      } else if (err.indexOf('Network Error') !== -1) {
        showMessage(t('sys.api.networkExceptionMsg'));
      } else if (error?.code === 'ERR_BAD_RESPONSE') {
        showMessage(t('sys.api.apiRequestFailed'));
      }
      console.log(error);
    }
  });

  async function handleSwitchCorp(corpCode) {
    formData.corpCode = corpCode;
  }

  async function handleLogin() {
    try {
      const data = await validForm();
      if (!data) return;
      loading.value = true;
      const res = await userStore.login(
        toRaw({
          password: data.password,
          username: data.account,
          validCode: data.validCode,
          rememberMe: unref(rememberMe.value),
          param_corpCode: formData.corpCode,
        }),
      );
      refreshValidCodeStatus(res);
      if (res.result === 'true') {
        notification.success({
          message: t('sys.login.loginSuccessTitle'),
          description: `${t('sys.login.loginSuccessDesc')}: ${res.user.userName}`,
          duration: 1,
        });
      }
    } catch (error: any) {
      const err: string = error?.toString?.() ?? '';
      if (error?.code === 'ECONNABORTED' && err.indexOf('timeout of') !== -1) {
        showMessage(t('sys.api.apiTimeoutMessage'));
      } else if (err.indexOf('Network Error') !== -1) {
        showMessage(t('sys.api.networkExceptionMsg'));
      } else if (error?.code === 'ERR_BAD_RESPONSE') {
        showMessage(t('sys.api.apiRequestFailed'));
      }
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  function handleOauth2(event: Event) {
    const ele = event.target as HTMLElement;
    let href = ele?.getAttribute('href') as string;
    if (!href) href = ele.parentElement?.getAttribute('href') as string;
    window.location.href = 'https://vue.jeesite.com' + href + '?state=vue';
    event.preventDefault();
  }
</script>
