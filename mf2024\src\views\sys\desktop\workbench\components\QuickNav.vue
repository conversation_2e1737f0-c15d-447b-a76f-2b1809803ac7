<template>
  <Card title="快捷导航" v-bind="$attrs">
    <vuedraggable
      :list="navItems"
      v-if="navItems && navItems.length > 0"
      class="image-list"
      animation="300"
      @end="onEnd"
      item-key="id"
      @change="onChange"
    >
      <!-- <template v-for="item in navItems" :key="item"> -->
      <template #item="{ element: item }">
        <CardGrid @contextmenu.prevent="handleContext($event, item)" @click="handleClick(item)">
          <div>
            <span class="flex flex-col items-center">
              <Icon :icon="item.menu.menuIcon" :color="item.menu.menuColor" size="36" />
              <span class="text-md mt-2">{{ item.menu.menuName }}</span>
            </span>
          </div>
        </CardGrid>
      </template>
    </vuedraggable>
  </Card>
</template>
<script lang="ts" setup>
  import { Card } from 'ant-design-vue';
  // import { navItems } from './data';
  import { Icon } from '/@/components/Icon';
  import vuedraggable from 'vuedraggable';
  import { useContextMenu } from '/@/hooks/web/useContextMenu';
  import { CollapseContainer } from '/@/components/Container';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { findUserNav, quickNavDelete, changeSorted } from '/@/api/sys/quickNav';
  import { ref, watch, onMounted } from 'vue';
  import { usePermissionStore } from '/@/store/modules/permission';
  import { useRouter } from 'vue-router';
  const useStore = usePermissionStore();
  const router = useRouter();
  const navItems = ref([]);

  watch(
    () => useStore.getQuickNavList,
    (val, prevVal) => {
      navItems.value = val;
    },
    {
      immediate: true,
    },
  );

  onMounted(async () => {
    let res = await findUserNav();
    await useStore.setQuickNavList(res.list);
  });

  findUserNav().then((res) => {
    console.log(res, 'res==findUserNav');
    navItems.value = res.list;
  });
  // navItems 变成响应式数据
  const CardGrid = Card.Grid;
  const handleClick = (item: any) => {
    // console.log('click',item);
    // 跳转
    router.push(item.menu.menuHref);

    // if (item.menu.url) {
    //   useRouter.push(item.url);
    // }
  };
  const onEnd = async () => {
    await changeSorted(navItems.value);
    let res = await findUserNav();
    await useStore.setQuickNavList(res.list);
  };

  // 鼠标右键点击事件start
  const [createContextMenu] = useContextMenu();
  const { createMessage } = useMessage();
  function handleContext(e: MouseEvent, item: any) {
    createContextMenu({
      event: e,
      items: [
        {
          label: '删除' + e.srcElement.innerText,
          icon: 'ant-design:delete-outlined',
          handler: () => {
            quickNavDelete({ id: item.id }).then(async (res) => {
              if (res.result == 'true') {
                createMessage.success('删除' + e.srcElement.innerText + '成功');
                let res = await findUserNav();
                await useStore.setQuickNavList(res.list);
              }
            });
          },
        },
      ],
    });
  }
  // 鼠标右键点击事件end

  const onChange = (e: any) => {
    console.log(e, 'onChange');
  };
</script>
<style scoped>
.image-list{
  display: flex;
  flex-wrap: wrap; 
}
</style>