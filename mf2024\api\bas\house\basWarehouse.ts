/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page, TreeDataModel } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface BasWarehouse extends BasicModel<BasWarehouse> {
  cwhcode?: string; // 仓库编码
  cwhname?: string; // 仓库名称
  cwhaddress?: string; // 仓库地址
  cwhphone?: string; // 电话
  cwhperson?: string; // 负责人
  bproxywh?: string; // 是否代管仓
  bwhpos?: string; // 是否货位管理
  multiplepos?: string; // 多货位
  deftpos?: string; // 默认货位
  remarks?: string; // 备注
}

export const basWarehouseList = (params?: BasWarehouse | any) =>
  defHttp.get<BasWarehouse>({ url: adminPath + '/bas/house/basWarehouse/list', params });

export const basWarehouseListData = (params?: BasWarehouse | any) =>
  defHttp.post<Page<BasWarehouse>>({ url: adminPath + '/bas/house/basWarehouse/listData', params });

export const basWarehouseForm = (params?: BasWarehouse | any) =>
  defHttp.get<BasWarehouse>({ url: adminPath + '/bas/house/basWarehouse/form', params });

export const basWarehouseSave = (params?: any, data?: BasWarehouse | any) =>
  defHttp.postJson<BasWarehouse>({ url: adminPath + '/bas/house/basWarehouse/save', params, data });

export const basWarehouseDelete = (params?: BasWarehouse | any) =>
  defHttp.get<BasWarehouse>({ url: adminPath + '/bas/house/basWarehouse/delete', params });

export const basWarehousetreeData = (params?: BasWarehouse | any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/bas/house/basWarehouse/treeData', params });
