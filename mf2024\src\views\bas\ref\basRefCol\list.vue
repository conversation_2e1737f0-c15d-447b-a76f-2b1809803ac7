<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="default" @click="handleExport()">
          <Icon icon="ant-design:download-outlined" /> {{ t('导出') }}
        </a-button>
        <a-button type="default" @click="handleImport()">
          <Icon icon="ant-design:upload-outlined" /> {{ t('导入') }}
        </a-button>
        <a-button type="primary" @click="handleForm({})" v-auth="'bas:ref:basRefCol:edit'">
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.refType }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
    <FormImport @register="registerImportModal" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsBasRefBasRefColList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { basRefColDelete, basRefColListData } from '/@/api/bas/ref/basRefCol';
  import { useDrawer } from '/@/components/Drawer';
  import { useModal } from '/@/components/Modal';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import FormImport from './formImport.vue';
  import { basRefTypeTreeData } from '/@/api/bas/ref/basRefType';

  const props = defineProps({
    treeCode: String,
    treeName: String,
  });

  const { t } = useI18n('bas.ref.basRefCol');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: router.currentRoute.value.meta.title || t('转换栏目管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('转换类型'),
        field: 'refType',
        fieldLabel: 'basRefType.name',
        component: 'TreeSelect',
        componentProps: {
          canSelectParent: false,
          api: basRefTypeTreeData,
        },
      },
      {
        label: t('当前KEY'),
        field: 'ckey',
        component: 'Input',
      },
      {
        label: t('当前名称'),
        field: 'cname',
        component: 'Input',
      },
      {
        label: t('外部KEY'),
        field: 'extkey',
        component: 'Input',
      },
      {
        label: t('外部名称'),
        field: 'extname',
        component: 'Input',
      },
      {
        label: t('备注说明'),
        field: 'remarks',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('转换类型'),
      dataIndex: 'refType',
      key: 'a.ref_type',
      sorter: true,
      width: 150,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('当前KEY'),
      dataIndex: 'ckey',
      key: 'a.ckey',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('当前名称'),
      dataIndex: 'cname',
      key: 'a.cname',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('外部KEY'),
      dataIndex: 'extkey',
      key: 'a.extkey',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('外部名称'),
      dataIndex: 'extname',
      key: 'a.extname',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('备注说明'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: t('类型描述'),
      dataIndex: 'basRefType.name',
      key: 'ref.name',
      sorter: true,
      width: 140,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑转换栏目'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'bas:ref:basRefCol:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除转换栏目'),
        popConfirm: {
          title: t('是否确认删除转换栏目'),
          confirm: handleDelete.bind(this, { id: record.id }),
        },
        auth: 'bas:ref:basRefCol:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm }] = useTable({
    api: basRefColListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  watch(
    () => props.treeCode,
    async () => {
      await getForm().setFieldsValue({
        refType: props.treeCode,
        'basRefType.name': props.treeName,
      });
      reload();
    },
  );

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleExport() {
    const { ctxAdminPath } = useGlobSetting();
    downloadByUrl({
      url: ctxAdminPath + '/bas/ref/basRefCol/exportData',
      target: '_self',
    });
  }

  const [registerImportModal, { openModal: importModal }] = useModal();

  function handleImport() {
    importModal(true, {});
  }

  async function handleDelete(record: Recordable) {
    const res = await basRefColDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleSuccess() {
    reload();
  }
</script>
