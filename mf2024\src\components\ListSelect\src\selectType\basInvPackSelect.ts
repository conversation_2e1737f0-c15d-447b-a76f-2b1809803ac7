import { useI18n } from '/@/hooks/web/useI18n';
import { BasicColumn, BasicTableProps, FormProps } from '/@/components/Table';
import { basInvPackListData } from '/@/api/bas/inv/pack/basInvPack';

const { t } = useI18n('sys.empUser');

const modalProps = {
  title: t('报装规格管理'),
};

const searchForm: FormProps = {
  baseColProps: { lg: 6, md: 8 },
  labelWidth: 100,
  schemas: [
    {
      label: t('存货编码'),
      field: 'invCode',
      component: 'Input',
    },
    {
      label: t('存货名称'),
      field: 'basInv.invName',
      component: 'Input',
    },
  ],
};

const tableColumns: BasicColumn[] = [
  {
    title: t('存货编码'),
    dataIndex: 'invCode',
    width: 100,
  },
  {
    title: t('存货名称'),
    dataIndex: 'basInv.invName',
    width: 100,
  },
  {
    title: t('包装名称'),
    dataIndex: 'packName',
    width: 100,
  },
  {
    title: t('托盘容量'),
    dataIndex: 'packSize',
    width: 100,
  },
  {
    title: t('单间容量'),
    dataIndex: 'pieceQty',
    width: 100,
  },

  {
    title: t('规格型号'),
    dataIndex: 'basInv.cinvStd',
    width: 100,
  },
  {
    title: t('单位'),
    dataIndex: 'basInv.unitName',
    width: 130,
  },
];

const tableProps: BasicTableProps = {
  api: basInvPackListData,
  beforeFetch: (params) => {
    params['isAll'] = true;
    return params;
  },
  columns: tableColumns,
  formConfig: searchForm,
  rowKey: 'id',
};

export default {
  modalProps,
  tableProps,
  itemCode: 'invCode',
  itemName: 'invName',
  isShowCode: true,
};
