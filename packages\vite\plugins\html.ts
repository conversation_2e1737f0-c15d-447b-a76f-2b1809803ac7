/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { type PluginOption } from 'vite';
import { createHtmlPlugin } from 'vite-plugin-html';

/**
 * Plugin to minimize and use ejs template syntax in index.html.
 * https://github.com/anncwb/vite-plugin-html
 */
export function configHtmlPlugin(isBuild: boolean): PluginOption {
  return createHtmlPlugin({
    minify: isBuild,
  });
}
