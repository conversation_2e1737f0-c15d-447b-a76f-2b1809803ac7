<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <!-- <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'wms:so:outdata:wmsSoOutRds:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template> -->
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.parentId }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsWmsSoOutdataWmsSoOutRdsList">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { wmsSoOutRdsDelete, wmsSoOutRdsListData } from '/@/api/wms/so/outdata/wmsSoOutRds';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('wms:so.outdata.wmsSoOutRds');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('装车发出记录管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('发货车次'),
        field: 'fhH.djNo',
        component: 'Input',
      },
      {
        label: t('发货单号'),
        field: 'fhC.soCode',
        component: 'Input',
      },
      {
        label: t('车牌'),
        field: 'fhH.carNo',
        component: 'Input',
      },
      {
        label: t('存货编码'),
        field: 'invCode',
        component: 'Input',
      },
      {
        label: t('存货名称'),
        field: 'basInv.invName',
        component: 'Input',
      },
      {
        label: t('仓库'),
        field: 'fhC.cwhname',
        component: 'Input',
      },
      {
        label: t('货位'),
        field: 'posCode',
        component: 'Input',
      },
      {
        label: t('批次'),
        field: 'cbatch',
        component: 'Input',
      },
      {
        label: t('客户'),
        field: 'fhC.cusName',
        component: 'Input',
      },
      {
        label: t('客户编码'),
        field: 'fhC.cusCode',
        component: 'Input',
      },
      {
        label: t('制单人'),
        field: 'createByName',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('发货单号'),
      dataIndex: 'fhC.soCode',
      key: 'fhc.so_code',
      sorter: true,
      width: 110,
      align: 'left',
    },
    {
      title: t('发货车次'),
      dataIndex: 'fhH.djNo',
      key: 'fhH.dj_no',
      sorter: true,
      width: 110,
      align: 'left',
    },
    {
      title: t('车牌'),
      dataIndex: 'fhH.carNo',
      key: 'fhH.carNo',
      // sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('批次'),
      dataIndex: 'cbatch',
      key: 'a.cbatch',
      sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: t('存货编码'),
      dataIndex: 'invCode',
      key: 'a.inv_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('存货名称'),
      dataIndex: 'basInv.invName',
      key: 'basinv.inv_name',
      // sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: t('规格型号'),
      dataIndex: 'basInv.invStd',
      key: 'a.inv_code',
      // sorter: true,
      width: 90,
      align: 'left',
    },
    {
      title: t('单位'),
      dataIndex: 'basInv.unitName',
      key: 'a.inv_code',
      // sorter: true,
      width: 50,
      align: 'center',
    },
    {
      title: t('仓库'),
      dataIndex: 'fhC.cwhname',
      key: 'a.pos_code',
      // sorter: true,
      width: 80,
      align: 'left',
    },
    {
      title: t('货位'),
      dataIndex: 'posCode',
      key: 'a.pos_code',
      sorter: true,
      width: 110,
      align: 'left',
    },
    {
      title: t('数量'),
      dataIndex: 'iqty',
      key: 'a.iqty',
      sorter: true,
      width: 80,
      align: 'right',
    },
    {
      title: t('件数'),
      dataIndex: 'inum',
      key: 'a.inum',
      sorter: true,
      width: 80,
      align: 'right',
    },
    {
      title: t('换算率'),
      dataIndex: 'ichangeRate',
      key: 'a.ichange_rate',
      sorter: true,
      width: 90,
      align: 'right',
    },
    {
      title: t('品级'),
      dataIndex: 'rgrade',
      key: 'a.rgrade',
      sorter: true,
      width: 90,
      align: 'left',
    },
    {
      title: t('合同号'),
      dataIndex: 'fhC.contractCode',
      key: 'fhC.contractCode',
      // sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('客户'),
      dataIndex: 'fhC.cusName',
      key: 'fhC.cusName',
      // sorter: true,
      width: 180,
      align: 'left',
    },
    {
      title: t('客户编码'),
      dataIndex: 'fhC.cusCode',
      key: 'fhC.cusCode',
      // sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('制单人'),
      dataIndex: 'createByName',
      key: 'a.create_by_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('制单时间'),
      dataIndex: 'createDate',
      key: 'a.create_date',
      sorter: true,
      width: 150,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除装车发出记录'),
        popConfirm: {
          title: t('是否确认删除装车发出记录'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'wms:so:outdata:wmsSoOutRds:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload }] = useTable({
    api: wmsSoOutRdsListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { id: record.id };
    const res = await wmsSoOutRdsDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
