<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable" @fetch-success="fetchSuccess">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button @click="expandAll" :title="t('展开一级')">
          <Icon icon="i-bi:chevron-double-down" /> {{ t('展开') }}
        </a-button>
        <a-button @click="collapseAll" :title="t('折叠全部')">
          <Icon icon="i-bi:chevron-double-up" /> {{ t('折叠') }}
        </a-button>
        <a-button type="default" @click="handleExport()">
          <Icon icon="i-ant-design:download-outlined" /> {{ t('导出') }}
        </a-button>
        <a-button type="default" @click="handleImport()">
          <Icon icon="i-ant-design:upload-outlined" /> {{ t('导入') }}
        </a-button>
        <a-button
          type="primary"
          @click="handleForm({})"
          v-auth="'bankdirectlink:useoffunds:billmanagerUseOfFunds:edit'"
        >
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ code: record.code })">
          {{ record.name }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
    <FormImport @register="registerImportModal" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsBankdirectlinkUseoffundsBillmanagerUseOfFundsList">
  import { unref, watch, nextTick } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import {
    billmanagerUseOfFundsDelete,
    billmanagerUseOfFundsListData,
  } from '/@/api/billmanager/bankdirectlink/useoffunds/billmanagerUseOfFunds';
  import {
    billmanagerUseOfFundsDisable,
    billmanagerUseOfFundsEnable,
  } from '/@/api/billmanager/bankdirectlink/useoffunds/billmanagerUseOfFunds';
  import { useDrawer } from '/@/components/Drawer';
  import { useModal } from '/@/components/Modal';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import FormImport from './formImport.vue';

  const props = defineProps({
    treeCode: String,
  });

  const { t } = useI18n('bankdirectlink.useoffunds.billmanagerUseOfFunds');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('资金用途管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('编码'),
        field: 'code',
        component: 'Input',
      },
      {
        label: t('名称'),
        field: 'name',
        component: 'Input',
      },
      {
        label: t('状态'),
        field: 'status',
        component: 'Select',
        componentProps: {
          dictType: 'sys_search_status',
          allowClear: true,
          onChange: handleSuccess,
        },
      },
      {
        label: t('备注'),
        field: 'remarks',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('编码'),
      dataIndex: 'code',
      width: 100,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('名称'),
      dataIndex: 'name',
      width: 230,
      align: 'left',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      width: 130,
      align: 'center',
      dictType: 'sys_search_status',
    },
    {
      title: t('更新日期'),
      dataIndex: 'updateDate',
      width: 130,
      align: 'center',
    },
    {
      title: t('备注'),
      dataIndex: 'remarks',
      width: 130,
      align: 'left',
    },
    {
      title: t('排序号'),
      dataIndex: 'treeSort',
      width: 130,
      align: 'center',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑资金用途'),
        onClick: handleForm.bind(this, { code: record.code }),
        auth: 'bankdirectlink:useoffunds:billmanagerUseOfFunds:edit',
      },
      {
        icon: 'i-ant-design:stop-outlined',
        color: 'error',
        title: t('停用资金用途'),
        popConfirm: {
          title: t('是否确认停用资金用途'),
          confirm: handleDisable.bind(this, record),
        },
        auth: 'bankdirectlink:useoffunds:billmanagerUseOfFunds:edit',
        ifShow: () => record.status === '0',
      },
      {
        icon: 'i-ant-design:check-circle-outlined',
        color: 'success',
        title: t('启用资金用途'),
        popConfirm: {
          title: t('是否确认启用资金用途'),
          confirm: handleEnable.bind(this, record),
        },
        auth: 'bankdirectlink:useoffunds:billmanagerUseOfFunds:edit',
        ifShow: () => record.status === '2',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除资金用途'),
        popConfirm: {
          title: t('是否确认删除资金用途'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'bankdirectlink:useoffunds:billmanagerUseOfFunds:edit',
      },
      {
        icon: 'i-fluent:add-circle-24-regular',
        title: t('新增下级资金用途'),
        onClick: handleForm.bind(this, {
          parentCode: record.id,
          parentName: record.name,
        }),
        auth: 'bankdirectlink:useoffunds:billmanagerUseOfFunds:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, expandAll, collapseAll, expandCollapse, getForm }] = useTable({
    api: billmanagerUseOfFundsListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    isTreeTable: true,
    pagination: false,
    canResize: true,
  });

  watch(
    () => props.treeCode,
    async () => {
      await getForm().setFieldsValue({
        code: props.treeCode,
      });
      reload();
    },
  );

  function fetchSuccess() {
    if (props.treeCode) {
      nextTick(expandAll);
    }
  }

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleExport() {
    const { ctxAdminPath } = useGlobSetting();
    downloadByUrl({
      url: ctxAdminPath + '/bankdirectlink/useoffunds/billmanagerUseOfFunds/exportData',
      params: getForm().getFieldsValue(),
    });
  }

  const [registerImportModal, { openModal: importModal }] = useModal();

  function handleImport() {
    importModal(true, {});
  }

  async function handleDisable(record: Recordable) {
    const params = { code: record.code };
    const res = await billmanagerUseOfFundsDisable(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  async function handleEnable(record: Recordable) {
    const params = { code: record.code };
    const res = await billmanagerUseOfFundsEnable(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  async function handleDelete(record: Recordable) {
    const params = { code: record.code };
    const res = await billmanagerUseOfFundsDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
