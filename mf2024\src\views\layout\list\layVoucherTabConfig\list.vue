<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleForm({ viewCode: props.colpop.code })" v-auth="'layout:edit'">
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.viewCode }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherTabConfigList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { layVoucherTabConfigDelete, layVoucherTabConfigListData } from '../../../../api/layout/list/layVoucherTabConfig';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('layout.layVoucherTabConfig');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: t('表格全局配置管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 8, md: 8 },
    labelWidth: 120,
    schemas: [
      {
        label: t('布局标志'),
        field: 'viewCode',
        component: 'Input',
      },
      {
        label: t('点击是否选中行'),
        field: 'clickToRowSelect',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否显示表格设置工具'),
        field: 'showTableSetting',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否显示表格设置工具配置'),
        field: 'tableSetting',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否显示斑马纹'),
        field: 'striped',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否取消表格默认padding'),
        field: 'inset',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否自动生成key'),
        field: 'autoCreateKey',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否显示合计行'),
        field: 'showSummary',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否显示表格'),
        field: 'emptyDataIsShowTable',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否树表'),
        field: 'isTreeTable',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否延迟加载表格数据'),
        field: 'immediate',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否启用搜索表单'),
        field: 'useSearchForm',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否显示行号'),
        field: 'showIndexColumn',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('文本超过宽度是否显示省略号'),
        field: 'ellipsis',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否自适应高度'),
        field: 'canResize',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('切换页面是否重置勾选状态'),
        field: 'clearSelectOnPageChange',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('请求接口'),
        field: 'api',
        component: 'Input',
      },
      {
        label: t('是否显示表格边框'),
        field: 'bordered',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否显示表格loading状态'),
        field: 'loading',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('表格标题'),
        field: 'title',
        component: 'Input',
      },
      {
        label: t('表格右侧温馨提示'),
        field: 'titleHelpMessage',
        component: 'Input',
      },
      {
        label: t('表格最大高度'),
        field: 'maxHeight',
        component: 'Input',
      },
      {
        label: t('自适应高度'),
        field: 'resizeHeightOffset',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('布局标志'),
      dataIndex: 'viewCode',
      key: 'a.view_code',
      sorter: true,
      width: 130,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('点击是否选中行'),
      dataIndex: 'clickToRowSelect',
      key: 'a.clicktorowselect',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否显示表格设置工具'),
      dataIndex: 'showTableSetting',
      key: 'a.showtablesetting',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否显示表格设置工具配置'),
      dataIndex: 'tableSetting',
      key: 'a.tablesetting',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否显示斑马纹'),
      dataIndex: 'striped',
      key: 'a.striped',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否取消表格默认padding'),
      dataIndex: 'inset',
      key: 'a.inset',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否自动生成key'),
      dataIndex: 'autoCreateKey',
      key: 'a.autocreatekey',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否显示合计行'),
      dataIndex: 'showSummary',
      key: 'a.showsummary',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否显示表格'),
      dataIndex: 'emptyDataIsShowTable',
      key: 'a.emptydataisshowtable',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否树表'),
      dataIndex: 'isTreeTable',
      key: 'a.istreetable',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否延迟加载表格数据'),
      dataIndex: 'immediate',
      key: 'a.immediate',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否启用搜索表单'),
      dataIndex: 'useSearchForm',
      key: 'a.usesearchform',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否显示行号'),
      dataIndex: 'showIndexColumn',
      key: 'a.showindexcolumn',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('文本超过宽度是否显示省略号'),
      dataIndex: 'ellipsis',
      key: 'a.ellipsis',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否自适应高度'),
      dataIndex: 'canResize',
      key: 'a.canresize',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('切换页面是否重置勾选状态'),
      dataIndex: 'clearSelectOnPageChange',
      key: 'a.clearselectonpagechange',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('请求接口'),
      dataIndex: 'api',
      key: 'a.api',
      sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: t('是否显示表格边框'),
      dataIndex: 'bordered',
      key: 'a.bordered',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否显示表格loading状态'),
      dataIndex: 'loading',
      key: 'a.loading',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('表格标题'),
      dataIndex: 'title',
      key: 'a.title',
      sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: t('表格右侧温馨提示'),
      dataIndex: 'titleHelpMessage',
      key: 'a.titlehelpmessage',
      sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: t('表格最大高度'),
      dataIndex: 'maxHeight',
      key: 'a.maxheight',
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: t('自适应高度'),
      dataIndex: 'resizeHeightOffset',
      key: 'a.resizeheightoffset',
      sorter: true,
      width: 150,
      align: 'right',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑表格全局配置'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'layout:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除表格全局配置'),
        popConfirm: {
          title: t('是否确认删除表格全局配置'),
          confirm: handleDelete.bind(this, { id: record.id }),
        },
        auth: 'layout:edit',
      },
    ],
  };

  const props = defineProps({
     colpop: { type: Object, default: {} },
  });
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm }] = useTable({
    api: layVoucherTabConfigListData,
    beforeFetch: (params) => {
      params.viewCode = props.colpop.code;
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: false,
    canResize: true,
  });
  watch(
    () => props.colpop,
    () => {
      reload();
    },
    // { immediate: true },
  );

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const res = await layVoucherTabConfigDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleSuccess() {
    reload();
  }
</script>
