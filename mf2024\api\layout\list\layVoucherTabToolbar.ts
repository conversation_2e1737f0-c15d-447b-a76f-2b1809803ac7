/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface LayVoucherTabToolbar extends BasicModel<LayVoucherTabToolbar> {
  viewCode?: string; // 布局标志
  vouchCode?: string; // 基础单据
  btnType?: string; // 按钮类型
  auth?: string; // 权限标识
  icon?: string; // 按钮图标
  title?: string; // 按钮标题
}

export const layVoucherTabToolbarList = (params?: LayVoucherTabToolbar | any) =>
  defHttp.get<LayVoucherTabToolbar>({ url: adminPath + '/layout/listToolBar/list', params });

export const layVoucherTabToolbarListData = (params?: LayVoucherTabToolbar | any) =>
  defHttp.post<Page<LayVoucherTabToolbar>>({ url: adminPath + '/layout/listToolBar/listData', params });

export const layVoucherTabToolbarForm = (params?: LayVoucherTabToolbar | any) =>
  defHttp.get<LayVoucherTabToolbar>({ url: adminPath + '/layout/listToolBar/form', params });

export const layVoucherTabToolbarSave = (params?: any, data?: LayVoucherTabToolbar | any) =>
  defHttp.postJson<LayVoucherTabToolbar>({ url: adminPath + '/layout/listToolBar/save', params, data });

export const layVoucherTabToolbarDelete = (params?: LayVoucherTabToolbar | any) =>
  defHttp.get<LayVoucherTabToolbar>({ url: adminPath + '/layout/listToolBar/delete', params });
