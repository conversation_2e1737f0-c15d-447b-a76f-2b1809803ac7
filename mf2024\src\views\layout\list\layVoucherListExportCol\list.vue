<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleForm({ code: props.colpop.code })"
          v-auth="'layout:export:layVoucherListExportCol:edit'"
        >
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.code }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutExportLayVoucherListExportColList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import {
    layVoucherListExportColDelete,
    layVoucherListExportColListData,
  } from '/@/api/layout/export/layVoucherListExportCol';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('layout.export.layVoucherListExportCol');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: router.currentRoute.value.meta.title || t('导入/出配置管理'),
  };

  const props = defineProps({
    colpop: { type: Object, default: {} },
  });

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('列表编码'),
        field: 'code',
        component: 'Input',
      },
      {
        label: t('字段标题'),
        field: 'exTitle',
        component: 'Input',
      },
      {
        label: t('字段名'),
        field: 'exAttrName',
        component: 'Input',
      },
      {
        label: t('字段排序'),
        field: 'exSort',
        component: 'Input',
      },
      {
        label: t('对齐方式'),
        field: 'exAlign',
        component: 'Select',
        componentProps: {
          dictType: 'lay_ex_align',
          allowClear: true,
        },
      },
      {
        label: t('字段类型'),
        field: 'exType',
        component: 'Select',
        componentProps: {
          dictType: 'lay_ex_type',
          allowClear: true,
        },
      },
      {
        label: t('导出列宽'),
        field: 'exWidth',
        component: 'Input',
      },
      {
        label: t('字符个数'),
        field: 'exWords',
        component: 'Input',
      },
      {
        label: t('字段索引'),
        field: 'exColumn',
        component: 'Input',
      },
      {
        label: t('字典类型'),
        field: 'exDictType',
        component: 'Input',
      },
      {
        label: t('反射类型'),
        field: 'exFieldType',
        component: 'Input',
      },
      {
        label: t('数值格式'),
        field: 'exDataFormat',
        component: 'Input',
      },
      {
        label: t('字段归属'),
        field: 'groups',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('列表编码'),
      dataIndex: 'code',
      key: 'a.code',
      sorter: true,
      width: 230,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('字段标题'),
      dataIndex: 'exTitle',
      key: 'a.ex_title',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('字段名'),
      dataIndex: 'exAttrName',
      key: 'a.ex_attr_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('字段排序'),
      dataIndex: 'exSort',
      key: 'a.ex_sort',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('对齐方式'),
      dataIndex: 'exAlign',
      key: 'a.ex_align',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'lay_ex_align',
    },
    {
      title: t('字段类型'),
      dataIndex: 'exType',
      key: 'a.ex_type',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'lay_ex_type',
    },
    {
      title: t('导出列宽'),
      dataIndex: 'exWidth',
      key: 'a.ex_width',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('字符个数'),
      dataIndex: 'exWords',
      key: 'a.ex_words',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('字段索引'),
      dataIndex: 'exColumn',
      key: 'a.ex_column',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('字典类型'),
      dataIndex: 'exDictType',
      key: 'a.ex_dict_type',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('反射类型'),
      dataIndex: 'exFieldType',
      key: 'a.ex_field_type',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('数值格式'),
      dataIndex: 'exDataFormat',
      key: 'a.ex_data_format',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('字段归属'),
      dataIndex: 'groups',
      key: 'a.groups',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑导入/出配置'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'layout:export:layVoucherListExportCol:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除导入/出配置'),
        popConfirm: {
          title: t('是否确认删除导入/出配置'),
          confirm: handleDelete.bind(this, { id: record.id }),
        },
        auth: 'layout:export:layVoucherListExportCol:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm }] = useTable({
    api: layVoucherListExportColListData,
    beforeFetch: (params) => {
      params.code = props.colpop.code;
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: false,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const res = await layVoucherListExportColDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleSuccess() {
    reload();
  }
</script>
