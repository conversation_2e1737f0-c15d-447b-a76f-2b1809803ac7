<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'bankdirectlink:payapply:billManagerPayApplyC1:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsBankdirectlinkPayapplyBillManagerPayApplyC1Form">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BillManagerPayApplyC1, billManagerPayApplyC1Save, billManagerPayApplyC1Form } from '/@/api/billmanager/bankdirectlink/payapply/billManagerPayApplyC1';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('bankdirectlink.payapply.billManagerPayApplyC1');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<BillManagerPayApplyC1>({} as BillManagerPayApplyC1);

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增付款申请单拆分表C1') : t('编辑付款申请单拆分表C1'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('明细ID'),
      field: 'parentId',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
    },
    {
      label: t('科目'),
      field: 'subject',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
    },
    {
      label: t('支付类型'),
      field: 'footType',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
    },
    {
      label: t('原币金额'),
      field: 'orgAmount',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ required: true }, { pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('拆分金额'),
      field: 'sumAmount',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await billManagerPayApplyC1Form(data);
    record.value = (res.billManagerPayApplyC1 || {}) as BillManagerPayApplyC1;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await billManagerPayApplyC1Save(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
