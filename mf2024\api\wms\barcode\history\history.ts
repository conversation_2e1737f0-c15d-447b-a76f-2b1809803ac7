/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page, TreeDataModel } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface barcodeHistory extends BasicModel<barcodeHistory> {
  pdfName?: string; // 文件名称
  ddate?: string; // 打印日期
  createByName?: string; // 打印人
  updateDate?: string; // 最后修改日期
  ctype?: string; // 文件来源
  remarks?: string; // 打印标记
}

export const barcodeHistoryList = (params?: barcodeHistory | any) =>
  defHttp.get<barcodeHistory>({ url: adminPath + '/barcode/history/listData', params });
