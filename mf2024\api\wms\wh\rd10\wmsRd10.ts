/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WmsRd10 extends BasicModel<WmsRd10> {
  djno?: string; // 单据编号
  ddate?: string; // 单据日期
  djStatus?: string; // 单据状态
  whCode?: string; // 仓库编码
  invCode?: string; // 存货编码
  checkBy?: string; // 检验员
  teamClass?: string; // 班次
  cteam?: string; // 班组
  cgrade?: string; // 品级
  iqty?: number; // 数量
  inum?: number; // 件数
  cbatch?: string; // 批次
  ichangeRate?: number; // 换算率
  sumQty?: number; // 累计入库数量
  sumNum?: number; // 累计入库件数
  maxTpSeq?: number; // 最大托盘序号
  createByName?: string; // 制单人
  updateByName?: string; // 修改人
  packSize?: number; // 托盘容量
  pieceQty?: number; // 单件容量
  packName?: string; // 包装名称
}

export const wmsRd10List = (params?: WmsRd10 | any) =>
  defHttp.get<WmsRd10>({ url: adminPath + '/wms/wh/rd10/wmsRd10/list', params });

export const wmsRd10ListData = (params?: WmsRd10 | any) =>
  defHttp.post<Page<WmsRd10>>({ url: adminPath + '/wms/wh/rd10/wmsRd10/listData', params });

export const wmsRd10Form = (params?: WmsRd10 | any) =>
  defHttp.get<WmsRd10>({ url: adminPath + '/wms/wh/rd10/wmsRd10/form', params });

export const wmsRd10Save = (params?: any, data?: WmsRd10 | any) =>
  defHttp.postJson<WmsRd10>({ url: adminPath + '/wms/wh/rd10/wmsRd10/save', params, data });

export const wmsRd10Delete = (params?: WmsRd10 | any) =>
  defHttp.get<WmsRd10>({ url: adminPath + '/wms/wh/rd10/wmsRd10/delete', params });
// /wms/wh/rd10/wmsRd10/createBatch
export const wmsRd10CreateBatch = (params?: any) =>
  defHttp.get<WmsRd10>({ url: adminPath + '/wms/wh/rd10/wmsRd10/createBatch', params });

export const wmsBatchNotify = (params?: WmsRd10 | any) =>
  defHttp.post<WmsRd10>({ url: adminPath + '/wms/wh/rd10/wmsRd10/batchNotify', params });

export const wmsRd10Close = (params?: WmsRd10 | any) =>
  defHttp.post<WmsRd10>({ url: adminPath + '/wms/wh/rd10/wmsRd10/close', params });
