/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface LayVoucherFields extends BasicModel<LayVoucherFields> {
  vouchCode?: string; // 单据标志
  primaryKey?: string; // 是否主键
  notNull?: string; // 是否必填
  isUnique?: string; // 是否唯一
  physicalName?: string; // 物理名称
  logicalName?: string; // 逻辑名称
  type?: string; // 类型
  length?: number; // 字段长度
  decimal?: number; // 小数位数
  description?: string; // 描述
  defaultValue?: string; // 默认值
  isInsert?: number; // 是否插入字段
  isUpdate?: number; // 是否更新字段
  isUpdateForce?: number; // 是否强制更新
  isQuery?: number; // 是否查询字段
  queryType?: string; // 查询类型
}

export const layVoucherFieldsList = (params?: LayVoucherFields | any) =>
  defHttp.get<LayVoucherFields>({ url: adminPath + '/layout/layVoucherFields/list', params });

export const layVoucherFieldsListData = (params?: LayVoucherFields | any) =>
  defHttp.post<Page<LayVoucherFields>>({ url: adminPath + '/layout/layVoucherFields/listData', params });

export const layVoucherFieldsForm = (params?: LayVoucherFields | any) =>
  defHttp.get<LayVoucherFields>({ url: adminPath + '/layout/layVoucherFields/form', params });

export const layVoucherFieldsSave = (params?: any, data?: LayVoucherFields | any) =>
  defHttp.postJson<LayVoucherFields>({ url: adminPath + '/layout/layVoucherFields/save', params, data });

export const layVoucherFieldsDelete = (params?: LayVoucherFields | any) =>
  defHttp.get<LayVoucherFields>({ url: adminPath + '/layout/layVoucherFields/delete', params });
