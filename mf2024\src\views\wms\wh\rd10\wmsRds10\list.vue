<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button primary @click="handleInvPrint({})" v-auth="'wms.wh:rd10:wmsRd10:view'">
          <Icon icon="i-ant-design:printer-outlined" /> {{ t('产品标签') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id , djno : record.createDate })">
          {{ record.createDate }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
    <BpmRuntimeTrace @register="registerTraceModal" />
    <barSizeTypeForm @register="registerBarSizeTypeModal" />
  </div>
</template>
<script lang="ts" setup name="ViewsWmsWhRd10WmsRds10List">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  // import { wmsRd10Delete, wmsRd10ListData } from '/@/api/wms/wh/rd10/wmsRd10';
  import { wmsRds10ListData } from '/@/api/wms/wh/rd10/wmsRds10';
  import { useDrawer } from '/@/components/Drawer';
  import { useModal } from '/@/components/Modal';
  import { BpmRuntimeTrace } from '/@/components/Bpm';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import { useGlobSetting } from '/@/hooks/setting';
  import barSizeTypeForm from '/@/views/wms/barcode/print/barSizeTypeForm.vue';
  import { BarTypeEnum } from '/@/enums/defEnum';

  const { t } = useI18n('wms/wh.rd10.wmsRds10');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const { ctxPath } = useGlobSetting();
  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: router.currentRoute.value.query.tabTitle || t('入库明细'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('入库时间'),
        field: 'createDate',
        component: 'RangePicker',
        componentProps: {
        },
      },
      {
        label: t('申请日期'),
        field: 'parent.ddate',
        component: 'RangePicker',
        componentProps: {
        },
      },
      // {
      //   label: t('单据状态'),
      //   field: 'parent.djStatus',
      //   component: 'Select',
      //   componentProps: {
      //     dictType: 'wms_rd10_status',
      //     allowClear: true,
      //   },
      // },
      {
        label: t('申请单号'),
        field: 'parent.djno',
        component: 'Input',
      },
      {
        label: t('仓库编码'),
        field: 'parent.whCode',
        component: 'Input',
      },

      {
        label: t('存货编码'),
        field: 'invCode',
        component: 'Input',
      },
      {
        label: t('存货名称'),
        field: 'basInv.invName',
        component: 'Input',
      },
      {
        label: t('规格型号'),
        field: 'basInv.invStd',
        component: 'Input',
      },
      {
        label: t('检验员'),
        field: 'checkBy',
        component: 'Select',
        componentProps: {
          dictType: 'mf_check_person',
          allowClear: true,
        },
        ifShow: false,
      },
      {
        label: t('班次'),
        field: 'teamClass',
        component: 'Select',
        componentProps: {
          dictType: 'mf_team_class',
          allowClear: true,
        },
        ifShow: false,
      },
      {
        label: t('班组'),
        field: 'cteam',
        component: 'Select',
        componentProps: {
          dictType: 'mf_team',
          allowClear: true,
        },
        ifShow: false,
      },
      {
        label: t('品级'),
        field: 'cgrade',
        component: 'Select',
        componentProps: {
          dictType: 'mf_grade',
          allowClear: true,
        },
        ifShow: false,
      },
      {
        label: t('数量'),
        field: 'iqty',
        component: 'Input',
        ifShow: false,
      },
      {
        label: t('件数'),
        field: 'inum',
        component: 'Input',
        ifShow: false,
      },
      {
        label: t('批次'),
        field: 'parent.cbatch',
        component: 'Input',
      },
      {
        label: t('含量(%)'),
        field: 'ichangeRate',
        component: 'Input',
        ifShow: false,
      },
      {
        label: t('累计入库数量'),
        field: 'sumQty',
        component: 'Input',
        ifShow: false,
      },
      {
        label: t('累计入库件数'),
        field: 'sumNum',
        component: 'Input',
        ifShow: false,
      },
      {
        label: t('最大托盘序号'),
        field: 'maxTpSeq',
        component: 'Input',
        ifShow: false,
      },
      {
        label: t('状态'),
        field: 'status',
        component: 'Select',
        componentProps: {
          dictType: 'mf_sys_status',
          allowClear: true,
        },
      },

      {
        label: t('U8单号'),
        field: 'u8Djno',
        component: 'Input',
        // ifShow: false,
      },
      {
        label: t('制单人'),
        field: 'createByName',
        component: 'Input',
        ifShow: false,
      },
      {
        label: t('修改人'),
        field: 'updateByName',
        component: 'Input',
        ifShow: false,
      },
      {
        label: t('托盘容量'),
        field: 'packSize',
        component: 'Input',
        ifShow: false,
      },
      {
        label: t('单件容量'),
        field: 'pieceQty',
        component: 'Input',
        ifShow: false,
      },
    ],
    fieldMapToTime: [
      ['parent.ddate', ['parent.ddate_gte', 'parent.ddate_lte']],
      ['createDate', ['createDate_gte', 'createDate_lte']]
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('入库时间'),
      dataIndex: 'createDate',
      key: 'a.create_date',
      sorter: true,
      width: 90,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('制单人'),
      dataIndex: 'createByName',
      key: 'a.create_by_name',
      sorter: true,
      width: 60,
      align: 'left',
    },
    {
      title: t('存货编码'),
      dataIndex: 'invCode',
      key: 'a.inv_code',
      sorter: true,
      width: 80,
      align: 'center',
    },
    {
      title: t('存货名称'),
      dataIndex: 'basInv.invName',
      // key: 'a.inv_code',
      // sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('规格型号'),
      dataIndex: 'basInv.invStd',
      width: 100,
      align: 'left',
    },
    {
      title: t('单位'),
      dataIndex: 'basInv.unitName',
      width: 40,
      align: 'left',
    },
    {
      title: t('批次'),
      dataIndex: 'parent.cbatch',
      key: 'parent.cbatch',
      sorter: true,
      width: 80,
      align: 'left',
    },
    {
      title: t('仓库'),
      dataIndex: 'parent.whCode',
      key: 'parent.wh_code',
      sorter: true,
      width: 60,
      align: 'center',
    },
    {
      title: t('入库数量'),
      dataIndex: 'iqty',
      key: 'a.iqty',
      sorter: true,
      width: 70,
      align: 'left',
    },
    {
      title: t('入库件数'),
      dataIndex: 'inum',
      key: 'a.inum',
      sorter: true,
      width: 70,
      align: 'left',
    },
    {
      title: t('货位名称'),
      dataIndex: 'basPos.posName',
      // key: 'basPos.pos_name',
      // sorter: true,
      width: 70,
      align: 'left',
    },
    {
      title: t('货位编码'),
      dataIndex: 'basPos.posCode',
      // key: 'basPos.pos_name',
      // sorter: true,
      width: 70,
      align: 'center',
    },
    {
      title: t('申请单号'),
      dataIndex: 'parent.djno',
      key: 'parent.djno',
      sorter: true,
      width: 100,
      align: 'center',
    },
    {
      title: t('申请日期'),
      dataIndex: 'parent.ddate',
      key: 'parent.ddate',
      sorter: true,
      width: 70,
      align: 'left',
    },
    { 
      title: t('单据状态'),
      dataIndex: 'parent.djStatus',
      key: 'parent.dj_status',
      sorter: true,
      width: 80,
      align: 'center',
      dictType: 'wms_rd10_status',
      fixed: 'left',
      ifShow: false,
    },
    {
      title: t('数量'),
      dataIndex: 'parent.iqty',
      key: 'parent.iqty',
      sorter: true,
      width: 80,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('件数'),
      dataIndex: 'parent.inum',
      key: 'parent.inum',
      sorter: true,
      width: 60,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('剩余入库数量'),
      dataIndex: 'parent.syQty',
      // key: 'parent.sy_qty',
      // sorter: true,
      width: 100,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('剩余入库件数'),
      dataIndex: 'parent.syNum',
      // key: 'parent.sy_num',
      // sorter: true,
      width: 100,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('累计入库数量'),
      dataIndex: 'parent.sumQty',
      key: 'parent.sum_qty',
      sorter: true,
      width: 90,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('累计入库件数'),
      dataIndex: 'parent.sumNum',
      key: 'parent.sum_num',
      sorter: true,
      width: 90,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('最大托盘序号'),
      dataIndex: 'parnt.maxTpSeq',
      key: 'a.max_tp_seq',
      sorter: true,
      width: 130,
      align: 'center',
      ifShow: false,
    },
    {
      title: t('update_date'),
      dataIndex: 'updateDate',
      key: 'a.update_date',
      sorter: true,
      width: 130,
      align: 'center',
      ifShow: false,
    },
    {
      title: t('remarks'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 130,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('修改人'),
      dataIndex: 'updateByName',
      key: 'a.update_by_name',
      sorter: true,
      width: 130,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('托盘容量'),
      dataIndex: 'parent.packSize',
      key: 'parent.pack_size',
      sorter: true,
      width: 80,
      align: 'center',
      ifShow: false,
    },
    {
      title: t('单件容量'),
      dataIndex: 'parent.pieceQty',
      key: 'parent.piece_qty',
      sorter: true,
      width: 80,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('U8单号'),
      dataIndex: 'u8Djno',
      key: 'a.u8_djno',
      sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'a.status',
      dictType: 'mf_sys_status',
      sorter: true,
      width: 60,
      align: 'center',
      fixed: 'right',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑入库通知'),
        onClick: handleForm.bind(this, { djno: record.djno }),
        auth: 'wms.wh:rd10:wmsRd10:edit',
        ifShow: () => record.djStatus == '1',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除入库通知'),
        popConfirm: {
          title: t('是否确认删除入库通知'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'wms.wh:rd10:wmsRd10:edit',
        ifShow: () => record.status == '9',
      },
      {
        icon: 'i-fluent:flowchart-20-regular',
        title: t('流程追踪'),
        onClick: handleTrace.bind(this, record),
        ifShow: () => record.status != '9',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getSelectRows }] = useTable({
    api: wmsRds10ListData,
    beforeFetch: (params) => {
      // router.currentRoute.value.query.code ? params.parent.djno = router.currentRoute.value.query.code : null;
      router.currentRoute.value.query.code ? params['parent.djno'] = router.currentRoute.value.query.code : ''
      console.log('params',params,router.currentRoute.value.query.code)
      return params;
    },
    columns: tableColumns,
    // actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
    rowSelection: { type: 'checkbox' },
    indexColumnProps: { width: 40 },
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }

  const [registerTraceModal, { openModal: traceModel }] = useModal();
  const [registerBarSizeTypeModal, { openModal: openBarSizeTypeModal }] = useModal();

  function handleTrace(record: Recordable) {
    traceModel(true, { formKey: 'mf_rd10_nottify', bizKey: record.id });
  }

  async function handlePrintTray() {
    let arr = await getSelectRows();
    if (getSelectRows().length == 0) {
      showMessage(t('请先选择一行数据'));
      return;
    }
    openPrintDrawer(true, { arr: arr });
  }

  async function handleInvPrint() {
    let arr = await getSelectRows();
    if (getSelectRows().length == 0) {
      showMessage(t('请先选择一行数据'));
      return;
    }
    openBarSizeTypeModal(true, { arr: arr, barSizeType: BarTypeEnum.MoNotify });
  }

  async function handleSuccessBarSizeTypeImport(res: any) {
    if (res.result == 'true') {
      showMessage(t('打印成功'));
      refresh();
    }
  }
</script>
<style lang="less" scoped>
</style>
