<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'bankdirectlink:contract:viewContract:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsBankdirectlinkContractViewContractForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { ViewContract, viewContractSave, viewContractForm } from '/@/api/billmanager/bankdirectlink/contract/viewContract';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('bankdirectlink.contract.viewContract');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<ViewContract>({} as ViewContract);

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增合同') : t('编辑合同'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('合同编码'),
      field: 'htcode',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
    },
    {
      label: t('合同名称'),
      field: 'htname',
      component: 'Input',
      componentProps: {
        maxlength: 400,
      },
    },
    {
      label: t('供应商编码'),
      field: 'vencode',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('供应商名称'),
      field: 'venname',
      component: 'Input',
      componentProps: {
        maxlength: 98,
      },
    },
    {
      label: t('预算金额'),
      field: 'amount',
      component: 'Input',
      componentProps: {
        maxlength: 36,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('已使用金额'),
      field: 'useAmount',
      component: 'Input',
      componentProps: {
        maxlength: 36,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('使用使用金额'),
      field: 'syUseMount',
      component: 'Input',
      componentProps: {
        maxlength: 36,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await viewContractForm(data);
    record.value = (res.viewContract || {}) as ViewContract;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    updateSchema([
      {
        field: 'htcode',
        componentProps: {
          disabled: !record.value.isNewRecord,
        },
      },
    ]);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        htcode: record.value.htcode,
      };
      // console.log('submit', params, data, record);
      const res = await viewContractSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
