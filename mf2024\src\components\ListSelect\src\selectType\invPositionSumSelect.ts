import { useI18n } from '/@/hooks/web/useI18n';
import { BasicColumn, BasicTableProps, FormProps } from '/@/components/Table';
import { invPositionSumListData } from '/@/api/wms/weighbridge/fh';

const { t } = useI18n('sys.empUser');

const modalProps = {
  title: t('货位管理'),
};

const searchForm: FormProps = {
  baseColProps: { lg: 6, md: 8 },
  labelWidth: 100,
  showResetButton:false,
  schemas: [
    {
      label: t('cwhCode'),
      field: 'cwhCode',
      component: 'Input',
      show: false,
    },
    {
      label: t('cinvCode'),
      field: 'cinvCode',
      component: 'Input',
      show: false,
    },
    {
      label: t('autoid'),
      field: 'autoid',
      component: 'Input',
      show: false,
    },
    {
      label: t('批次号'),
      field: 'cbatch',
      component: 'Input',
    },
    {
      label: t('货位名称'),
      field: 'cposName',
      component: 'Input',
    },
    {
      label: t('货位编码'),
      field: 'cposCode',
      component: 'Input',
    },
  ],
};

const tableColumns: BasicColumn[] = [
  {
    title: t('批次号'),
    dataIndex: 'cbatch',
    key: 'a.cwhCode',
    sorter: true,
    width: 100,
  },
  {
    title: t('货位编码'),
    dataIndex: 'cposCode',
    key: 'a.cus_code',
    sorter: true,
    width: 100,
  },
  {
    title: t('货位名称'),
    dataIndex: 'cposName',
    key: 'a.cposName',
    sorter: true,
    width: 100,
  },
  {
    title: t('数量'),
    dataIndex: 'iquantity',
    key: 'a.plan_weight',
    sorter: true,
    width: 100,
    customRender: ({ record }) => {
      // 保留两位小数
      return record.iquantity ? Number(record.iquantity).toFixed(2) : '0.00';
    },
  },
];

const tableProps: BasicTableProps = {
  api: invPositionSumListData,
  beforeFetch: (params) => {
    params['isAll'] = true;
    return params;
  },
  columns: tableColumns,
  formConfig: searchForm,
  rowKey: 'cbatch',
};

export default {
  modalProps,
  tableProps,
  itemCode: 'cposCode',
  itemName: 'cposName',
  isShowCode: true,
};
