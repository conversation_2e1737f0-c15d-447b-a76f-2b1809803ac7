<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer v-bind="$attrs" :showFooter="false" @register="registerDrawer" width="80%">
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
      <span> （{{ record.code + '-' + record.typeName + '-' + record.typeCode}}） </span>
    </template>
    <Tabs v-model:activeKey="activeKey" tabPosition="left">
      <Tabs.TabPane key="0" :forceRender="true" tab="基础字段">
        <TabFields :colpop="{...colpop,activeKey:'0'}" />
      </Tabs.TabPane>

      <Tabs.TabPane key="1" :forceRender="true" tab="扩展字段">
        <TabFields :colpop="{...colpop,activeKey:'1'}" />
      </Tabs.TabPane>

      
    </Tabs>
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsTestTestDataForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { Tabs } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  // import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';

  import TabFields from '../layVoucherFields/list.vue';

  // import TabConfig from '../layVoucherTabConfig/configForm.vue';
  import TableCol from '../layVoucherTableCol/list.vue';
  // import TabToolbar from '../layVoucherTabToolbar/list.vue';
  // import TabRightOpe from '../layVoucherTabRightOpe/list.vue';

  // const emit = defineEmits(['success', 'register']);
  let colpop = ref({});
  const { t } = useI18n('test.testData');
  // const { showMessage } = useMessage();
  const getTitle = computed(() => ({
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: t('单据字段设计'),
  }));
  let record = ref({
    code: '',
    name: '',
    typeName: '',
    typeCode: '',
  });

  const activeKey = ref<string>('0');
  const [registerDrawer, { setDrawerProps }] = useDrawerInner(async (data) => {
    activeKey.value = '0';
    colpop.value = data;
    setDrawerProps({ loading: true });
    console.log(data);
    record.value = data;
    // await resetFields();
    // const res = await testDataForm(data);
    // record.value = (res.testData || {}) as TestData;
    // record.value.__t = new Date().getTime();
    // setFieldsValue(record.value);
    // setTestDataChildTableData(res);
    setDrawerProps({ loading: false });
  });

  // async function handleSubmit() {
  //   try {
  //     const data = await validate();
  //     setDrawerProps({ confirmLoading: true });
  //     const params: any = {
  //       isNewRecord: record.value.isNewRecord,
  //       id: record.value.id,
  //     };
  //     data.testDataChildList = await getTestDataChildList();
  //     // console.log('submit', params, data, record);
  //     const res = await testDataSave(params, data);
  //     showMessage(res.message);
  //     setTimeout(closeDrawer);
  //     emit('success', data);
  //   } catch (error: any) {
  //     if (error && error.errorFields) {
  //       showMessage(t('您填写的信息有误，请根据提示修正。'));
  //     }
  //     console.log('error', error);
  //   } finally {
  //     setDrawerProps({ confirmLoading: false });
  //   }
  // }
</script>
<style>
.scroll-container .scrollbar__view {
    /* box-sizing: border-box; */
    height: 100%;
}
.ant-tabs {
    height: 100%;
}
</style>