{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"baseUrl": ".", "target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "strict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "noImplicitOverride": true, "useUnknownInCatchVariables": false, "composite": false, "declarationMap": false, "inlineSources": false, "isolatedModules": true, "preserveWatchOutput": true, "removeComments": true, "skipLibCheck": true, "noImplicitAny": false, "strictPropertyInitialization": true, "strictBindCallApply": true, "strictNullChecks": true, "noUnusedLocals": false, "noUnusedParameters": false, "experimentalDecorators": true, "resolveJsonModule": true, "declaration": false, "jsx": "preserve", "jsxImportSource": "vue", "useDefineForClassFields": true, "allowImportingTsExtensions": false, "lib": ["ESNext", "DOM"], "types": ["node", "vue-types", "vite/client", "@jeesite/types"]}, "include": ["./**/*.ts", "./**/*.tsx", "./**/*.vue"], "exclude": ["node_modules", "vite.config.ts", "dist"], "ts-node": {"transpileOnly": true, "files": true, "esm": true}}