import { BasicModel } from '../model/baseModel';

//U8自定义字段表头
export interface U8DefHEntity extends BasicModel<U8DefHEntity> {
  cdefine1?: string; // cdefine1
  cdefine2?: string; // cdefine2
  cdefine3?: string; // cdefine3
  cdefine4?: string; // cdefine4
  cdefine5?: number; // cdefine5
  cdefine6?: string; // cdefine6
  cdefine7?: number; // cdefine7
  cdefine8?: string; // cdefine8
  cdefine9?: string; // cdefine9
  cdefine10?: string; // cdefine10
  cdefine11?: string; // cdefine11
  cdefine12?: string; // cdefine12
  cdefine13?: string; // cdefine13
  cdefine14?: string; // cdefine14
  cdefine15?: number; // cdefine15
  cdefine16?: number; // cdefine16
}

//U8自定义字段表体
export interface U8DefBEntity extends BasicModel<U8DefBEntity> {
  cdefine22?: string; // cdefine22
  cdefine23?: string; // cdefine23
  cdefine24?: string; // cdefine24
  cdefine25?: string; // cdefine25
  cdefine26?: number; // cdefine26
  cdefine27?: string; // cdefine27
  cdefine28?: number; // cdefine28
  cdefine29?: string; // cdefine29
  cdefine30?: string; // cdefine30
  cdefine31?: string; // cdefine31
  cdefine32?: string; // cdefine32
  cdefine33?: string; // cdefine33
  cdefine34?: string; // cdefine34
  cdefine35?: string; // cdefine35
  cdefine36?: number; // cdefine36
  cdefine37?: number; // cdefine37
}