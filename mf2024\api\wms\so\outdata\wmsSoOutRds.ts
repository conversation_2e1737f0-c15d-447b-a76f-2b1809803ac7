/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WmsSoOutRds extends BasicModel<WmsSoOutRds> {
  parentId?: string; // 装车计划明细ID
  invCode?: string; // 存货编码
  posCode?: string; // 货位
  iqty?: number; // 数量
  inum?: number; // 件数
  ichangeRate?: number; // 换算率
  rgrade?: string; // 品级
  createByName?: string; // create_by_name
  updateByName?: string; // update_by_name
}

export const wmsSoOutRdsList = (params?: WmsSoOutRds | any) =>
  defHttp.get<WmsSoOutRds>({ url: adminPath + '/wms/so/outdata/wmsSoOutRds/list', params });

export const wmsSoOutRdsListData = (params?: WmsSoOutRds | any) =>
  defHttp.post<Page<WmsSoOutRds>>({ url: adminPath + '/wms/so/outdata/wmsSoOutRds/listData', params });

export const wmsSoOutRdsForm = (params?: WmsSoOutRds | any) =>
  defHttp.get<WmsSoOutRds>({ url: adminPath + '/wms/so/outdata/wmsSoOutRds/form', params });

export const wmsSoOutRdsSave = (params?: any, data?: WmsSoOutRds | any) =>
  defHttp.postJson<WmsSoOutRds>({ url: adminPath + '/wms/so/outdata/wmsSoOutRds/save', params, data });

export const wmsSoOutRdsDelete = (params?: WmsSoOutRds | any) =>
  defHttp.get<WmsSoOutRds>({ url: adminPath + '/wms/so/outdata/wmsSoOutRds/delete', params });
