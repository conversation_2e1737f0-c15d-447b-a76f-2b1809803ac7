{"name": "@jeesite/types", "version": "5.12.1", "private": true, "type": "module", "scripts": {"type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint:eslint": "eslint --cache --max-warnings 0  \"./**/*.{vue,ts,tsx}\" --fix", "lint:all": "npm run type:check && npm run lint:eslint", "uninstall": "rimraf node_modules", "update": "ncu -u"}, "homepage": "https://jeesite.com", "repository": {"type": "git", "url": "https://gitee.com/thinkgem/jeesite-vue.git"}, "bugs": {"url": "https://gitee.com/thinkgem/jeesite-vue/issues"}, "author": {"name": "ThinkGem", "email": "<EMAIL>", "url": "https://gitee.com/thinkgem"}}