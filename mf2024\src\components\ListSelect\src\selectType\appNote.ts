import { useI18n } from '/@/hooks/web/useI18n';
import { BasicColumn, BasicTableProps, FormProps } from '/@/components/Table';
import { billmanagerApNoteListSelect } from '/@/api/billmanager/bankdirectlink/useoffunds/billmanagerUseOfFunds';

const { t } = useI18n('sys.viewContract');

const modalProps = {
  title: t('汇票选择'),
};

const searchForm: FormProps = {
  baseColProps: { lg: 6, md: 8 },
  labelWidth: 90,
  schemas: [
    {
      label: t('票据号'),
      field: 'clink',
      component: 'Input',
    },
    // {
    //   label: t('合同名称'),
    //   field: 'htname',
    //   component: 'Input',
    // },
    // {
    //   label: t('供应商编码'),
    //   field: 'vencode',
    //   component: 'Input',
    // },
    // {
    //   label: t('供应商名称'),
    //   field: 'venname',
    //   component: 'Input',
    // },
    // {
    //   label: t('预算金额'),
    //   field: 'amount',
    //   component: 'Input',
    // },
    // {
    //   label: t('已使用金额'),
    //   field: 'useAmount',
    //   component: 'Input',
    // },
    // {
    //   label: t('使用使用金额'),
    //   field: 'syUseMount',
    //   component: 'Input',
    // },
  ],
};

const tableColumns: BasicColumn[] = [
  {
    title: t('票据号'),
    dataIndex: 'clink',
    key: 'a.clink',
    sorter: true,
    width: 230,
    align: 'left',
    slot: 'firstColumn',
  },
  {
    title: t('金额'),
    dataIndex: 'iramount',
    key: 'a.iramount',
    sorter: true,
    width: 60,
    align: 'left',
  },
  {
    title: t('承兑银行'),
    dataIndex: 'cbank',
    key: 'a.cbank',
    // sorter: true,
    width: 230,
    align: 'left',
  },
  {
    title: t('到期日期'),
    dataIndex: 'dexpiredate',
    key: 'a.dexpiredate',
    // sorter: true,
    width: 130,
    align: 'left',
  },
  {
    title: t('应答日期'),
    dataIndex: 'dreceiptdate',
    key: 'a.dreceiptdate',
    // sorter: true,
    width: 130,
    align: 'right',
  },
  {
    title: t('是否分包'),
    dataIndex: 'bsubpackage',
    key: 'a.bsubpackage',
    // sorter: true,
    width: 130,
    align: 'right',
    dictType: 'mf_BlGrdBagCrcl_Prmt_Ind',
  },
  // {
  //   title: t('使用使用金额'),
  //   dataIndex: 'syUseMount',
  //   key: 'a.sy_use_mount',
  //   sorter: true,
  //   width: 130,
  //   align: 'right',
  // },
];

const tableProps: BasicTableProps = {
  api: billmanagerApNoteListSelect,
  beforeFetch: (params) => {
    params['isAll'] = true;
    return params;
  },
  columns: tableColumns,
  formConfig: searchForm,
  rowKey: 'clink',
};

export default {
  modalProps,
  tableProps,
  itemCode: 'clink',
  itemName: 'clink',
  isShowCode: false,
};
