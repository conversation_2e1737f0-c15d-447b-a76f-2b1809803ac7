/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
@header-trigger-prefix-cls: ~'jeesite-layout-header-trigger';
@header-prefix-cls: ~'jeesite-layout-header';
@breadcrumb-prefix-cls: ~'jeesite-layout-breadcrumb';
@logo-prefix-cls: ~'jeesite-app-logo';

.ant-layout .ant-layout-header.@{header-prefix-cls} {
  display: flex;
  height: @header-height;
  padding: 0;
  margin-left: -1px;
  line-height: @header-height;
  color: @white;
  background-color: @white;
  align-items: center;
  justify-content: space-between;
}

@media (max-width: @screen-md) {
  .ant-layout .ant-layout-header.@{header-prefix-cls} {
    overflow: auto hidden;
  }
}

.@{header-prefix-cls} {
  &--mobile {
    .@{breadcrumb-prefix-cls},
    .error-action,
    .notify-item,
    .fullscreen-item {
      display: none;
    }

    .@{logo-prefix-cls} {
      min-width: unset;
      padding-right: 0;

      &__title {
        display: none;
      }
    }
    .@{header-trigger-prefix-cls} {
      padding: 0 4px 0 8px !important;
    }
    .@{header-prefix-cls}-action {
      padding-right: 4px;
    }
  }

  &--fixed {
    position: fixed;
    top: 0;
    left: 0;
    z-index: @layout-header-fixed-z-index;
    width: 100%;
  }

  &-logo {
    height: @header-height;
    min-width: 192px;
    padding: 0 10px;
    font-size: 14px;
    font-weight: bold;
    justify-content: center;

    img {
      width: @logo-width;
      height: @logo-width;
      margin-right: 2px;
    }
  }

  &-left {
    display: flex;
    height: 100%;
    align-items: center;
    flex-shrink: 0;

    .@{header-trigger-prefix-cls} {
      display: flex;
      height: 100%;
      padding: 1px 10px 0;
      cursor: pointer;
      align-items: center;

      .anticon {
        font-size: 16px;
      }

      &.light {
        &:hover {
          background-color: @header-light-bg-hover-color;
        }

        svg {
          fill: #000;
        }
      }

      &.dark {
        &:hover {
          background-color: @header-dark-bg-hover-color;
        }
      }
    }
  }

  &-menu {
    height: 100%;
    min-width: 0;
    flex: 1;
    align-items: center;
    // overflow-x: hidden;
    // position: relative;
  }

  &-action {
    display: flex;
    min-width: 180px;
    // padding-right: 12px;
    align-items: center;
    flex-shrink: 0;

    > div,
    > span {
      display: flex !important;
      padding: 0 2px;
      height: calc(@header-height - 10px);
      line-height: calc(@header-height - 10px);
      border-radius: 6px;
      font-size: 1.2em;
      cursor: pointer;
      align-items: center;

      &.ant-badge {
        .ant-badge-dot {
          top: 10px;
          right: 2px;
        }

        .ant-badge-count {
          min-width: 14px;
          height: 14px;
          padding: 0 4px;
          box-shadow: 0 0 0 1px #d1d1d1;
          font-size: 9px;
          line-height: 15px;
        }
      }
    }

    span[role='img'] {
      padding: 0 8px;
    }
  }

  &--light {
    background-color: @white !important;
    border-bottom: 1px solid @header-light-bottom-border-color;
    border-left: 1px solid @header-light-bottom-border-color;

    .@{header-prefix-cls}-logo {
      color: @text-color-base;

      &:hover {
        background-color: @header-light-bg-hover-color;
      }
    }

    .@{header-prefix-cls}-action {
      > div,
      > span {
        color: @text-color-base;

        .jeesite-icon {
          padding: 0 10px;
          font-size: 20px !important;
        }

        &:hover {
          background-color: @header-light-bg-hover-color;
        }
      }

      &-icon,
      span[role='img'] {
        color: @text-color-base;
      }
    }
  }

  &--dark {
    background-color: @header-dark-bg-color !important;
    // border-bottom: 1px solid @border-color-base;
    border-left: 1px solid @border-color-base;
    .@{header-prefix-cls}-logo {
      &:hover {
        background-color: @header-dark-bg-hover-color;
      }
    }

    .@{header-prefix-cls}-action {
      > div,
      > span {
        .jeesite-icon {
          padding: 0 10px;
          font-size: 20px !important;
          color: @white;
        }

        .ant-badge {
          span {
            color: @white;
          }
        }

        &:hover {
          background-color: @header-dark-bg-hover-color;
        }
      }
    }
  }
}

html[data-theme='dark'] {
  .@{header-prefix-cls} {
    &--dark {
      .@{header-prefix-cls}-action {
        > div,
        > span {
          &:hover {
            background-color: #262626;
          }
        }
      }
    }
  }
}
