# 访问项目的根路径
VITE_PUBLIC_PATH = /vue

# 打包编译发布路径
VITE_OUTPUT_DIR = ../maven/vue-pro-dist-5.8.1/src/main/resources/vue

# 路由模式（true: history、false: hash）
VITE_ROUTE_WEB_HISTORY = true

# 是否删除 console 调试信息
VITE_DROP_CONSOLE = true

# 是否启用 gzip 或 brotli 压缩，多个使用 `,` 分隔
# 支持选项: gzip | brotli | none
VITE_BUILD_COMPRESS = 'none'

# 是否删除源文件时使用压缩，默认为 false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

# 访问接口的根路径（例如：https://vue.jeesite.com）建议为空
VITE_GLOB_API_URL = 

# 访问接口的前缀，在根路径之后
VITE_GLOB_API_URL_PREFIX = 

# 访问接口的管理基础路径
VITE_GLOB_ADMIN_PATH = /a

# 文件预览类型（true、oss）
VITE_FILE_PREVIEW = true

# 是否启用 Vue PWA
VITE_USE_PWA = false

# 是否兼容 Chrome 内核比较低的浏览器，如 QQ 浏览器（编译包会变大）
VITE_LEGACY = true
