<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicModal
    v-bind="$attrs"
    :title="t('打印尺寸选择')"
    :okText="t('确认')"
    @register="registerModal"
    @ok="handleSubmit"
    width="20%"
  >
    <BasicForm @register="registerForm"></BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, onMounted ,computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { useGlobSetting } from '/@/hooks/setting';
  import { printSnNumber } from '/@/api/wms/barcode/encode';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { downloadByUrl } from '/@/utils/file/download';
  import { BarTypeEnum } from '/@/enums/defEnum';
  const { showMessage, createSuccessModal } = useMessage();
  const emit = defineEmits(['success', 'register']);
  const { ctxPath } = useGlobSetting();
  

  const { t } = useI18n('bas.inv.basInv');

  const uploadInfo = ref('');
  let selectInv = ref<any>([]);
  let barSizeType = ref<any>('');
  let bizKey = ref<any>('');

  const [registerModal, { setModalProps, closeModal }] = useModalInner((data:any) => {
    barSizeType.value = data.barSizeType;
    selectInv.value = data.arr;
    data.bizKey ? bizKey.value = data.bizKey : '';
  });

  // 计算 barSizeType
  const inputFormSchemas = computed(() => {
    // 如果 barSizeType.value 为 4，则返回 barSizeTypeForm.vue 的 inputFormSchemas 数组
    if (barSizeType.value == BarTypeEnum.Inventory) {
      return inputFormSchemas4;
    } else if (barSizeType.value == BarTypeEnum.PoOrder) {
      return inputFormSchemas2;
    } else if (barSizeType.value == BarTypeEnum.Position) {
      return inputFormSchemas5;
    } else if (barSizeType.value == BarTypeEnum.MoNotify) {
      return inputFormSchemas1;
    }
  });

  const inputFormSchemas1: FormSchema[] = [
    {
      label: t('请选择一项'),
      field: 'barSizeType',
      component: 'RadioGroup',
      defaultValue: '8040',
      componentProps: {
        dictType: 'bar_size_type1',
        maxlength: 50,
      },
      colProps: {
        span: 8,
      },
      required: true,
    },
  ];
  const inputFormSchemas2: FormSchema[] = [
    {
      label: t('请选择一项'),
      field: 'barSizeType',
      component: 'RadioGroup',
      defaultValue: '8040',
      componentProps: {
        dictType: 'bar_size_type2',
        maxlength: 50,
      },
      colProps: {
        span: 8,
      },
      required: true,
    },
  ];
  const inputFormSchemas4: FormSchema[] = [
    {
      label: t('请选择一项'),
      field: 'barSizeType',
      component: 'RadioGroup',
      defaultValue: '8040',
      componentProps: {
        dictType: 'bar_size_type4',
        maxlength: 50,
      },
      colProps: {
        span: 8,
      },
      required: true,  
    },
  ];
  const inputFormSchemas5: FormSchema[] = [
    {
      label: t('请选择一项'),
      field: 'barSizeType',
      component: 'RadioGroup',
      defaultValue: '8080',
      componentProps: {
        dictType: 'bar_size_type5',
        maxlength: 50,
      },
      colProps: {
        span: 8,
      },
      required: true,
    },
  ];
  const [registerForm, { validate }] =
    useForm({
      labelWidth: 120,
      schemas: inputFormSchemas,
      baseColProps: { lg: 24, md: 24 },
    });

  async function handleSubmit() {
    // 把得到的值传递给父组件
    try {
      const data = await validate();
      // const res = await printBySelInv(data);
      // if(res.result == 'true'){
      //   emit('success', res);
      //   closeModal();
      // }
      console.log(selectInv.value,'selectInv.value');
      const selIds = selectInv.value.map((item) => item.id).join(',');
      const prtQtys = selectInv.value.map((item) => item.prtqty ? item.prtqty : 1).join(',');
      const params = {
         barType: barSizeType.value,
         barSizeType: data.barSizeType,
         prtQtys: prtQtys,
      };
      if(bizKey.value == 'bizKey'){
        params.bizKey = selIds;
      }else{
        params.selIds = selIds;
      }
      const res = await printSnNumber(params);
      if(res.result == "true"){
        closeModal();
        createSuccessModal({ 
          content: '打印 ' + res.fileName,
          okText: '下载',
          onOk() {
            downloadByUrl({ url: ctxPath + res.pdfUrl });
            closeModal();
          },
        });
      } else {
        showMessage(res.message);
      } 
    }catch(error){
      
    }

  }
</script>
