<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="default" v-auth="'billmanager:xtlog:view'" @click="handleExport()">
          <Icon icon="ant-design:download-outlined" /> {{ t('导出') }}
        </a-button>
        <Popconfirm
          title="是否确认重新协同？"
          ok-text="是"
          cancel-text="否"
          placement="bottom"
          @confirm="handleAgain()"
        >
          <a-button type="warning" v-auth="'billmanager:xtlog:edit'" ghost>
            <Icon icon="simple-line-icons:action-undo" /> {{ t('重新协同') }}
          </a-button>
        </Popconfirm>
        <Popconfirm
          title="是否确认关闭协同？"
          ok-text="是"
          cancel-text="否"
          placement="bottom"
          @confirm="handleClose()"
        >
          <a-button type="error" v-auth="'billmanager:xtlog:edit'" ghost>
            <Icon icon="simple-line-icons:close" /> {{ t('关闭协同') }}
          </a-button>
        </Popconfirm>
      </template>
    </BasicTable>
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsBillmanagerXtlogU8XtLogList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { u8XtLogListData, reStartXt, closeXt } from '/@/api/billmanager/xtlog/u8XtLog';
  import { FormProps } from '/@/components/Form';
  import { Popconfirm } from 'ant-design-vue';
  import { uploadFileList } from '/@/api/sys/upload';

  const { t } = useI18n('shop.xtlog.u8XtLog');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: router.currentRoute.value.meta.title || t('银企直连协同日志管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('协同标志'),
        field: 'xtno',
        component: 'Input',
      },
      {
        label: t('来源单据'),
        field: 'vouchId',
        component: 'Input',
      },
      {
        label: t('业务类型'),
        field: 'busType',
        component: 'Select',
        componentProps: {
          dictType: 'bank_xt_bus_type',
          allowClear: true,
        },
      },
      {
        label: t('业务节点'),
        field: 'busNode',
        component: 'Select',
        componentProps: {
          dictType: 'bank_xt_bus_node',
          allowClear: true,
        },
      },
      {
        label: t('节点状态'),
        field: 'xtStatus',
        component: 'Select',
        componentProps: {
          dictType: 'bank_xt_status',
          allowClear: true,
        },
      },
      {
        label: t('协同状态'),
        field: 'routeStatus',
        component: 'Select',
        componentProps: {
          dictType: 'bank_xt_route_status',
          allowClear: true,
        },
        defaultValue: '1',
      },
      {
        label: t('目标单据'),
        field: 'xtDjno',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('节点状态'),
      dataIndex: 'xtStatus',
      key: 'a.xt_status',
      sorter: true,
      width: 100,
      align: 'center',
      dictType: 'bank_xt_status',
      fixed: 'left',
    },
    {
      title: t('协同标志'),
      dataIndex: 'xtno',
      key: 'a.xtno',
      sorter: true,
      width: 100,
      align: 'left',
      fixed: 'left',
    },
    {
      title: t('来源单据'),
      dataIndex: 'vouchId',
      key: 'a.vouch_id',
      sorter: true,
      width: 200,
      align: 'left',
    },
    {
      title: t('业务类型'),
      dataIndex: 'busType',
      key: 'a.bus_type',
      sorter: true,
      width: 100,
      align: 'center',
      dictType: 'bank_xt_bus_type',
    },
    {
      title: t('业务节点'),
      dataIndex: 'busNode',
      key: 'a.bus_node',
      sorter: true,
      width: 100,
      align: 'center',
      dictType: 'bank_xt_bus_node',
    },
    {
      title: t('顺序'),
      dataIndex: 'sortNum',
      key: 'a.sort_num',
      sorter: false,
      width: 80,
      align: 'center',
    },
    {
      title: t('执行结果'),
      dataIndex: 'cresult',
      key: 'a.cresult',
      sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: t('开始'),
      dataIndex: 'bstar',
      key: 'a.bstar',
      sorter: false,
      width: 60,
      align: 'left',
    },
    {
      title: t('结束'),
      dataIndex: 'bend',
      key: 'a.bend',
      sorter: false,
      width: 60,
      align: 'left',
    },
    {
      title: t('单据日期'),
      dataIndex: 'ddate',
      key: 'a.ddate',
      sorter: true,
      width: 100,
      align: 'center',
    },
    {
      title: t('初始化时间'),
      dataIndex: 'createDate',
      key: 'a.create_date',
      sorter: true,
      width: 150,
      align: 'center',
    },
    {
      title: t('就绪时间'),
      dataIndex: 'preDate',
      key: 'a.pre_date',
      sorter: true,
      width: 150,
      align: 'center',
    },
    {
      title: t('完成时间'),
      dataIndex: 'overDate',
      key: 'a.over_date',
      sorter: true,
      width: 150,
      align: 'center',
    },
    {
      title: t('最后执行'),
      dataIndex: 'updateDate',
      key: 'a.update_date',
      sorter: true,
      width: 150,
      align: 'center',
    },
    {
      title: t('目标单据'),
      dataIndex: 'xtDjno',
      key: 'a.xt_djno',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('重发数'),
      dataIndex: 'reTimes',
      key: 'a.re_times',
      sorter: true,
      width: 80,
      align: 'center',
    },
    {
      title: t('协同状态'),
      dataIndex: 'routeStatus',
      key: 'a.route_status',
      sorter: true,
      width: 100,
      align: 'center',
      dictType: 'bank_xt_route_status',
      fixed: 'right',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 100,
    actions: (record: Recordable) => [
      {
        icon: 'i-ant-design:download-outlined',
        title: t('下载'),
        onClick: handleDownload.bind(this, { id: record.id }),
      },
    ],
  };

  const [registerTable, { reload, getSelectRows }] = useTable({
    api: u8XtLogListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
    rowSelection: {
      type: 'checkbox',
    },
  });

  async function handleExport() {
    const { ctxAdminPath } = useGlobSetting();
    downloadByUrl({
      url: ctxAdminPath + '/billmanager/xtlog/u8XtLog/exportData',
      target: '_self',
    });
  }

  // 重新协同
  async function handleAgain() {
    if (getSelectRows().length && getSelectRows().length >= 1) {
      const selIds = getSelectRows().map((item: any) => item.id).join(',');
      const res = await reStartXt({ selIds: selIds });
      showMessage(res.message);
      handleSuccess();
    } else {
      showMessage('请选择一行数据！！');
    }
  }

  // 关闭协同
  async function handleClose() {
    if (getSelectRows().length && getSelectRows().length >= 1) {
      const selIds = getSelectRows().map((item: any) => item.id).join(',');
      const res = await closeXt({ selIds: selIds });
      showMessage(res.message);
      handleSuccess();
    } else {
      showMessage('请选择一行数据！！');
    }
  }

  async function handleDownload(record: Recordable) {
    const { ctxAdminPath } = useGlobSetting();
    try {
      // 根据bizKey和bizType获取文件列表
      const fileList = await uploadFileList(
        {
          bizKey: record.id,
          bizType: 'xtLog_pdf_file',
        },
        ctxAdminPath + '/file/fileList',
      );

      if (fileList && fileList.length > 0) {
        // 如果有多个文件，下载第一个文件，或者可以让用户选择
        const file = fileList[0];
        downloadByUrl({ url: ctxAdminPath + '/file/download/' + file.id });
      } else {
        showMessage('没有找到相关文件');
      }
    } catch (error) {
      console.error('下载文件失败:', error);
      showMessage('下载文件失败');
    }
  }
  function handleSuccess() {
    reload();
  }
</script>
