<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'bankdirectlink:payapply:billManagerPayApplyC1:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.parentId }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsBankdirectlinkPayapplyBillManagerPayApplyC1List">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { billManagerPayApplyC1Delete, billManagerPayApplyC1ListData } from '/@/api/billmanager/bankdirectlink/payapply/billManagerPayApplyC1';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('bankdirectlink.payapply.billManagerPayApplyC1');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('付款申请单拆分表C1管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('明细ID'),
        field: 'parentId',
        component: 'Input',
      },
      {
        label: t('科目'),
        field: 'subject',
        component: 'Input',
      },
      {
        label: t('支付类型'),
        field: 'footType',
        component: 'Input',
      },
      {
        label: t('原币金额'),
        field: 'orgAmount',
        component: 'Input',
      },
      {
        label: t('拆分金额'),
        field: 'sumAmount',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('明细ID'),
      dataIndex: 'parentId',
      key: 'a.parent_id',
      sorter: true,
      width: 230,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('科目'),
      dataIndex: 'subject',
      key: 'a.subject',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('支付类型'),
      dataIndex: 'footType',
      key: 'a.foot_type',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('原币金额'),
      dataIndex: 'orgAmount',
      key: 'a.org_amount',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('拆分金额'),
      dataIndex: 'sumAmount',
      key: 'a.sum_amount',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑付款申请单拆分表C1'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'bankdirectlink:payapply:billManagerPayApplyC1:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除付款申请单拆分表C1'),
        popConfirm: {
          title: t('是否确认删除付款申请单拆分表C1'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'bankdirectlink:payapply:billManagerPayApplyC1:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload }] = useTable({
    api: billManagerPayApplyC1ListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { id: record.id };
    const res = await billManagerPayApplyC1Delete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
