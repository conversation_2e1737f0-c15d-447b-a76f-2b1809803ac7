<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable" @fetchSuccess="fetchSuccess">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button @click="expandAll" :title="t('展开一级')">
          <Icon icon="bi:chevron-double-down" /> {{ t('展开') }}
        </a-button>
        <a-button @click="collapseAll" :title="t('展开全部')">
          <Icon icon="bi:chevron-double-up" /> {{ t('折叠') }}
        </a-button>
        <!-- <a-button type="primary" @click="handleForm({})" v-auth="'bas:pos:basPosition:edit'">
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button> -->
        <a-button type="default" @click="handlePrint({})" v-auth="'bas:pos:basPosition:edit'">
          <Icon icon="simple-line-icons:printer" /> {{ t('货位打印') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <span class="cursor-pointer" @click="expandCollapse(record)">
          ( {{ record.posCode }} )
        </span>
        <a @click="handleForm({ posCode: record.posCode })">
          {{ record.posCode }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
    <PrintModal @register="registerPrintModal" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsBasPosBasPositionList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, watch, nextTick } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { basPositionDelete, basPositionListData } from '/@/api/bas/pos/basPosition';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import PrintModal from '/@/components/print/printModal.vue';

  const props = defineProps({
    treeCode: String,
  });

  const { t } = useI18n('bas.pos.basPosition');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: router.currentRoute.value.meta.title || t('货位档案管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('货位编码'),
        field: 'posCode',
        component: 'Input',
      },
      {
        label: t('货位名称'),
        field: 'posName',
        component: 'Input',
      },
      {
        label: t('仓库编码'),
        field: 'wareHouse.cwhname',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('货位编码'),
      dataIndex: 'posCode',
      width: 230,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('货位名称'),
      dataIndex: 'posName',
      width: 230,
      align: 'left',
    },
    {
      title: t('仓库名称'),
      dataIndex: 'wareHouse.cwhname',
      width: 130,
      align: 'left',
    },
    {
      title: t('顺序号'),
      dataIndex: 'treeSort',
      width: 130,
      align: 'left',
    },
    {
      title: t('备注'),
      dataIndex: 'memo',
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑货位档案'),
        onClick: handleForm.bind(this, { posCode: record.posCode }),
        auth: 'bas:pos:basPosition:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除货位档案'),
        popConfirm: {
          title: t('是否确认删除货位档案'),
          confirm: handleDelete.bind(this, { posCode: record.posCode }),
        },
        auth: 'bas:pos:basPosition:edit',
      },
      {
        icon: 'fluent:add-circle-24-regular',
        title: t('新建下级货位档案'),
        onClick: handleForm.bind(this, {
          parentCode: record.id,
          parentName: record.posName,
        }),
        auth: 'bas:pos:basPosition:edit',
      },
    ],
  };

  const [registerPrintModal, { openModal: openPrintModal }] = useModal();
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [
    registerTable,
    { reload, expandAll, collapseAll, expandCollapse, getForm, getSelectRows },
  ] = useTable({
    api: basPositionListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    //actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    isTreeTable: true,
    pagination: true,
    canResize: true,
    rowSelection: {
      type: 'checkbox',
    },
  });

  watch(
    () => props.treeCode,
    async () => {
      await getForm().setFieldsValue({
        posCode: props.treeCode,
      });
      reload();
    },
  );

  function fetchSuccess() {
    if (props.treeCode) {
      nextTick(expandAll);
    }
  }

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const res = await basPositionDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  // 货位打印
  async function handlePrint() {
    let arr = await getSelectRows();
    if (getSelectRows().length == 0) {
      showMessage(t('请先选择一行数据'));
      return;
    }
    const idsArr = arr.map((item) => {
      return item.posCode;
    });
    let params = {
      ids: idsArr.join(','),
      title: '货位标签打印',
      height: '0.9',
      width: '0.9',
      fileName: 'printHW',
    };
    openPrintModal(true, params);
  }

  function handleSuccess() {
    reload();
  }
</script>
