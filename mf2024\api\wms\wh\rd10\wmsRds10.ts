/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WmsRds10 extends BasicModel<WmsRds10> {
  parentId?: string; // 单据编号
  invCode?: string; // 存货编码
  iqty?: number; // 数量
  inum?: number; // 件数
  cbatch?: string; // 批次
  posCode?: string; // 货位
  ichangeRate?: number; // 换算率
  sumQty?: number; // 累计入库数量
  sumNum?: number; // 累计入库件数
  maxTpSeq?: number; // 最大托盘序号
  createByName?: string; // 制单人
  updateByName?: string; // 修改人
  packSize?: number; // 托盘容量
  pieceQty?: number; // 单件容量
  packName?: string; // 包装名称
  WmsRd10?: any[]; // 子对象列表
}

export const wmsRds10ListData = (params?: WmsRds10 | any) =>
  defHttp.post<Page<WmsRds10>>({ url: adminPath + '/wms/wh/rd10/wmsRds10/listData', params });

export const wmsRds10Form = (params?: WmsRds10 | any) =>
  defHttp.get<WmsRds10>({ url: adminPath + '/wms/wh/rd10/wmsRds10/form', params });
