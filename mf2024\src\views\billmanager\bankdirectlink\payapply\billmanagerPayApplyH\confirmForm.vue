<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'bankdirectlink:payapply:billmanagerPayApplyH:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="95%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <div class="page-container">
      <!-- 表单区域 -->
      <div class="form-section">
        <BasicForm @register="registerForm" />
      </div>

      <!-- 三个表格区域，统一布局 -->
      <div class="tables-section">
        <div class="table-column table-column-detail">
          <div class="table-header">
            <h3 class="table-title">{{ t('明细') }}</h3>
          </div>
          <div class="table-content">
            <BasicTable
              @register="registerBillmanagerPayApplyCTable"
              @row-click="handleBillmanagerPayApplyCRowClick"
              :canResize="true"
              :isCanResizeParent="true"
              :resizeHeightOffset="0"
              :scroll="{ x: 'max-content', y: 300 }"
            />
          </div>
        </div>

        <div class="table-column table-column-payment">
          <div class="table-header">
            <h3 class="table-title">{{ t('付款方式') }}</h3>
          </div>
          <div class="table-content">
            <BasicTable
              @register="registerPaymentMethodTable"
              @row-click="handlePaymentMethodRowClick"
              :canResize="true"
              :isCanResizeParent="true"
              :resizeHeightOffset="0"
              :scroll="{ x: 'max-content', y: 300 }"
            />
          </div>
        </div>

        <div class="table-column table-column-bill">
          <div class="table-header">
            <h3 class="table-title">{{ t('票据') }}</h3>
          </div>
          <div class="table-content">
            <BasicTable
              @register="registerBillTable"
              @row-click="handleBillRowClick"
              :canResize="true"
              :isCanResizeParent="true"
              :resizeHeightOffset="0"
              :scroll="{ x: 'max-content', y: 300 }"
            />
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <BpmButton
        v-if="!isViewMode"
        v-model:bpmEntity="record"
        bpmEntityKey="id"
        formKey="peyApply"
        completeText="提交"
        :completeModal="true"
        :loading="loadingRef"
        :auth="'bankdirectlink:payapply:billmanagerPayApplyH:edit'"
        @validate="handleValidate"
        @complete="handleSubmit"
        @success="handleSuccess"
        @close="closeDrawer"
      />
    </template>
    <ListSelect
      ref="listSelectRef"
      selectType="contractListSelect"
      :checkbox="false"
      @select="handleSelect"
      :selectList="selectListRef"
      :queryParams="queryParams"
      v-show="false"
    />

    <!-- 付款详情弹窗 -->
    <PaymentModal @register="registerPaymentModal" @success="handlePaymentSuccess" />

    <!-- 票据选择弹窗 -->
    <PayBillModal @register="registerBillModal" @success="handleBillSuccess" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsBankdirectlinkPayapplyBillmanagerPayApplyHForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { useModal } from '/@/components/Modal';
  import {
    BillmanagerPayApplyH,
    billmanagerPayApplyHSave,
    billmanagerPayApplyHForm,
  } from '/@/api/billmanager/bankdirectlink/payapply/billmanagerPayApplyH';
  import { BpmButton } from '/@/components/Bpm';
  import { ListSelect } from '/@/components/ListSelect';
  import { officeTreeData } from '/@/api/sys/office';
  import { OfficeTypeEnum } from '/@/enums/defEnum';
  import PaymentModal from './PaymentModal.vue';
  import PayBillModal from './PayBillModal.vue';
  import {
    billManagerPayApplyC1ListData,
    billManagerPayApplyC1Delete,
  } from '/@/api/billmanager/bankdirectlink/payapply/billManagerPayApplyC1';
  import {
    billManagerPayApplyC2Delete,
    billManagerPayApplyC2ListData,
  } from '/@/api/billmanager/bankdirectlink/payapply/billManagerPayApplyC2';
  import { billmanagerUseOfFundsTreeData } from '/@/api/billmanager/bankdirectlink/useoffunds/billmanagerUseOfFunds';
  import { useDict } from '/@/components/Dict';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('bankdirectlink.payapply.billmanagerPayApplyH');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<BillmanagerPayApplyH>({} as BillmanagerPayApplyH);
  const loadingRef = ref(false);
  const selectListRef = ref<any>([]);
  let queryParams = ref<any>({});
  const listSelectRef = ref<any>(null);
  const { initDict, getDictLabel } = useDict();

  // 控制页面是否为查看模式，默认为false
  const isViewMode = ref(false);

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('票据确认') : t('票据确认'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('申请单号'),
      field: 'id',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      dynamicDisabled: true,
    },
    {
      label: t('申请日期'),
      field: 'ddate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        showTime: false,
      },
      dynamicDisabled: true,
      required: true,
    },
    {
      label: t('申请部门'),
      field: 'deptCode',
      fieldLabel: 'office.officeName',
      component: 'TreeSelect',
      componentProps: {
        // maxlength: 100,
        api: officeTreeData,
        params: { isLoadUser: true, userIdPrefix: '', isAll: true },
        canSelectParent: false,
        // treeCheckable: true,
        allowClear: true,
        maxlength: 64,
      },
      required: true,
      dynamicDisabled: true,
    },
    {
      label: t('资金用途'),
      field: 'useOfFunds',
      fieldLabel: 'billmanagerUseOfFunds.name',
      component: 'TreeSelect',
      componentProps: {
        // maxlength: 100,
        api: billmanagerUseOfFundsTreeData,
        params: { isAll: true },
        canSelectParent: false,
        // treeCheckable: true,
        allowClear: true,
        maxlength: 64,
      },
      dynamicDisabled: true,
    },
    {
      label: t('供应商'),
      field: 'venCode',
      fieldLabel: 'vendor.officeName',
      component: 'TreeSelect',
      componentProps: {
        api: officeTreeData,
        params: { isLoadUser: true, userIdPrefix: '', isAll: true, officeTypes: OfficeTypeEnum.WL },
        canSelectParent: false,
        // treeCheckable: true,
        allowClear: true,
        maxlength: 64,
      },
      dynamicDisabled: true,
    },
    {
      label: t('备注'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 4000,
      },
      colProps: { lg: 24, md: 24 },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate, updateSchema }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerBillmanagerPayApplyCTable, billmanagerPayApplyCTable] = useTable({
    actionColumn: {
      width: 60,
      actions: (_record: Recordable) => {
        // 如果是查看模式，不显示任何操作按钮
        if (isViewMode.value) {
          return [];
        }
        return [
          // {
          //   icon: 'i-ant-design:delete-outlined',
          //   color: 'error',
          //   popConfirm: {
          //     title: '是否确认删除',
          //     confirm: handleBillmanagerPayApplyCDelete.bind(this, _record),
          //   },
          //   auth: 'bankdirectlink:payapply:billmanagerPayApplyH:edit',
          //   ifShow: () => record.value.status !== '4',
          // },
          {
            label: '操作',
            onClick: handleAction.bind(this, _record),
          },
        ];
      },
    },
    rowKey: 'id',
    pagination: false,
    bordered: true,
    size: 'small',
    inset: true,
  });

  // 付款方式表配置
  const [registerPaymentMethodTable, paymentMethodTable] = useTable({
    actionColumn: {
      width: 80,
      actions: (_record: Recordable) => {
        // 如果是查看模式，不显示任何操作按钮
        if (isViewMode.value) {
          return [];
        }
        return [
          {
            icon: 'i-ant-design:delete-outlined',
            color: 'error',
            popConfirm: {
              title: '是否确认删除',
              confirm: handlePaymentMethodDelete.bind(this, _record),
            },
            auth: 'bankdirectlink:payapply:billmanagerPayApplyH:edit',
          },
          {
            label: '操作',
            onClick: handleBillAction.bind(this, _record),
          },
        ];
      },
    },
    rowKey: 'id',
    pagination: false,
    bordered: true,
    size: 'small',
    inset: true,
  });

  // 票据表配置
  const [registerBillTable, billTable] = useTable({
    actionColumn: {
      width: 80,
      actions: (_record: Recordable) => {
        // 如果是查看模式，不显示任何操作按钮
        if (isViewMode.value) {
          return [];
        }
        return [
          {
            icon: 'i-ant-design:delete-outlined',
            color: 'error',
            popConfirm: {
              title: '是否确认删除',
              confirm: handleBillDelete.bind(this, _record),
            },
            auth: 'bankdirectlink:payapply:billmanagerPayApplyH:edit',
          },
        ];
      },
    },
    rowKey: 'id',
    pagination: false,
    bordered: true,
    size: 'small',
    inset: true,
  });

  // 付款详情弹窗相关方法
  const [registerPaymentModal, { openModal: openPaymentModal }] = useModal();

  // 票据选择弹窗相关方法
  const [registerBillModal, { openModal: openBillModal }] = useModal();

  async function setBillmanagerPayApplyCTableData(_res: Recordable) {
    billmanagerPayApplyCTable.setColumns([
      {
        title: t('行号'),
        dataIndex: 'irowno',
        width: 60,
        align: 'left',
        editRow: false,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 8,
        },
        editRule: false,
      },
      {
        title: t('款项类型'),
        dataIndex: 'payType',
        width: 80,
        align: 'left',
        dictType: 'mf_payType', // 添加字典类型，用于显示标签值
        editRow: false,
        editComponent: 'Select',
        editComponentProps: {
          maxlength: 10,
          dictType: 'mf_payType',
          // required: true,
        },
        editRule: true,
        customRender: ({ record }) => {
          // 优先显示字典标签，如果没有则显示原值
          return (
            record.payTypeLabel || getDictLabel('mf_payType', record.payType) || record.payType
          );
        },
      },
      // {
      //   title: t('供应商'),
      //   dataIndex: 'venCode',
      //   width: 130,
      //   align: 'left',
      //   editRow: true,
      //   editComponent: 'Input',
      //   editComponentProps: {
      //     maxlength: 100,
      //   },
      //   editRule: false,
      // },
      {
        title: t('币种'),
        dataIndex: 'cexchName',
        width: 80,
        align: 'left',
        dictType: 'mf_bizhong', // 添加字典类型，用于显示标签值
        editRow: false,
        editComponent: 'Select',
        editComponentProps: {
          maxlength: 10,
          dictType: 'mf_bizhong',
          // required: true,
        },
        editRule: false,
        customRender: ({ record }) => {
          // 优先显示字典标签，如果没有则显示原值
          return (
            record.cexchNameLabel ||
            getDictLabel('mf_bizhong', record.cexchName) ||
            record.cexchName
          );
        },
      },
      {
        title: t('原币金额'),
        dataIndex: 'orgAmount',
        width: 80,
        align: 'left',
        editRow: false,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 16,
          // required: true,
        },
        editRule: true,
      },
      {
        title: t('部门'),
        dataIndex: 'office.officeName',
        width: 80,
        align: 'left',
        editRow: false,
        editComponent: 'TreeSelect',
        editComponentProps: {
          api: officeTreeData,
          params: { isLoadUser: true, userIdPrefix: '', isAll: true },
          canSelectParent: false,
          // treeCheckable: true,
          allowClear: true,
          // params: { postType: '1' },
          maxlength: 64,
          // required: true,
        },
        editRule: false,
      },
      {
        title: t('备注'),
        dataIndex: 'remarks',
        width: 130,
        align: 'left',
        editRow: false,
        editComponent: 'InputTextArea',
        editComponentProps: {
          maxlength: 4000,
        },
        editRule: false,
      },
      {
        title: t('合同号'),
        dataIndex: 'contractId',
        width: 130,
        align: 'left',
        editRow: false,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 64,
          // required: true,
        },
        editRule: false,
      },
      // {
      //   title: t('资金用途'),
      //   dataIndex: 'useOfFunds',
      //   width: 130,
      //   align: 'left',
      //   editRow: true,
      //   editComponent: 'Select',
      //   editComponentProps: {
      //     dictType: 'mf_amount_use',
      //     maxlength: 50,
      //     required: true,
      //   },
      //   editRule: true,
      // },
      {
        title: t('U8生单状态'),
        dataIndex: 'u8Status',
        width: 130,
        align: 'left',
        editRow: false,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 10,
        },
        editRule: false,
      },
    ]);

    // 初始化字典数据
    await initDict(['mf_payType', 'mf_bizhong']);

    // 处理字典字段的显示值
    const tableData = (record.value.billmanagerPayApplyCList || []).map((item: any) => {
      const processedItem = {
        ...item,
      };

      // 为字典字段设置显示标签
      if (item.payType) {
        processedItem.payTypeLabel = getDictLabel('mf_payType', item.payType);
      }
      if (item.cexchName) {
        processedItem.cexchNameLabel = getDictLabel('mf_bizhong', item.cexchName);
      }

      console.log('处理字典字段:', {
        原始: { payType: item.payType, cexchName: item.cexchName },
        处理后: {
          payTypeLabel: processedItem.payTypeLabel,
          cexchNameLabel: processedItem.cexchNameLabel,
        },
      });

      return processedItem;
    });

    console.log('设置表格数据:', tableData);
    billmanagerPayApplyCTable.setTableData(tableData);
    // billmanagerPayApplyCTable.setTableData(record.value.billmanagerPayApplyCList || []);
  }

  function handleBillmanagerPayApplyCRowClick(record: Recordable) {
    // 查看模式下不允许编辑
    if (!isViewMode.value) {
      record.onEdit?.(true, false);
    }
  }

  // 获取下一个行号
  function getNextRowNo() {
    const dataSource = billmanagerPayApplyCTable.getDataSource();
    if (dataSource.length === 0) {
      return 1;
    }
    // 找到当前最大的行号
    const maxRowNo = Math.max(...dataSource.map((item: any) => parseInt(item.irowno) || 0));
    return maxRowNo + 1;
  }

  async function getBillmanagerPayApplyCList() {
    let billmanagerPayApplyCList: Recordable[] = [];

    console.log('开始处理 billmanagerPayApplyCList 数据');
    const dataSource = billmanagerPayApplyCTable.getDataSource();
    console.log('表格数据源:', dataSource);

    // 直接返回数据，不进行验证
    console.log('跳过表格验证，直接返回数据');
    for (const record of dataSource) {
      console.log('处理记录:', record);
      billmanagerPayApplyCList.push({
        ...record,
        id: !!record.isNewRecord ? '' : record.id,
      });
    }

    for (const record of billmanagerPayApplyCTable.getDelDataSource()) {
      if (!!record.isNewRecord) continue;
      billmanagerPayApplyCList.push({
        ...record,
        status: '1',
      });
    }

    console.log('最终返回数据:', billmanagerPayApplyCList);
    return billmanagerPayApplyCList;
  }

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data: any) => {
    // 判断 data.isView 是否为 true，如果 true，页面 所有的操作按钮不展示 默认 isView 为 false
    isViewMode.value = data.isView === true;
    console.log('设置查看模式:', isViewMode.value, 'data.isView:', data.isView);

    setDrawerProps({ loading: true });
    await resetFields();
    const res = await billmanagerPayApplyHForm(data);
    record.value = (res.billmanagerPayApplyH || {}) as BillmanagerPayApplyH;
    record.value.__t = new Date().getTime();
    console.log('表单数据:', record.value);
    setFieldsValue(record.value);

    // 如果是查看模式，禁用表单中的备注字段编辑
    if (isViewMode.value) {
      updateSchema({
        field: 'remarks',
        componentProps: {
          disabled: true,
        },
      });
    } else {
      updateSchema({
        field: 'remarks',
        componentProps: {
          disabled: false,
        },
      });
    }

    setBillmanagerPayApplyCTableData(res);

    const resC1 = await billManagerPayApplyC1ListData({
      parentCode: record.value.id,
    });

    const resC2 = await billManagerPayApplyC2ListData({
      parentCode: record.value.id,
    });
    record.value.paymentMethodList = resC1.list || [];
    record.value.billList = resC2.list || [];
    // 设置付款方式表和票据表数据
    await setPaymentMethodTableData();
    await setBillTableData();
    setDrawerProps({ loading: false });
  });

  async function handleValidate(_event: any, formData: any) {
    try {
      const data = await validate();
      console.log('data===111', data);
      formData(true, data); // 将表单数据传递给 BpmButton
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    }
  }

  async function handleSubmit(event: any) {
    try {
      loadingRef.value = true;

      console.log('开始表单验证...');
      // const data = event?.formData || (await validate()); // 接受 BpmButton 传递过来的表单数据
      const validateResult = await validate();
      console.log('表单验证结果:', validateResult);

      const data = Object.assign(validateResult, event?.formData);
      console.log('合并后的数据:', data);

      // console.log('data===', data, event, await validate());
      data.bpm = Object.assign(data.bpm || {}, record.value.bpm); // 流程信息
      data.status = record.value.status; // 提交状态
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        djno: record.value.djno,
      };

      console.log('开始获取 billmanagerPayApplyCList...');
      data.billmanagerPayApplyCList = await getBillmanagerPayApplyCList();
      console.log('获取 billmanagerPayApplyCList 成功');

      // console.log('submit', params, data, record);
      const res = await billmanagerPayApplyHSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      console.error('handleSubmit 出错:', error);
      if (error && error.errorFields) {
        console.error('验证错误字段:', error.errorFields);
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      loadingRef.value = false;
      setDrawerProps({ confirmLoading: false });
    }
  }

  async function handleSuccess() {
    emit('success');
  }

  async function handleSelect(selectData: any) {
    console.log(selectData);
    // billmanagerPayApplyCTable.updateTableDataRecord(record.value.id, {
    //   ...record.value,
    //   contractId: selectData[0].id,
    // });

    // 为每个选中的合同添加一行，行号递增
    selectData.forEach((item: any) => {
      billmanagerPayApplyCTable.insertTableDataRecord({
        editable: true,
        id: new Date().getTime() + Math.floor(Math.random() * 1000), // 确保每行的id唯一
        contractId: item.id,
        irowno: getNextRowNo(), // 使用递增行号
        isNewRecord: true,
      });
    });
  }

  async function handleAction(_record: Recordable) {
    const res = await billManagerPayApplyC1ListData({
      parentId: _record.id,
    });
    _record.paymentDetailList = res.list || [];
    // _record.vendor: {
    //     officeName: record.value.vendor.officeName,
    //   }
    _record.vendor = {
      officeName: record.value.vendor.officeName,
    };
    // 打开付款详情弹窗，传递当前记录数据
    console.log('handleAction', _record);
    openPaymentModal(true, _record);
  }

  // 处理付款详情弹窗成功回调
  async function handlePaymentSuccess(data: any) {
    console.log('付款详情保存成功:', data);
    showMessage('付款详情保存成功');

    const res = await billManagerPayApplyC1ListData({
      parentId: data.originalRecord.id,
    });

    // 更新 record 中的数据
    record.value.paymentMethodList = res.list || [];

    // 重新设置表格数据，包含字典字段处理
    await setPaymentMethodTableData();
  }

  // 票据操作方法
  async function handleBillAction(_record: Recordable) {
    console.log('handleBillAction', record);

    const res = await billManagerPayApplyC2ListData({
      parentId: _record.id,
    });
    _record.billList = res.list || [];
    // 打开票据选择弹窗，传递当前记录数据
    _record = {
      ..._record,
      vendor: {
        officeName: record.value.vendor.officeName,
      },
    };
    openBillModal(true, _record);
  }

  // 处理票据选择弹窗成功回调
  async function handleBillSuccess(data: any) {
    console.log('票据选择保存成功:', data);
    showMessage('票据选择保存成功');

    const res = await billManagerPayApplyC2ListData({
      parentCode: data.originalRecord.parentCode,
    });
    billTable.setTableData(res.list || []);
  }

  // 付款方式表相关方法
  async function setPaymentMethodTableData() {
    paymentMethodTable.setColumns([
      {
        title: t('行号'),
        dataIndex: 'irowno',
        width: 80,
        align: 'left',
      },
      {
        title: t('付款方式'),
        dataIndex: 'footType',
        width: 80,
        align: 'left',
        dictType: 'mf_pay_method',
        customRender: ({ record }) => {
          // 优先显示字典标签，如果没有则显示原值
          return (
            record.footTypeLabel ||
            getDictLabel('mf_pay_method', record.footType) ||
            record.footType
          );
        },
      },
      {
        title: t('科目'),
        dataIndex: 'subject',
        width: 80,
        align: 'left',
        dictType: 'csettleType',
        customRender: ({ record }) => {
          // 优先显示字典标签，如果没有则显示原值
          return (
            record.subjectLabel || getDictLabel('csettleType', record.subject) || record.subject
          );
        },
      },
      {
        title: t('金额'),
        dataIndex: 'orgAmount',
        width: 80,
        align: 'right',
        format: 'currency',
      },
    ]);

    // 初始化字典数据
    await initDict(['mf_pay_method', 'csettleType']);

    // 处理字典字段的显示值
    const tableData = (record.value.paymentMethodList || []).map((item: any) => {
      const processedItem = {
        ...item,
      };

      // 为字典字段设置显示标签
      if (item.footType) {
        processedItem.footTypeLabel = getDictLabel('mf_pay_method', item.footType);
      }
      if (item.subject) {
        processedItem.subjectLabel = getDictLabel('csettleType', item.subject);
      }

      console.log('处理付款方式字典字段:', {
        原始: { footType: item.footType, subject: item.subject },
        处理后: {
          footTypeLabel: processedItem.footTypeLabel,
          subjectLabel: processedItem.subjectLabel,
        },
      });

      return processedItem;
    });

    console.log('设置付款方式表格数据:', tableData);
    paymentMethodTable.setTableData(tableData);
  }

  function handlePaymentMethodRowClick(record: Recordable) {
    // 查看模式下不允许编辑
    if (!isViewMode.value) {
      record.onEdit?.(true, false);
    }
  }

  async function handlePaymentMethodDelete(record: Recordable) {
    // 调用删除方法
    const res = await billManagerPayApplyC1Delete({ id: record.id });

    if (res.result === 'true') {
      showMessage(res.message);
      paymentMethodTable.deleteTableDataRecord(record);
    }

    // paymentMethodTable.deleteTableDataRecord(record);
  }

  // 票据表相关方法
  async function setBillTableData() {
    billTable.setColumns([
      {
        title: t('票据号'),
        dataIndex: 'clink',
        width: 120,
        minWidth: 120,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 50,
        },
        editRule: true,
        ellipsis: true,
      },
      {
        title: t('承兑银行'),
        dataIndex: 'cbank',
        width: 100,
        minWidth: 100,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 50,
        },
        editRule: true,
        ellipsis: true,
      },
      {
        title: t('到期日期'),
        dataIndex: 'dexpiredate',
        width: 90,
        minWidth: 90,
        align: 'center',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 50,
        },
        editRule: true,
        ellipsis: true,
      },
      {
        title: t('应答日期'),
        dataIndex: 'dreceiptdate',
        width: 90,
        minWidth: 90,
        align: 'center',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 50,
        },
        editRule: true,
        ellipsis: true,
      },
      {
        title: t('金额'),
        dataIndex: 'orgAmount',
        width: 80,
        minWidth: 80,
        align: 'right',
        editRow: false,
        editComponent: 'Input',
        editComponentProps: {
          type: 'number',
          min: 0,
          step: 0.01,
        },
        editRule: true,
        format: 'currency',
        ellipsis: true,
      },
    ]);
    billTable.setTableData(record.value.billList || []);
  }

  function handleBillRowClick(record: Recordable) {
    // 查看模式下不允许编辑
    if (!isViewMode.value) {
      record.onEdit?.(true, false);
    }
  }

  async function handleBillDelete(record: Recordable) {
    // billTable.deleteTableDataRecord(record);
    const res = await billManagerPayApplyC2Delete({ id: record.id });
    if (res.result === 'true') {
      showMessage(res.message);
      billTable.deleteTableDataRecord(record);
    }
  }
</script>

<style scoped>
  /* 页面整体容器 */
  .page-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 0;
    gap: 16px;
  }

  /* 表单区域 */
  .form-section {
    flex-shrink: 0;
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  /* 表格区域容器 */
  .tables-section {
    flex: 1;
    display: flex;
    gap: 16px;
    min-height: 0;
    align-items: stretch;
  }

  /* 表格列容器 */
  .table-column {
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    min-height: 400px;
  }

  /* 表格列宽度分配 */
  .table-column-detail {
    flex: 4;
    min-width: 400px;
  }

  .table-column-payment {
    flex: 3;
    min-width: 300px;
  }

  .table-column-bill {
    flex: 3;
    min-width: 500px; /* 票据表格需要更多空间显示多列 */
  }

  /* 表格标题区域 */
  .table-header {
    flex-shrink: 0;
    padding: 16px 20px 12px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
  }

  .table-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    line-height: 1.5;
  }

  /* 表格内容区域 */
  .table-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    padding: 16px;
    height: 100%;
  }

  /* BasicTable组件样式优化 */
  .table-content :deep(.jeesite-basic-table) {
    height: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 6px;
    overflow: hidden;
    min-height: 350px; /* 确保最小高度 */
  }

  .table-content :deep(.ant-table-wrapper) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .table-content :deep(.ant-table) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .table-content :deep(.ant-table-container) {
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    overflow: hidden; /* 确保容器不会阻止滚动条显示 */
  }

  .table-content :deep(.ant-table-content) {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 确保内容区域不会阻止滚动条显示 */
  }

  /* 表头样式 */
  .table-content :deep(.ant-table-header) {
    flex-shrink: 0;
    background: #fafafa;
  }

  .table-content :deep(.ant-table-thead > tr > th) {
    background: #fafafa;
    font-weight: 600;
    color: #262626;
    border-bottom: 2px solid #f0f0f0;
  }

  /* 表格主体内容区域 */
  .table-content :deep(.ant-table-body) {
    flex: 1;
    overflow: auto !important; /* 强制启用滚动 */
    min-height: 300px; /* 确保最小高度，即使数据少也占满空间 */
    height: 100%;
    max-height: 300px; /* 限制最大高度以触发滚动 */
  }

  /* 确保表格滚动容器正确设置 */
  .table-content :deep(.ant-table-scroll) {
    height: 100%;
    overflow: auto !important; /* 强制启用滚动 */
  }

  /* 表格tbody区域样式 */
  .table-content :deep(.ant-table-tbody) {
    min-height: 250px; /* 确保tbody有足够高度 */
  }

  /* 强制显示滚动条 */
  .table-content :deep(.ant-table-container .ant-table-body) {
    overflow-x: auto !important;
    overflow-y: auto !important;
  }

  /* 确保表格内容宽度超出容器时显示滚动条 */
  .table-content :deep(.ant-table) {
    min-width: 100%;
  }

  /* 滚动条样式优化 */
  .table-content :deep(.ant-table-body)::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .table-content :deep(.ant-table-body)::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 4px;
  }

  .table-content :deep(.ant-table-body)::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 4px;
  }

  .table-content :deep(.ant-table-body)::-webkit-scrollbar-thumb:hover {
    background: #bfbfbf;
  }

  /* 表格行样式优化 */
  .table-content :deep(.ant-table-tbody > tr:hover > td) {
    background-color: #f5f7fa;
  }

  .table-content :deep(.ant-table-tbody > tr.ant-table-row-selected > td) {
    background-color: #e6f7ff;
  }

  /* 票据表格特殊样式 */
  .table-column-bill .table-content :deep(.ant-table) {
    font-size: 12px; /* 稍微缩小字体以适应更多列 */
  }

  .table-column-bill .table-content :deep(.ant-table-thead > tr > th) {
    padding: 8px 4px; /* 减少表头padding */
    font-size: 12px;
    white-space: nowrap;
  }

  .table-column-bill .table-content :deep(.ant-table-tbody > tr > td) {
    padding: 8px 4px; /* 减少单元格padding */
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 确保票据表格有水平滚动 */
  .table-column-bill .table-content :deep(.ant-table-container) {
    overflow-x: auto !important;
    overflow-y: hidden;
  }

  .table-column-bill .table-content :deep(.ant-table-body) {
    overflow-x: auto !important;
    overflow-y: auto !important;
  }

  .table-column-bill .table-content :deep(.ant-table-scroll) {
    overflow-x: auto !important;
  }

  /* 强制票据表格显示滚动条 */
  .table-column-bill .table-content :deep(.ant-table) {
    width: 100%;
    min-width: 480px; /* 设置最小宽度，强制显示滚动条 */
  }

  /* 响应式设计 */
  @media (max-width: 1024px) {
    .tables-section {
      flex-direction: column;
      gap: 12px;
    }

    .table-column {
      min-height: 300px;
    }

    .form-section {
      padding: 12px;
    }

    .table-content {
      padding: 12px;
    }
  }
</style>
