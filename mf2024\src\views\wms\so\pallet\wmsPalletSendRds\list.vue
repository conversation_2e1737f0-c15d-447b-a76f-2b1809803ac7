<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <!-- <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'wms/so:pallet:wmsPalletSendRds:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template> -->
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.cbatch }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsWmsSoPalletWmsPalletSendRdsList">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { wmsPalletSendRdsDelete, wmsPalletSendRdsListData } from '/@/api/wms/so/pallet/wmsPalletSendRds';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('wms/so.pallet.wmsPalletSendRds');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('托盘发出记录管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('托盘码'),
        field: 'cbatch',
        component: 'Input',
      },
      {
        label: t('车次'),
        field: 'carDjno',
        component: 'Input',
      },
      {
        label: t('发货单号'),
        field: 'fhDjno',
        component: 'Input',
      },
      {
        label: t('车牌号'),
        field: 'carNo',
        component: 'Input',
      },
      {
        label: t('客户编码'),
        field: 'cusCode',
        component: 'Input',
      },
      {
        label: t('客户名称'),
        field: 'cusName',
        component: 'Input',
      },
      {
        label: t('存货编码'),
        field: 'invCode',
        component: 'Input',
      },
      {
        label: t('存货名称'),
        field: 'invName',
        component: 'Input',
      },
      {
        label: t('合同号'),
        field: 'contract',
        component: 'Input',
      },
      {
        label: t('发出仓库'),
        field: 'whName',
        component: 'Input',
      },
      {
        label: t('发出人'),
        field: 'createByName',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('托盘码'),
      dataIndex: 'cbatch',
      key: 'a.cbatch',
      sorter: true,
      width: 180,
      align: 'left',
      // slot: 'firstColumn',
    },
    {
      title: t('车次'),
      dataIndex: 'carDjno',
      key: 'a.car_djno',
      sorter: true,
      width: 110,
      align: 'left',
    },
    {
      title: t('发货单号'),
      dataIndex: 'fhDjno',
      key: 'a.fh_djno',
      sorter: true,
      width: 110,
      align: 'left',
    },
    {
      title: t('车牌号'),
      dataIndex: 'carNo',
      key: 'a.car_no',
      sorter: true,
      width: 110,
      align: 'left',
    },
    {
      title: t('客户编码'),
      dataIndex: 'cusCode',
      key: 'a.cus_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('客户名称'),
      dataIndex: 'cusName',
      key: 'a.cus_name',
      sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: t('存货编码'),
      dataIndex: 'invCode',
      key: 'a.inv_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('存货名称'),
      dataIndex: 'invName',
      key: 'a.inv_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('规格型号'),
      dataIndex: 'invStd',
      key: 'a.inv_std',
      sorter: true,
      width: 110,
      align: 'left',
    },
    {
      title: t('单位'),
      dataIndex: 'unitName',
      key: 'a.unit_name',
      sorter: true,
      width: 80,
      align: 'left',
    },
    {
      title: t('合同号'),
      dataIndex: 'contract',
      key: 'a.contract',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('发出仓库'),
      dataIndex: 'whName',
      key: 'a.wh_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('发出重量'),
      dataIndex: 'fweight',
      key: 'a.fweight',
      sorter: true,
      width: 130,
      align: 'right',
      ifShow: false,
    },
    {
      title: t('发出人'),
      dataIndex: 'createByName',
      key: 'a.create_by_name',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('发出日期'),
      dataIndex: 'createDate',
      key: 'a.create_date',
      sorter: true,
      width: 130,
      align: 'right',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑托盘发出记录'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'wms/so:pallet:wmsPalletSendRds:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload }] = useTable({
    api: wmsPalletSendRdsListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    // actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { id: record.id };
    const res = await wmsPalletSendRdsDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
