<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicModal
    v-bind="$attrs"
    :title="t('EXCEL 复制打印')"
    :okText="t('确认')"
    @register="registerModal"
    @ok="handleSubmit"
    width="40%"
    :minHeight="320"
  >
  <!--     :minHeight="120"
    :width="800" -->
    <!-- 多行文本框 -->

     <!-- <a-textarea
      v-model:value="uploadInfo"
      placeholder="存货编码 【跳格】 打印数量"
      style="width: 100%"
      :auto-size="{ minRows: 14, maxRows: 30 }"
    /> -->
    <BasicForm @register="registerForm"></BasicForm>
    <p style="color: red;line-height: 30px;margin-left: 20px;">
      {{ t('    提示：请复制EXCEL文件至输入框中。存货编码 【@@】 打印数量。 如：0101000336@@10 结束后回车') }}
    </p>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { printBySelInv } from '/@/api/wms/barcode/encode';
  import { replaceEnum } from '/@/enums/defEnum';
  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('bas.inv.basInv');

  const uploadInfo = ref('');
  let materialFlag = ref<any>('');

  const [registerModal, { setModalProps, closeModal }] = useModalInner((data:any) => {
    materialFlag.value = data.materialFlag;
  });

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('打印尺寸'),
      field: 'barSizeType',
      component: 'RadioGroup',
      defaultValue: '8040',
      componentProps: {
        dictType: 'bar_size_type4',
        maxlength: 50,
      },
      required: true,
    },
    {
      label: t(''),
      field: 'selIds',
      component: 'InputTextArea',
      // componentProps: {
      //   maxlength: 500,
      //   rows: 14,
      // },
      componentProps: ({ tableAction, formModel }) => {
        return {
          maxlength: 500,
          rows: 14,
          onblur: (e) => {
            let value = e.target.value;
            let newValue = value.replace(/\t/g, replaceEnum.replace);
            formModel['selIds'] = newValue;
          }
        }
      },
      required: true,
    }
  ];
  const [registerForm, { validate }] =
    useForm({
      labelWidth: 120,
      schemas: inputFormSchemas,
      baseColProps: { lg: 24, md: 24 },
    });

  async function handleSubmit() {
    // 把得到的值传递给父组件

    try {
      const data = await validate();
      const res = await printBySelInv(data);
      if(res.result == 'true'){
        emit('success', res);
        closeModal();
      }
    }catch(error){
      
    }

  }
</script>
