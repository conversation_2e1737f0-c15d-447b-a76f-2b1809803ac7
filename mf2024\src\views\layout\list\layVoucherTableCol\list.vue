<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button
          type="primary"
          @click="
            handleForm({
              viewCode: props.colpop.code,
              vouchCode: props.colpop.vouchCode,
              'voucherCls.typeName': props.colpop.vouchName,
            })
          "
          v-auth="'layout:edit'"
        >
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ viewCode: record.viewCode })">
          {{ record.viewCode }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherTableColList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import {
    layVoucherTableColDelete,
    layVoucherTableColListData,
  } from '../../../../api/layout/list/layVoucherTableCol';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('layout.layVoucherTableCol');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: t('表格配置管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('布局标志'),
        field: 'viewCode',
        component: 'Input',
      },
      {
        label: t('基础单据'),
        field: 'layVoucher.name',
        component: 'Input',
      },
      {
        label: t('列名'),
        field: 'title',
        component: 'Input',
      },
      {
        label: t('字段名'),
        field: 'dataIndex',
        component: 'Input',
      },
      {
        label: t('排序字段'),
        field: 'dbKey',
        component: 'Input',
      },
      {
        label: t('是否可排序'),
        field: 'sorter',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('列宽'),
        field: 'width',
        component: 'Input',
      },
      {
        label: t('对齐方式'),
        field: 'align',
        component: 'Input',
      },
      {
        label: t('插槽'),
        field: 'slot',
        component: 'Input',
      },
      {
        label: t('字典'),
        field: 'dictType',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('顺序号'),
      dataIndex: 'sortNum',
      key: 'a.sort_num',
      sorter: true,
      width: 80,
      align: 'center',
    },
    {
      title: t('布局标志'),
      dataIndex: 'viewCode',
      key: 'a.view_code',
      sorter: true,
      width: 130,
      align: 'left',
      slot: 'firstColumn',
      ifShow:false,
    },
    {
      title: t('基础单据'),
      dataIndex: 'layVoucher.name',
      key: 'a.vouch_code',
      sorter: true,
      width: 230,
      align: 'left',
      ifShow:false,
    },
    {
      title: t('列名'),
      dataIndex: 'title',
      key: 'a.title',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('字段名'),
      dataIndex: 'dataIndex',
      key: 'a.data_index',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('排序字段'),
      dataIndex: 'dbKey',
      key: 'a.db_key',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('是否可排序'),
      dataIndex: 'sorter',
      key: 'a.sorter',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否隐藏'),
      dataIndex: 'defaultHidden',
      key: 'a.default_hidden',
      // sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('列宽'),
      dataIndex: 'width',
      key: 'a.width',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('固定定位'),
      dataIndex: 'fixed',
      key: 'a.fixed',
      // sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('对齐方式'),
      dataIndex: 'align',
      key: 'a.align',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('插槽'),
      dataIndex: 'slot',
      key: 'a.slot',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('字典'),
      dataIndex: 'dictType',
      key: 'a.dict_type',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑表格配置'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'layout:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除表格配置'),
        popConfirm: {
          title: t('是否确认删除表格配置'),
          confirm: handleDelete.bind(this, { id: record.id }),
        },
        auth: 'layout:edit',
      },
    ],
  };
  const props = defineProps({
    colpop: { type: Object, default: {} },
  });
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm }] = useTable({
    api: layVoucherTableColListData,
    beforeFetch: (params) => {
      params.viewCode = props.colpop.code;
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: false,
    canResize: true,
    pagination: true,
    showIndexColumn: false,
    maxHeight: 500,
  });
  watch(
    () => props.colpop,
    () => {
      reload();
    },
    // { immediate: true },
  );

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const res = await layVoucherTableColDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleSuccess() {
    reload();
  }
</script>
