/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface VoucherFields extends BasicModel<VoucherFields> {
  vouchCode?: string; // 单据标志
  primaryKey?: string; // 是否主键
  notNull?: string; // 是否必填
  tabUinque?: string; // 是否唯一
  physicalName?: string; // 物理名称
  logicalName?: string; // 逻辑名称
  fieldType?: string; // 类型
  fieldLength?: number; // 字段长度
  fieldDecimal?: number; // 小数位数
  description?: string; // 描述
  defaultValue?: string; // 默认值
}

export const voucherFieldsList = (params?: VoucherFields | any) =>
  defHttp.get<VoucherFields>({ url: adminPath + '/layout/voucherFields/list', params });

export const voucherFieldsListData = (params?: VoucherFields | any) =>
  defHttp.post<Page<VoucherFields>>({ url: adminPath + '/layout/voucherFields/listData', params });

export const voucherFieldsForm = (params?: VoucherFields | any) =>
  defHttp.get<VoucherFields>({ url: adminPath + '/layout/voucherFields/form', params });

export const voucherFieldsSave = (params?: any, data?: VoucherFields | any) =>
  defHttp.postJson<VoucherFields>({ url: adminPath + '/layout/voucherFields/save', params, data });

export const voucherFieldsDelete = (params?: VoucherFields | any) =>
  defHttp.get<VoucherFields>({ url: adminPath + '/layout/voucherFields/delete', params });
