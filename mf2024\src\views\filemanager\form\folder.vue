<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicModal
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'filemanager:filemanagerFolder:edit'"
    @register="registerModal"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import {
    FilemanagerFolder,
    filemanagerFolderSave,
    filemanagerFolderForm,
    filemanagerFolderTreeData,
  } from '/@/api/filemanager/filemanagerFolder';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('filemanager.filemanagerFolder');
  const { showMessage } = useMessage();
  const record = ref<FilemanagerFolder>({} as FilemanagerFolder);
  const getTitle = computed(() => ({
    icon: 'i-fa-folder-o',
    value: record.value.isNewRecord ? t('新增文件夹') : t('编辑文件夹'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('上级文件夹'),
      field: 'parentCode',
      fieldLabel: 'parentName',
      component: 'TreeSelect',
      componentProps: {
        allowClear: true,
        style: 'width: calc(50% - 60px)',
      },
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('文件夹名'),
      field: 'folderName',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      required: true,
    },
    {
      label: t('排序号'),
      field: 'treeSort',
      helpMessage: '升序',
      component: 'InputNumber',
      defaultValue: '30',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ required: true }, { pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('备注信息'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 500,
      },
      colProps: { lg: 24, md: 24 },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ loading: true });
    await resetFields();
    const res = await filemanagerFolderForm(data);
    record.value = (res.filemanagerFolder || {}) as FilemanagerFolder;
    record.value.__t = new Date().getTime();
    if (data.parentCode && data.parentName) {
      record.value.parentCode = data.parentCode;
      record.value.parentName = data.parentName;
    }
    if (data.groupType) {
      record.value.groupType = data.groupType;
    }
    setFieldsValue(record.value);
    updateSchema([
      {
        field: 'parentCode',
        componentProps: {
          api: filemanagerFolderTreeData,
          params: {
            excludeCode: record.value.id,
            isShowRawName: true,
          },
        },
      },
      {
        field: 'id',
        componentProps: {
          disabled: !record.value.isNewRecord,
        },
      },
    ]);
    setModalProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setModalProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      data.groupType = record.value.groupType;
      // console.log('submit', params, data, record);
      const res = await filemanagerFolderSave(params, data);
      showMessage(res.message);
      closeModal();
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
