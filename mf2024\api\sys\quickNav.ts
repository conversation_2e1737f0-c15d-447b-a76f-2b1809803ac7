/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface QuickNav extends BasicModel<QuickNav> {
  userCode?: string; // 用户编码
  menuId?: string; // 菜单ID
  sorted?: number; // 顺序号
}

export const quickNavList = (params?: QuickNav | any) =>
  defHttp.get<QuickNav>({ url: adminPath + '/sys/quickNav/list', params });

export const quickNavListData = (params?: QuickNav | any) =>
  defHttp.post<Page<QuickNav>>({ url: adminPath + '/sys/quickNav/listData', params });

export const quickNavForm = (params?: QuickNav | any) =>
  defHttp.get<QuickNav>({ url: adminPath + '/sys/quickNav/form', params });

export const quickNavSave = (params?: any, data?: QuickNav | any) =>
  defHttp.postJson<QuickNav>({ url: adminPath + '/sys/quickNav/save', params, data });

export const quickNavDelete = (params?: QuickNav | any) =>
  defHttp.get<QuickNav>({ url: adminPath + '/sys/quickNav/delete', params });

export const findUserNav = (params?: QuickNav | any) =>
  defHttp.get<QuickNav>({ url: adminPath + '/sys/quickNav/findUserNav', params });

export const changeSorted = (params?: QuickNav | any) =>
  defHttp.postJson<QuickNav>({ url: adminPath + '/sys/quickNav/changeSorted', params });
