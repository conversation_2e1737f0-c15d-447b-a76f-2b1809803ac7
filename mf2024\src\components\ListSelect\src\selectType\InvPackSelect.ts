import { useI18n } from '/@/hooks/web/useI18n';
import { BasicColumn, BasicTableProps, FormProps } from '/@/components/Table';
import { basInvPackListData } from '/@/api/bas/inv/pack/basInvPack';

const { t } = useI18n('sys.empUser');

const modalProps = {
  title: t('成品批号选择'),
};

const searchForm: FormProps = {
  baseColProps: { lg: 6, md: 8 },
  labelWidth: 100,
  schemas: [
    {
      label: t('批号'),
      field: 'addCode',
      component: 'Input',
    },
    {
      label: t('存货名称'),
      field: 'basInv.invName',
      component: 'Input',
    },
    {
      label: t('规格型号'),
      field: 'basInv.invStd',
      component: 'Input',
    },
    {
      label: t('存货编码'),
      field: 'basInv.invCode',
      component: 'Input',
    },
  ],
};

const tableColumns: BasicColumn[] = [
  {
    title: t('批号'),
    dataIndex: 'addCode',
    width: 80,
  },
  {
    title: t('规格说明'),
    dataIndex: 'packName',
    width: 150,
    align: 'left',
  },
  {
    title: t('存货名称'),
    dataIndex: 'basInv.invName',
    width: 150,
    align: 'left',
  },
  {
    title: t('规格型号'),
    dataIndex: 'basInv.invStd',
    width: 120,
    align: 'left',
  },
  {
    title: t('托盘容量'),
    dataIndex: 'packSize',
    width: 80,
  },
  {
    title: t('存货编码'),
    dataIndex: 'basInv.invCode',
    width: 120,
    align: 'left',
  },
  {
    title: t('单位'),
    dataIndex: 'basInv.unitName',
    width: 80,
  },
];

const tableProps: BasicTableProps = {
  api: basInvPackListData,
  beforeFetch: (params) => {
    params['isAll'] = true;
    console.log('params', params);
    return params;
  },
  columns: tableColumns,
  formConfig: searchForm,
  rowKey: 'addCode',
};

export default {
  modalProps,
  tableProps,
  itemCode: 'addCode',
  itemName: 'addCode',
  isShowCode: true,
};
