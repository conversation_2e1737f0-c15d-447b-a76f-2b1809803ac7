<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'bankdirectlink:payapply:billmanagerPayApplyC:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsBankdirectlinkPayapplyBillmanagerPayApplyCForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BillmanagerPayApplyC, billmanagerPayApplyCSave, billmanagerPayApplyCForm } from '/@/api/billmanager/bankdirectlink/payapply/billmanagerPayApplyC';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('bankdirectlink.payapply.billmanagerPayApplyC');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<BillmanagerPayApplyC>({} as BillmanagerPayApplyC);

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增付款申请单子表') : t('编辑付款申请单子表'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('主表ID'),
      field: 'parentId',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
    },
    {
      label: t('款项类型'),
      field: 'payType',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
      required: true,
    },
    {
      label: t('供应商'),
      field: 'venCode',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('币种'),
      field: 'cexchName',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
    },
    {
      label: t('原币金额'),
      field: 'orgAmount',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ required: true }, { pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('部门'),
      field: 'deptCode',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('备注'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 4000,
      },
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('来源id'),
      field: 'sourceId',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('合同号'),
      field: 'contractId',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('资金用途'),
      field: 'useOfFunds',
      component: 'Input',
      componentProps: {
        maxlength: 50,
      },
      required: true,
    },
    {
      label: t('U8生单状态'),
      field: 'u8Status',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await billmanagerPayApplyCForm(data);
    record.value = (res.billmanagerPayApplyC || {}) as BillmanagerPayApplyC;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await billmanagerPayApplyCSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
