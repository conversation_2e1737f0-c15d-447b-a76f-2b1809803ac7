/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page, TreeDataModel } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface encode extends BasicModel<encode> {
  selIds?: string; // 选中记录ID
  prtQtys?: string; // 打印份数
  barType?: string; // 标签来源
}

export const printSnNumber = (params?: encode | any) =>
  defHttp.post<encode>({ url: adminPath + '/barcode/encode/printSnNumber', params });

export const printBySelInv = (params?: encode | any) =>
  defHttp.post<encode>({ url: adminPath + '/barcode/encode/printBySelInv', params });

// /barcode/encode/printBySelTuoPan
export const printBySelTuoPan = (params?: encode | any) =>
  defHttp.post<encode>({ url: adminPath + '/barcode/encode/printBySelTuoPan', params });
