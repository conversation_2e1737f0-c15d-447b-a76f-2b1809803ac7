<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'bas:inv:pack:basInvPack:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="40%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsBasInvBasInvPackForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasInvPack, basInvPackSave, basInvPackForm } from '/@/api/bas/inv/pack/basInvPack';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('bas.inv.pack.basInvPack');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<BasInvPack>({} as BasInvPack);

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增包装规格') : t('编辑包装规格'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('存货编码'),
      field: 'invCode',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      required: true,
      dynamicDisabled: true,
    },
    {
      label: t('存货名称'),
      field: 'basInv.invName',
      component: 'Input',
      componentProps: {
        maxlength: -1,
      },
      dynamicDisabled: true,
    },
    {
      label: t('规格型号'),
      field: 'basInv.invStd',
      component: 'Input',
      componentProps: {
        maxlength: -1,
      },
      dynamicDisabled: true,
    },
    {
      label: t('单位'),
      field: 'basInv.unitName',
      component: 'Input',
      componentProps: {
        maxlength: -1,
      },
      dynamicDisabled: true,
    },
    {
      label: t('批号'),
      field: 'addCode',
      component: 'Input',
      componentProps: {
        maxlength: 4,
      },
      required: true,
    },
    {
      label: t('规格说明'),
      field: 'packName',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      required: true,
    },
    {
      label: t('托盘容量'),
      field: 'packSize',
      component: 'InputNumber',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ required: true }, { pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('单件容量'),
      field: 'pieceQty',
      component: 'InputNumber',
      componentProps: {
        maxlength: 16,
      },
      rules: [
        { pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') },
      ],
    },
    {
      label: t('默认部门'),
      field: 'depCode',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
    },
    {
      label: t('默认仓库'),
      field: 'whCode',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
    },
    {
      label: t('附加打印数'),
      field: 'plusPrintQty',
      component: 'InputNumber',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t(''),
      field: 'defBatch',
      component: 'CheckboxGroup',
      componentProps: {
        options: [{ label: '是否通用批次规则', value: '1' }],
      },
    },
    {
      label: t(''),
      field: 'bneedTuoPan',
      component: 'CheckboxGroup',
      componentProps: {
        options: [{ label: '是否托盘追溯', value: '1' }],
      },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate, updateSchema }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 24, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await basInvPackForm(data);
    record.value = (res.basInvPack || {}) as BasInvPack;
    record.value.__t = new Date().getTime();
    if (data.record) {
      record.value.invCode = data.record.invCode;
      record.value.basInv = {
        invName: data.record.invName,
        invStd: data.record.invStd,
        unitName: data.record.unitName,
      };
    }
    setFieldsValue(record.value);
    updateSchema([
      {
        field: 'addCode',
        componentProps: {
          disabled: !record.value.isNewRecord,
        },
      },
    ]);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord
      };
      // console.log('submit', params, data, record);
      const res = await basInvPackSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
