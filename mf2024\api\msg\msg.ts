/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface Msg extends BasicModel<Msg> {
  msgType?: string; // 消息标题
  msgTitle?: string; // 消息标题
  msgContentEntity?: any; // 消息内容
  sendUserCode?: string; // 发送者用户编码
  sendUserName?: string; // 发送者用户姓名
  sendDate?: string; // 发送时间
}

export const msgList = (params?: Msg | any) =>
  defHttp.get<Msg>({ url: adminPath + '/msg/list', params });

export const msgListData = (params?: Msg | any) =>
  defHttp.post<Page<Msg>>({ url: adminPath + '/msg/listData', params });

export const msgReadMsg = (params?: Msg | any) =>
  defHttp.get<Msg>({ url: adminPath + '/msg/readMsg', params });

export const msgReadAllMsg = (params?: Msg | any) =>
  defHttp.get<Msg>({ url: adminPath + '/msg/readAllMsg', params });

export const msgUnreadMsg = (params?: any, data?: Msg | any) =>
  defHttp.postJson<Msg>(
    { url: adminPath + '/msg/unreadMsg?__notUpdateSession=true', params, data },
    { errorMessageMode: 'none' },
  );

export const msgPullPoolMsg = (params?: Msg | any) =>
  defHttp.get<Msg>(
    { url: adminPath + '/msg/pullPoolMsg?__notUpdateSession=true', params },
    { errorMessageMode: 'none' },
  );
