/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page, TreeDataModel } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface BasInv extends BasicModel<BasInv> {
  cinvcode?: string; // 存货编码
  cinvname?: string; // 存货名称
  cinvstd?: string; // 规格型号
  cinvaddcode?: string; // 存货代码
  clsCode?: string; // 产品分类编码
  unitName?: string; // 单位
  itaxrate?: number; // 税率
  iinvsprice?: number; // 参考成本
  iinvscost?: number; // 参考售价
  iinvlscost?: number; // 最低售价
  iinvncost?: number; // 最新成本
  iinvsalecost?: number; // 零售单价
  fretailprice?: number; // 零售价格
}

export const basInvList = (params?: BasInv | any) =>
  defHttp.get<BasInv>({ url: adminPath + '/bas/inv/list', params });

export const basInvListData = (params?: BasInv | any) =>
  defHttp.post<Page<BasInv>>({ url: adminPath + '/bas/inv/listData', params });

export const basInvForm = (params?: BasInv | any) =>
  defHttp.get<BasInv>({ url: adminPath + '/bas/inv/form', params });

export const basInvSave = (params?: any, data?: BasInv | any) =>
  defHttp.postJson<BasInv>({ url: adminPath + '/bas/inv/save', params, data });

export const basInvDelete = (params?: BasInv | any) =>
  defHttp.get<BasInv>({ url: adminPath + '/bas/inv/delete', params });

export const basInvTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/bas/inv/cls/treeData', params });

export const updateFbStatus = (params?: any, data?: BasInv | any) =>
  defHttp.postJson<BasInv>({ url: adminPath + '/shop/shopInv/updateFbStatus', params, data });

export const savePic = (params?: any, data?: BasInv | any) =>
  defHttp.postJson<BasInv>({ url: adminPath + '/shop/shopInv/savePic', params, data });

export const cusInvListData = (params?: BasInv | any) =>
  defHttp.post<Page<BasInv>>({ url: adminPath + '/shop/shopInv/cusInvListData', params });

export const cusInvListDataByCusCode = (params?: BasInv | any) =>
  defHttp.post<Page<BasInv>>({ url: adminPath + '/shop/shopInv/cusInvListDataByCusCode', params });

export const uploadVideo = (params?: any, data?: BasInv | any) =>
  defHttp.postJson<BasInv>({ url: adminPath + '/bas/inv/uploadVideo', params, data });

export const uploadPdf = (params?: any, data?: BasInv | any) =>
  defHttp.postJson<BasInv>({ url: adminPath + '/bas/inv/uploadPdf', params, data });
