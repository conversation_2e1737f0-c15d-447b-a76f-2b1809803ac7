/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface LayVoucherTabRightOpe extends BasicModel<LayVoucherTabRightOpe> {
  viewCode?: string; // 布局标志
  vouchCode?: string; // 基础单据
  iocn?: string; // 按钮图标
  title?: string; // 按钮标题
  color?: string; // 按钮颜色
  auth?: string; // 权限标识
  ifShow?: string; // 是否显示
  params?: string; // 行数据
}

export const layVoucherTabRightOpeList = (params?: LayVoucherTabRightOpe | any) =>
  defHttp.get<LayVoucherTabRightOpe>({ url: adminPath + '/layout/listRowBtn/list', params });

export const layVoucherTabRightOpeListData = (params?: LayVoucherTabRightOpe | any) =>
  defHttp.post<Page<LayVoucherTabRightOpe>>({ url: adminPath + '/layout/listRowBtn/listData', params });

export const layVoucherTabRightOpeForm = (params?: LayVoucherTabRightOpe | any) =>
  defHttp.get<LayVoucherTabRightOpe>({ url: adminPath + '/layout/listRowBtn/form', params });

export const layVoucherTabRightOpeSave = (params?: any, data?: LayVoucherTabRightOpe | any) =>
  defHttp.postJson<LayVoucherTabRightOpe>({ url: adminPath + '/layout/listRowBtn/save', params, data });

export const layVoucherTabRightOpeDelete = (params?: LayVoucherTabRightOpe | any) =>
  defHttp.get<LayVoucherTabRightOpe>({ url: adminPath + '/layout/listRowBtn/delete', params });
