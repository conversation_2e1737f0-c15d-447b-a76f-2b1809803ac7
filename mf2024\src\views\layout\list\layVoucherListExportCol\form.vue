<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'layout:export:layVoucherListExportCol:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutExportLayVoucherListExportColForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import {
    LayVoucherListExportCol,
    layVoucherListExportColSave,
    layVoucherListExportColForm,
  } from '/@/api/layout/export/layVoucherListExportCol';

  import { dictTypeTreeData } from '/@/api/sys/dictType';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('layout.export.layVoucherListExportCol');
  const { showMessage } = useMessage();
  const record = ref<LayVoucherListExportCol>({} as LayVoucherListExportCol);
  const getTitle = computed(() => ({
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增导入/出配置') : t('编辑导入/出配置'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('共用配置'),
      field: 'detailInfo',
      component: 'FormGroup',
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('布局标志'),
      field: 'code',
      component: 'Input',
      dynamicDisabled: true,
      componentProps: {
        maxlength: 200,
      },
      required: true,
    },
    {
      label: t('字段标题'),
      field: 'exTitle',
      component: 'Input',
      helpMessage: '需要添加批注请用“**”分隔，标题**批注，仅对导出模板有效',
      componentProps: {
        maxlength: 100,
      },
      required: true,
    },
    {
      label: t('字段名'),
      field: 'exAttrName',
      component: 'Input',
      helpMessage:
        '默认调用当前字段的“get”方法，如指定导出字段为对象，请填写“对象名.对象属性”，例：“area.name”、“office.name”',
      componentProps: {
        maxlength: 100,
      },
      required: true,
    },
    {
      label: t('作用类型'),
      field: 'exType',
      component: 'Select',
      defaultValue: '0',
      componentProps: {
        dictType: 'lay_ex_type',
        allowClear: true,
      },
    },
    {
      label: t('字典类型'),
      field: 'exDictType',
      fieldLabel: 'name',
      component: 'TreeSelect',
      helpMessage: '请设置字典的type值',
      componentProps: {
        maxlength: 100,
        api: dictTypeTreeData,
        canSelectParent: false,
        allowClear: true,
      },
    },
    {
      label: t('反射类型'),
      field: 'exFieldType',
      component: 'Input',
      helpMessage: '如：MoneyType.class 金额类型转换（保留两位）',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('数值格式'),
      field: 'exDataFormat',
      component: 'Input',
      helpMessage: '例如：数值：0.00；日期：yyyy-MM-dd；金额：￥#,##0.00',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('字段分组'),
      field: 'groups',
      component: 'Input',
      helpMessage: '针对每一种业务的导入、导出（imp、exp）',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('导出配置'),
      field: 'detailInfo',
      component: 'FormGroup',
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('字段排序'),
      field: 'exSort',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ required: true }, { pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('对齐方式'),
      field: 'exAlign',
      component: 'Select',
      componentProps: {
        dictType: 'lay_ex_align',
        allowClear: true,
      },
      required: true,
    },
    {
      label: t('导出列宽'),
      field: 'exWidth',
      component: 'Input',
      helpMessage:
        '以字符宽度的1/256为单位，假如你想显示5个字符的话，就可以设置5*256，1个汉字占2个字符',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ required: true }, { pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('字符个数'),
      field: 'exWords',
      component: 'Input',
      helpMessage: 'words*256=width，1个汉字占2个字符',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('导入配置'),
      field: 'detailInfo',
      component: 'FormGroup',
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('字段索引'),
      field: 'exColumn',
      component: 'Input',
      helpMessage: '导入时指定列索引（从0开始）在指定读取excel中的指定的列时使用',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await layVoucherListExportColForm(data);
    record.value = (res.layVoucherListExportCol || {}) as LayVoucherListExportCol;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await layVoucherListExportColSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
