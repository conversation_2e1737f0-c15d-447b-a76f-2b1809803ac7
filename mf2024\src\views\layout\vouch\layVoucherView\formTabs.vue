<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer v-bind="$attrs" :showFooter="false" @register="registerDrawer" width="80%">
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
      <span> （{{ record.code + '-' + record.name + '-' + record.typeName }}） </span>
    </template>
    <Tabs v-model:activeKey="activeKey" tabPosition="left">
      <Tabs.TabPane key="1" :forceRender="true" tab="全局设置">
        <FormConfig :colpop="colpop" />
      </Tabs.TabPane>

      <Tabs.TabPane key="2" :forceRender="true" tab="表单页签">
        <FormTabConfig :colpop="colpop" />
      </Tabs.TabPane>

      <Tabs.TabPane key="3" :forceRender="true" tab="表单栏目">
        <FormCol :colpop="colpop" />
      </Tabs.TabPane>

      <Tabs.TabPane key="4" :forceRender="true" tab="表单按钮">
        <FormBtn :colpop="colpop" />
      </Tabs.TabPane>
    </Tabs>
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsTestTestDataForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { Tabs } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  // import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';

  import FormConfig from '../../form/layVoucherFormConfig/configForm.vue';
  import FormCol from '../../form/layVoucherFormListCol/list.vue';
  import FormTabConfig from '../../form/layVoucherFormTabConfig/list.vue';
  import FormBtn from '../../form/layVoucherFormBtn/list.vue';

  // const emit = defineEmits(['success', 'register']);
  let colpop = ref({});
  const { t } = useI18n('test.testData');
  // const { showMessage } = useMessage();
  const getTitle = computed(() => ({
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: t('表单设计'),
  }));
  let record = ref({
    code: '',
    name: '',
    typeName: '',
    typeCode: '',
  });

  const activeKey = ref<string>('1');
  const [registerDrawer, { setDrawerProps }] = useDrawerInner(async (data) => {
    activeKey.value = '1';
    colpop.value = data;
    setDrawerProps({ loading: true });
    record.value = data;
    // await resetFields();
    // const res = await testDataForm(data);
    // record.value = (res.testData || {}) as TestData;
    // record.value.__t = new Date().getTime();
    // setFieldsValue(record.value);
    // setTestDataChildTableData(res);
    setDrawerProps({ loading: false });
  });

  // async function handleSubmit() {
  //   try {
  //     const data = await validate();
  //     setDrawerProps({ confirmLoading: true });
  //     const params: any = {
  //       isNewRecord: record.value.isNewRecord,
  //       id: record.value.id,
  //     };
  //     data.testDataChildList = await getTestDataChildList();
  //     // console.log('submit', params, data, record);
  //     const res = await testDataSave(params, data);
  //     showMessage(res.message);
  //     setTimeout(closeDrawer);
  //     emit('success', data);
  //   } catch (error: any) {
  //     if (error && error.errorFields) {
  //       showMessage(t('您填写的信息有误，请根据提示修正。'));
  //     }
  //     console.log('error', error);
  //   } finally {
  //     setDrawerProps({ confirmLoading: false });
  //   }
  // }
</script>
