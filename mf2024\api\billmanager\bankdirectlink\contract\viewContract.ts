/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '/@/api/model/baseModel';

const { adminPath } = useGlobSetting();

export interface ViewContract extends BasicModel<ViewContract> {
  htcode?: string; // 合同编码
  htname?: string; // 合同名称
  vencode?: string; // 供应商编码
  venname?: string; // 供应商名称
  amount?: number; // 预算金额
  useAmount?: number; // 已使用金额
  syUseMount?: number; // 使用使用金额
}

export const viewContractList = (params?: ViewContract | any) =>
  defHttp.get<ViewContract>({ url: adminPath + '/bankdirectlink/contract/viewContract/list', params });

export const viewContractListData = (params?: ViewContract | any) =>
  defHttp.post<Page<ViewContract>>({ url: adminPath + '/bankdirectlink/contract/viewContract/listData', params });

export const viewContractForm = (params?: ViewContract | any) =>
  defHttp.get<ViewContract>({ url: adminPath + '/bankdirectlink/contract/viewContract/form', params });

export const viewContractSave = (params?: any, data?: ViewContract | any) =>
  defHttp.postJson<ViewContract>({ url: adminPath + '/bankdirectlink/contract/viewContract/save', params, data });

export const viewContractDelete = (params?: ViewContract | any) =>
  defHttp.get<ViewContract>({ url: adminPath + '/bankdirectlink/contract/viewContract/delete', params });
