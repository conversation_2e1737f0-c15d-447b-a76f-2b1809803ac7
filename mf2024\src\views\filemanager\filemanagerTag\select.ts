import { useI18n } from '/@/hooks/web/useI18n';
import { BasicColumn, BasicTableProps, FormProps } from '/@/components/Table';
import { filemanagerTagListData } from '/@/api/filemanager/filemanagerTag';

const { t } = useI18n('sys.filemanagerTag');

const modalProps = {
  title: t('标签选择'),
};

const searchForm: FormProps = {
  baseColProps: { lg: 6, md: 8 },
  labelWidth: 90,
  schemas: [
    {
      label: t('标签名称'),
      field: 'tagName',
      component: 'Input',
    },
    {
      label: t('系统内置'),
      field: 'isSys',
      component: 'Select',
      componentProps: {
        dictType: 'sys_yes_no',
        allowClear: true,
      },
    },
    {
      label: t('状态'),
      field: 'status',
      component: 'Select',
      componentProps: {
        dictType: 'sys_search_status',
        allowClear: true,
      },
    },
    {
      label: t('备注信息'),
      field: 'remarks',
      component: 'Input',
    },
  ],
};

const tableColumns: BasicColumn[] = [
  {
    title: t('标签名称'),
    dataIndex: 'tagName',
    key: 'a.tag_name',
    sorter: true,
    width: 200,
    align: 'center',
    slot: 'firstColumn',
  },
  {
    title: t('系统内置'),
    dataIndex: 'isSys',
    key: 'a.is_sys',
    sorter: true,
    width: 130,
    align: 'center',
    dictType: 'sys_yes_no',
  },
  {
    title: t('状态'),
    dataIndex: 'status',
    key: 'a.status',
    sorter: true,
    width: 130,
    align: 'center',
    dictType: 'sys_search_status',
  },
  {
    title: t('更新时间'),
    dataIndex: 'updateDate',
    key: 'a.update_date',
    sorter: true,
    width: 130,
    align: 'center',
  },
  {
    title: t('备注信息'),
    dataIndex: 'remarks',
    key: 'a.remarks',
    sorter: true,
    width: 130,
    align: 'left',
  },
];

const tableProps: BasicTableProps = {
  api: filemanagerTagListData,
  beforeFetch: (params) => {
    params['isAll'] = true;
    return params;
  },
  columns: tableColumns,
  formConfig: searchForm,
  rowKey: 'tagId',
};

export default {
  modalProps,
  tableProps,
  itemCode: 'tagId',
  itemName: 'tagName',
  isShowCode: false,
};
