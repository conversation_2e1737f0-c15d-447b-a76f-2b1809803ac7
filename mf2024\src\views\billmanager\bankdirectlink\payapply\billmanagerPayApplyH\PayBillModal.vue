<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicModal
    v-bind="$attrs"
    :showFooter="true"
    @register="registerBillModal"
    @ok="handleBillSubmit"
    width="80%"
    title="票据选择"
  >
    <BasicForm @register="registerBillForm">
      <template #billDetailList>
        <div class="mb-2 p-3 bg-gray-50 rounded border">
          <div class="text-sm text-gray-600 mb-2">
            票据总金额: <span class="font-semibold text-blue-600">{{ totalAmount.toFixed(2) }}</span> |
            已分配: <span class="font-semibold text-green-600">{{ allocatedAmount.toFixed(2) }}</span> |
            剩余: <span class="font-semibold" :class="remainingAmount >= 0 ? 'text-orange-600' : 'text-red-600'">
              {{ remainingAmount.toFixed(2) }}
            </span>
          </div>
          <div class="text-xs" :class="Math.abs(remainingAmount) < 0.01 ? 'text-green-600' : 'text-red-600'">
            <Icon :icon="Math.abs(remainingAmount) < 0.01 ? 'i-ant-design:check-circle-outlined' : 'i-ant-design:exclamation-circle-outlined'" />
            {{ Math.abs(remainingAmount) < 0.01 ? '✓ 票据金额分配完成，可以提交' : '⚠ 票据详情总金额必须等于票据总金额才能提交' }}
          </div>
        </div>
        <BasicTable
          @register="registerBillDetailTable"
          @row-click="handleBillDetailRowClick"
          @edit-change="handleEditChange"
          @edit-end="handleEditEnd"
        />
        <a-button
          class="mt-2"
          @click="handleBillDetailAdd"
          type="primary"
          :disabled="Math.abs(remainingAmount) < 0.01"
        >
          <Icon icon="i-ant-design:plus-circle-outlined" /> {{ t('选择汇票') }}
        </a-button>
      </template>
    </BasicForm>
    <ListSelect
      ref="listSelectRef"
      selectType="appNote"
      :checkbox="false"
      @select="handleSelect"
      :selectList="selectListRef"
      :queryParams="queryParams"
      v-show="false"
    />
  </BasicModal>
</template>

<script lang="ts" setup name="PayBillModal">
  import { ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { officeTreeData } from '/@/api/sys/office';
  import { OfficeTypeEnum } from '/@/enums/defEnum';
  import { ListSelect } from '/@/components/ListSelect';
  import { billManagerPayApplyC1BillSelection } from '/@/api/billmanager/bankdirectlink/payapply/billManagerPayApplyC1';
import { c } from 'node_modules/vite/dist/node/types.d-aGj9QkWt';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('bankdirectlink.payapply.billmanagerPayApplyH');
  const { showMessage } = useMessage();

  // 当前记录
  const currentRecord = ref<Recordable>({});

  // 总金额和已分配金额
  const totalAmount = ref<number>(0);
  const allocatedAmount = ref<number>(0);

  // listSelectRef
  const selectListRef = ref<any>([]);
  let queryParams = ref<any>({});
  const listSelectRef = ref<any>(null);

  // 计算剩余可分配金额
  const remainingAmount = computed(() => {
    return totalAmount.value - allocatedAmount.value;
  });

  // 票据选择弹窗表单配置
  const billFormSchemas: FormSchema[] = [
    {
      label: t('供应商'),
      field: 'supplier',
      component: 'TreeSelect',
      componentProps: {
        api: officeTreeData,
        params: { isLoadUser: true, userIdPrefix: '', isAll: true, officeTypes: OfficeTypeEnum.WL },
        canSelectParent: false,
        allowClear: true,
        maxlength: 64,
      },
      required: true,
      colProps: { lg: 12, md: 24 },
      dynamicDisabled: true,
    },
    {
      label: t('总金额'),
      field: 'totalAmount',
      component: 'Input',
      componentProps: {
        maxlength: 16,
        placeholder: '请输入总金额',
        type: 'number',
        min: 0,
        step: 0.01,
      },
      required: true,
      colProps: { lg: 12, md: 24 },
      dynamicDisabled: true,
    },
    {
      label: t('票据详情'),
      field: 'billDetailList',
      component: 'Input',
      colProps: { lg: 24, md: 24 },
      slot: 'billDetailList',
    },
  ];

  const [registerBillForm, {
    resetFields: resetBillFields,
    setFieldsValue: setBillFieldsValue,
    validate: validateBillForm,
  }] = useForm({
    labelWidth: 120,
    schemas: billFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  // 票据详情表格配置
  const [registerBillDetailTable, billDetailTable] = useTable({
    actionColumn: {
      width: 80,
      actions: (_record: Recordable) => [
        {
          icon: 'i-ant-design:delete-outlined',
          color: 'error',
          popConfirm: {
            title: '是否确认删除',
            confirm: handleBillDetailDelete.bind(this, _record),
          },
        },
      ],
    },
    rowKey: 'id',
    pagination: false,
    bordered: true,
    size: 'small',
    inset: true,
  });

  const [registerBillModal, { closeModal: closeBillModal }] = useModalInner(async (data: any) => {
    currentRecord.value = data;
    console.log('data', data);
    // 设置总金额
    totalAmount.value = parseFloat(data.orgAmount || '0');

    // 设置弹窗表单初始值
    await resetBillFields();
    setBillFieldsValue({
      supplier: data.vendor.officeName || '',
      totalAmount: data.orgAmount || '',
    });

    // 设置票据详情表格数据
    setTimeout(() => {
      setBillDetailTableData();

      // 处理现有数据，标记为非新增记录
      const existingData = (data.billList || []).map((item: any) => ({
        ...item,
        isNewRecord: false, // 现有数据标记为非新增
        editable: true,
      }));

      console.log('初始化票据详情数据:', existingData);
      billDetailTable.setTableData(existingData);

      // 初始化时计算已分配金额
      calculateAllocatedAmount();
    }, 100);
  });

  // 安全获取表格数据
  function getTableDataSafely(): any[] {
    try {
      const tableData = billDetailTable.getDataSource();
      return Array.isArray(tableData) ? tableData : [];
    } catch (error) {
      console.warn('获取表格数据失败:', error);
      return [];
    }
  }

  // 计算已分配金额
  function calculateAllocatedAmount() {
    try {
      const tableData = getTableDataSafely();
      let total = 0;

      console.log('calculateAllocatedAmount - 表格数据:', tableData);

      tableData.forEach((record: any) => {
        if (record && typeof record === 'object') {
          // 只使用已保存的值，不使用编辑状态的值
          const amount = parseFloat(record.orgAmount || '0');
          console.log('计算行金额:', amount, '来源: orgAmount =', record.orgAmount, 'record:', record);

          if (!isNaN(amount) && amount >= 0) {
            total += amount;
          }
        }
      });

      allocatedAmount.value = total;
      console.log('calculateAllocatedAmount完成 - 总计:', total, '总金额:', totalAmount.value, '剩余:', totalAmount.value - total);
    } catch (error) {
      console.error('计算已分配金额失败:', error);
      allocatedAmount.value = 0;
    }
  }

  // 设置票据详情表格数据和列配置
  function setBillDetailTableData() {
    try {
      billDetailTable.setColumns([
        {
          title: t('票据号'),
          dataIndex: 'clink',
          width: 200,
          align: 'left',
          editRow: false,
          editComponent: 'Input',
          editComponentProps: {
            maxlength: 50,
            placeholder: '请输入票据号',
          },
          editRule: (text: any, _record: Recordable) => {
            return new Promise<void>((resolve, reject) => {
              if (!text || text === '') {
                return reject('请输入票据号');
              }
              resolve();
            });
          },
        },
        {
          title: t('承兑银行'),
          dataIndex: 'cbank',
          width: 200,
          align: 'left',
          editRow: false,
          editComponent: 'Input',
          editComponentProps: {
            maxlength: 50,
            placeholder: '请输入承兑银行',
          },
        },
        {
          title: t('到期日期'),
          dataIndex: 'dexpiredate',
          width: 200,
          align: 'left',
          editRow: false,
          editComponent: 'Input',
          editComponentProps: {
            maxlength: 50,
            placeholder: '请输入到期日期',
          },
        },
        {
          title: t('应答日期'),
          dataIndex: 'dreceiptdate',
          width: 200,
          align: 'left',
          editRow: false,
          editComponent: 'Input',
          editComponentProps: {
            maxlength: 50,
            placeholder: '请输入应答日期',
          },
        },
        {
          title: t('金额'),
          dataIndex: 'orgAmount',
          width: 150,
          align: 'left',
          editRow: true,
          editComponent: 'Input',
          editComponentProps: {
            maxlength: 16,
            required: true,
            type: 'number',
            min: 0,
            step: 0.01,
            placeholder: '请输入金额',
          },
          editRule: (text: any, record: Recordable) => {
            return new Promise<void>((resolve, reject) => {
              const value = parseFloat(text || '0');

              // 验证金额必须大于0
              if (!text || value <= 0) {
                return reject('金额必须大于0');
              }

              // 验证金额不能超过原始金额
              const originalAmount = parseFloat(record.originalAmount || '0');
              if (originalAmount > 0 && value > originalAmount) {
                return reject(`金额不能超过原始票据金额 ${originalAmount.toFixed(2)}`);
              }

              // 计算所有行的总金额（包括当前正在编辑的行）
              const tableData = getTableDataSafely();
              let totalAllocated = 0;
              console.log('editRule验证 - 表格数据:', tableData);

              tableData.forEach((r: any) => {
                if (r && typeof r === 'object') {
                  let amount = 0;

                  console.log('editRule - 比较ID:', 'r.id =', r.id, 'record.id =', record.id, '相等?', r.id === record.id);

                  if (r.id === record.id) {
                    // 当前编辑的行使用新输入的值
                    amount = value;
                    console.log('editRule - 当前编辑行:', amount, '输入值:', value, 'record:', r);
                  } else {
                    // 其他行：优先使用编辑状态的值，如果没有则使用已保存的值
                    if (r.editValueRefs && r.editValueRefs.orgAmount !== undefined && r.editValueRefs.orgAmount !== null) {
                      amount = parseFloat(r.editValueRefs.orgAmount || '0');
                      console.log('editRule - 其他行(编辑状态):', amount, '来源: editValueRefs.orgAmount =', r.editValueRefs.orgAmount);
                    } else {
                      amount = parseFloat(r.orgAmount || '0');
                      console.log('editRule - 其他行(已保存):', amount, '来源: orgAmount =', r.orgAmount);
                    }
                  }

                  if (!isNaN(amount) && amount >= 0) {
                    totalAllocated += amount;
                  }
                }
              });

              console.log('editRule验证 - 总计算金额:', totalAllocated);

              if (totalAllocated > totalAmount.value) {
                return reject(`所有金额总和(${totalAllocated.toFixed(2)})不能超过总金额(${totalAmount.value.toFixed(2)})`);
              }

              console.log('editRule验证通过:', value, '所有行总金额:', totalAllocated.toFixed(2));

              // 验证通过后，立即更新已分配金额显示（模拟编辑状态）
              allocatedAmount.value = totalAllocated;

              resolve();
            });
          },
        },
      ]);
      // 初始化空数据
      billDetailTable.setTableData([]);
    } catch (error) {
      console.error('设置票据详情表格数据失败:', error);
    }
  }

  // 处理表格编辑变化事件
  function handleEditChange(data: any) {
    console.log('handleEditChange - 表格编辑变化:', data);
    // 如果是金额列发生变化，重新计算已分配金额
    if (data.column && data.column.dataIndex === 'orgAmount') {
      console.log('handleEditChange - 金额列变化，当前值:', data.value, '记录ID:', data.record.id);

      // 立即更新已分配金额（模拟编辑中的状态）
      const tableData = getTableDataSafely();
      let total = 0;

      tableData.forEach((record: any) => {
        if (record && typeof record === 'object') {
          let amount = 0;

          console.log('handleEditChange - 比较ID:', 'record.id =', record.id, 'data.record.id =', data.record.id, '相等?', record.id === data.record.id);

          if (record.id === data.record.id) {
            // 当前编辑的行使用新输入的值
            amount = parseFloat(data.value || '0');
            console.log('handleEditChange - 当前编辑行:', amount, '输入值:', data.value, 'record:', record);
          } else {
            // 其他行：优先使用编辑状态的值，如果没有则使用已保存的值
            if (record.editValueRefs && record.editValueRefs.orgAmount !== undefined && record.editValueRefs.orgAmount !== null) {
              amount = parseFloat(record.editValueRefs.orgAmount || '0');
              console.log('handleEditChange - 其他行(编辑状态):', amount, '来源: editValueRefs.orgAmount =', record.editValueRefs.orgAmount);
            } else {
              amount = parseFloat(record.orgAmount || '0');
              console.log('handleEditChange - 其他行(已保存):', amount, '来源: orgAmount =', record.orgAmount);
            }
          }

          if (!isNaN(amount) && amount >= 0) {
            total += amount;
          }
        }
      });

      allocatedAmount.value = total;
      console.log('handleEditChange - 实时更新已分配金额:', total, '剩余:', totalAmount.value - total);
    }
  }

  // 处理表格编辑结束事件
  function handleEditEnd(data: any) {
    console.log('handleEditEnd - 编辑结束:', data);
    // 如果是金额列编辑结束，重新计算已分配金额
    if (data.column && data.column.dataIndex === 'orgAmount') {
      console.log('handleEditEnd - 金额列编辑结束，重新计算');
      // 延迟一点时间确保数据已经保存
      setTimeout(() => {
        calculateAllocatedAmount();
      }, 50);
    }
  }

  // 票据详情表格行点击事件
  function handleBillDetailRowClick(record: Recordable) {
    record.onEdit?.(true, false);
  }

  // 新增票据详情
  async function handleBillDetailAdd() {
    try {
      // 检查 listSelectRef 是否已经初始化
      if (!listSelectRef.value) {
        showMessage('组件正在初始化，请稍后再试');
        return;
      }

      // 检查 openSelectModal 方法是否存在
      if (typeof listSelectRef.value.openSelectModal !== 'function') {
        showMessage('选择功能暂不可用，请稍后再试');
        return;
      }

      // 等待一小段时间确保组件完全初始化
      await new Promise((resolve) => setTimeout(resolve, 100));
      console.log('开始打开选择弹窗', listSelectRef.value);

      listSelectRef.value.openSelectModal();
    } catch (error) {
      console.error('新增票据详情失败:', error);
      showMessage('新增票据详情失败，请重试');
    }
  }

  // 删除票据详情
  function handleBillDetailDelete(record: Recordable) {
    try {
      billDetailTable.deleteTableDataRecord(record);
      // 删除后重新计算已分配金额
      setTimeout(() => {
        calculateAllocatedAmount();
      }, 50);
    } catch (error) {
      console.error('删除票据详情失败:', error);
      showMessage('删除票据详情失败，请重试');
    }
  }

  // 票据选择弹窗提交
  async function handleBillSubmit() {
    try {
      const formData = await validateBillForm();

      // 获取表格数据
      const tableData = getTableDataSafely();
      if (tableData.length === 0) {
        showMessage('请至少添加一条票据详情');
        return;
      }

      // 验证表格数据
      for (const record of tableData) {
        if (!(await record.onEdit?.(false, true))) {
          showMessage('请完善票据详情信息');
          return;
        }
        record.parentId = currentRecord.value.id;
        record.parentDid = currentRecord.value.parentId;
        record.irowno = currentRecord.value.irowno;
        record.parentCode = currentRecord.value.parentCode;
      }

      // 最终验证总金额 - 必须完全相等
      calculateAllocatedAmount();
      if (Math.abs(allocatedAmount.value - totalAmount.value) > 0.01) {
        const difference = totalAmount.value - allocatedAmount.value;
        if (difference > 0) {
          showMessage(
            `票据详情总金额(${allocatedAmount.value.toFixed(2)})必须等于票据总金额(${totalAmount.value.toFixed(2)})，还需分配${difference.toFixed(2)}`,
          );
        } else {
          showMessage(
            `票据详情总金额(${allocatedAmount.value.toFixed(2)})必须等于票据总金额(${totalAmount.value.toFixed(2)})，超出${Math.abs(difference).toFixed(2)}`,
          );
        }
        return;
      }
      // 加3位随机数
      formData.list = tableData;
      formData.id = currentRecord.value.id;
      const params: any = {
        isNewRecord: false,
        id: formData.id,
      };
      console.log('提交数据:', formData);
      const res = await billManagerPayApplyC1BillSelection(params, formData);
      if (res.result === 'true') {
        console.log('票据详情提交数据:', {
          formData,
          tableData,
          originalRecord: currentRecord.value,
          totalAmount: totalAmount.value,
          allocatedAmount: allocatedAmount.value,
        });

        showMessage('票据详情保存成功');

        // 检查是否有新增的票据详情数据
        const hasNewRecords = tableData.some((record: any) => record.isNewRecord === true);
        const hasExistingRecords = tableData.some((record: any) => record.isNewRecord === false);
        console.log('包含新增记录:', hasNewRecords, '包含现有记录:', hasExistingRecords, '表格数据:', tableData);

        // 始终触发 success 事件，让父组件处理数据更新（新增或修改）
        console.log('触发 success 事件，让父组件处理数据更新');
        emit('success', {
          formData,
          tableData,
          originalRecord: currentRecord.value,
          hasNewRecords,
          hasExistingRecords
        });

        closeBillModal();
      }
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    }
  }

  async function handleSelect(selectData: any) {
    console.log('选择的票据数据:', selectData);

    try {
      // 获取当前表格中已存在的票据号
      const existingTableData = getTableDataSafely();
      const existingBillNos = existingTableData.map((record: any) => record.clink).filter(Boolean);

      // 过滤掉已存在的票据，避免重复添加
      const newItems = selectData.filter((item: any) => {
        const billNo = item.clink || '';
        if (!billNo) return false; // 跳过没有票据号的数据

        if (existingBillNos.includes(billNo)) {
          console.log(`票据号 ${billNo} 已存在，跳过添加`);
          return false;
        }
        return true;
      });

      if (newItems.length === 0) {
        showMessage('所选票据已存在，未添加新的票据');
        return;
      }

      // 为每个新的票据添加一行到表格中
      newItems.forEach((item: any) => {
        const originalAmount = parseFloat(item.iramount || '0');
        billDetailTable.insertTableDataRecord({
          id: new Date().getTime() + Math.floor(Math.random() * 1000), // 确保每行的id唯一
          clink: item.clink || '', // 票据号
          orgAmount: originalAmount, // 当前分配金额
          cbank: item.cbank || '', // 承兑银行
          dexpiredate: item.dexpiredate || '', // 到期日期
          dreceiptdate: item.dreceiptdate || '', // 应答日期
          originalAmount: originalAmount, // 保存原始金额，用于验证
          isNewRecord: true,
          editable: true,
        });
      });

      // 显示添加结果
      const skippedCount = selectData.length - newItems.length;
      if (skippedCount > 0) {
        showMessage(`成功添加 ${newItems.length} 条票据，跳过 ${skippedCount} 条重复票据`);
      } else {
        showMessage(`成功添加 ${newItems.length} 条票据`);
      }

      // 重新计算已分配金额
      setTimeout(() => {
        calculateAllocatedAmount();
      }, 100);
    } catch (error) {
      console.error('处理选择的票据数据失败:', error);
      showMessage('添加票据数据失败，请重试');
    }
  }
</script>
