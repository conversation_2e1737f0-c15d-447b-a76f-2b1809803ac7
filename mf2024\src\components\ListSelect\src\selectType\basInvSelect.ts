import { useI18n } from '/@/hooks/web/useI18n';
import { BasicColumn, BasicTableProps, FormProps } from '/@/components/Table';
import { userListData } from '/@/api/sys/user';
import { basInvListData } from '/@/api/bas/inv/basInv';

const { t } = useI18n('sys.empUser');

const modalProps = {
  title: t('存货选择'),
};

const searchForm: FormProps = {
  baseColProps: { lg: 6, md: 8 },
  labelWidth: 100,
  schemas: [
    {
      label: t('存货编码'),
      field: 'invCode',
      component: 'Input',
    },
    {
      label: t('存货名称'),
      field: 'invName',
      component: 'Input',
    },
  ],
};

const tableColumns: BasicColumn[] = [
  {
    title: t('存货编码'),
    dataIndex: 'invCode',
    width: 100,
  },
  {
    title: t('存货名称'),
    dataIndex: 'invName',
    width: 100,
  },
  {
    title: t('规格型号'),
    dataIndex: 'cinvStd',
    width: 100,
  },
  {
    title: t('单位'),
    dataIndex: 'unitName',
    width: 130,
  },
];

const tableProps: BasicTableProps = {
  api: basInvListData,
  beforeFetch: (params) => {
    params['isAll'] = true;
    return params;
  },
  columns: tableColumns,
  formConfig: searchForm,
  rowKey: 'invCode',
};

export default {
  modalProps,
  tableProps,
  itemCode: 'invCode',
  itemName: 'invName',
  isShowCode: true,
};
