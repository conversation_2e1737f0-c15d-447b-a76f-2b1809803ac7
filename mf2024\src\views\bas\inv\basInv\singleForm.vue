<template>
  <div class="basicForm">
    <BasicForm @register="registerForm">
      <!-- 插槽（备注信息） -->
      <template #remarks="{ model, field }">
        <WangEditor
          v-model:value="model[field]"
          :bizKey="record.id"
          :bizType="'testDataChild_' + field"
          :height="300"
        />
      </template>
    </BasicForm>
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsTestTestDataForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, watch } from 'vue';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { TestData } from '/@/api/test/testData';
  import { WangEditor } from '/@/components/WangEditor';
  import { formSchemas } from '/@/utils/custom';

  const props = defineProps({
    schemas: { type: Array, default: [] },
    data: { type: Object, default: {} },
    formConfig: { type: Object, default: {} },
  });
  let inputFormSchemas: FormSchema[] = [];
  watch(
    () => props,
    () => {
      inputFormSchemas = formSchemas(
        props.schemas,
        props.formConfig.formColNum,
        props.data,
      ) as FormSchema[];

      setTimeout(() => {
        zdy(props.data);
      }, 0);
    },
    { immediate: true },
  );

  const record = ref<TestData>({} as TestData);
  const [registerForm, { resetFields, setFieldsValue, validate, setProps }] = useForm();
  async function zdy(data) {
    setProps({
      labelWidth: 120, // 控件前面的标签宽度
      schemas: inputFormSchemas, // 控件定义列表
      baseColProps: { lg: parseInt(props.formConfig.formColNum), md: 24 },
    });
    await resetFields(); // 重置表单数据
    record.value = data;
    await setFieldsValue(data); // 设置字段值
  }

  async function validateForm() {
    try {
      const data = await validate(); // 验证表单，并返回数据
      return data;
    } catch (error: any) {
      if (error && error.errorFields) {
        return false;
      }
    }
  }
  defineExpose({
    validateForm,
  });
</script>
<style>
  .col {
    color: red;
    font-weight: 100;
  }
</style>
