# PowerShell script to fix API import paths in mf2024 project

$files = @(
    "mf2024\src\components\Upload\src\data.tsx",
    "mf2024\src\views\layout\form\layVoucherFormBtn\form.vue",
    "mf2024\src\views\layout\form\layVoucherFormBtn\list.vue",
    "mf2024\src\views\layout\form\layVoucherFormConfig\configForm.vue",
    "mf2024\src\views\layout\form\layVoucherFormConfig\form.vue",
    "mf2024\src\views\layout\form\layVoucherFormConfig\list.vue",
    "mf2024\src\views\layout\form\layVoucherFormListCol\form.vue",
    "mf2024\src\views\layout\form\layVoucherFormListCol\list.vue",
    "mf2024\src\views\layout\form\layVoucherFormTabConfig\form.vue",
    "mf2024\src\views\layout\list\layVoucherTabConfig\configForm.vue",
    "mf2024\src\views\layout\list\layVoucherTabConfig\form.vue",
    "mf2024\src\views\layout\list\layVoucherTabConfig\list.vue",
    "mf2024\src\views\layout\list\layVoucherTableCol\form.vue",
    "mf2024\src\views\layout\list\layVoucherTableCol\list.vue",
    "mf2024\src\views\layout\list\layVoucherTabRightOpe\form.vue",
    "mf2024\src\views\layout\list\layVoucherTabRightOpe\list.vue",
    "mf2024\src\views\layout\list\layVoucherTabToolbar\form.vue",
    "mf2024\src\views\layout\list\layVoucherTabToolbar\list.vue",
    "mf2024\src\views\layout\vouch\layVoucher\form.vue",
    "mf2024\src\views\layout\vouch\layVoucher\index.vue",
    "mf2024\src\views\layout\vouch\layVoucher\list.vue",
    "mf2024\src\views\layout\vouch\layVoucherFields\form.vue",
    "mf2024\src\views\layout\vouch\layVoucherFields\list.vue",
    "mf2024\src\views\layout\vouch\layVoucherView\form.vue",
    "mf2024\src\views\layout\vouch\layVoucherView\list.vue"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Processing $file"
        $content = Get-Content $file -Raw
        
        # Replace relative API paths with alias paths
        $content = $content -replace "from\s+['""]\.\.\/\.\.\/\.\.\/api\/", "from '/@/api/"
        $content = $content -replace "from\s+['""]\.\.\/\.\.\/\.\.\/\.\.\/api\/", "from '/@/api/"
        $content = $content -replace "from\s+['""]\.\.\/\.\.\/api\/", "from '/@/api/"
        
        Set-Content $file $content -NoNewline
        Write-Host "Fixed $file"
    } else {
        Write-Host "File not found: $file"
    }
}

Write-Host "All files processed!"
