/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface LayVoucherTabConfig extends BasicModel<LayVoucherTabConfig> {
  viewCode?: string; // 布局标志
  clickToRowSelect?: number; // 点击是否选中行
  showTableSetting?: number; // 是否显示表格设置工具
  tableSetting?: string; // 是否显示表格设置工具配置
  striped?: number; // 是否显示斑马纹
  inset?: number; // 是否取消表格默认padding
  autoCreateKey?: number; // 是否自动生成key
  showSummary?: number; // 是否显示合计行
  emptyDataIsShowTable?: number; // 是否显示表格
  isTreeTable?: number; // 是否树表
  immediate?: number; // 是否延迟加载表格数据
  useSearchForm?: number; // 是否启用搜索表单
  showIndexColumn?: number; // 是否显示行号
  ellipsis?: number; // 文本超过宽度是否显示省略号
  canResize?: number; // 是否自适应高度
  clearSelectOnPageChange?: number; // 切换页面是否重置勾选状态
  api?: string; // 请求接口
  bordered?: number; // 是否显示表格边框
  loading?: number; // 是否显示表格loading状态
  title?: string; // 表格标题
  titleHelpMessage?: string; // 表格右侧温馨提示
  maxHeight?: number; // 表格最大高度
  resizeHeightOffset?: number; // 自适应高度
}

export const layVoucherTabConfigList = (params?: LayVoucherTabConfig | any) =>
  defHttp.get<LayVoucherTabConfig>({ url: adminPath + '/layout/listTabConfig/list', params });

export const layVoucherTabConfigListData = (params?: LayVoucherTabConfig | any) =>
  defHttp.post<Page<LayVoucherTabConfig>>({
    url: adminPath + '/layout/listTabConfig/listData',
    params,
  });

export const layVoucherTabConfigForm = (params?: LayVoucherTabConfig | any) =>
  defHttp.get<LayVoucherTabConfig>({ url: adminPath + '/layout/listTabConfig/form', params });

export const layVoucherTabConfigFindOne = (params?: LayVoucherTabConfig | any) =>
  defHttp.get<LayVoucherTabConfig>({
    url: adminPath + '/layout/listTabConfig/findOne',
    params,
  });

export const layVoucherTabConfigSave = (params?: any, data?: LayVoucherTabConfig | any) =>
  defHttp.postJson<LayVoucherTabConfig>({
    url: adminPath + '/layout/listTabConfig/save',
    params,
    data,
  });

export const layVoucherTabConfigDelete = (params?: LayVoucherTabConfig | any) =>
  defHttp.get<LayVoucherTabConfig>({
    url: adminPath + '/layout/listTabConfig/delete',
    params,
  });

export const exportMethod = (params?: any) =>
  defHttp.get<LayVoucherTabConfig>({
    url: adminPath + `/common/getBeanMethod`,
    params,
  });
