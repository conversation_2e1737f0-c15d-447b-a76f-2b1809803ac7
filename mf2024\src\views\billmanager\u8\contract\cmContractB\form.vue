<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'billmanager:u8:contract:cmContractB:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsBillmanagerU8ContractCmContractBForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { CmContractB, cmContractBSave, cmContractBForm } from '/@/api/billmanager/u8/contract/cmContractB';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('billmanager.u8.contract.cmContractB');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<CmContractB>({} as CmContractB);

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增合同') : t('编辑合同'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('合同分组编码'),
      field: 'strcontractgrp',
      component: 'Input',
      componentProps: {
        maxlength: 12,
      },
    },
    {
      label: t('合同类型编码'),
      field: 'strcontracttype',
      component: 'Input',
      componentProps: {
        maxlength: 12,
      },
      required: true,
    },
    {
      label: t('合同性质'),
      field: 'strcontractkind',
      component: 'Input',
      componentProps: {
        maxlength: 32,
      },
      required: true,
    },
    {
      label: t('合同名称'),
      field: 'strcontractname',
      component: 'Input',
      componentProps: {
        maxlength: 400,
      },
    },
    {
      label: t('对方单位'),
      field: 'strbisectionunit',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('所属主合同编码'),
      field: 'strparentid',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('保修期'),
      field: 'strrepair',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
    },
    {
      label: t('对方负责人'),
      field: 'strbisectionperson',
      component: 'Input',
      componentProps: {
        maxlength: 50,
      },
    },
    {
      label: t('合同签定日期'),
      field: 'strcontractorderdate',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('合同开始日期'),
      field: 'strcontractstartdate',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('合同结束日期'),
      field: 'strcontractenddate',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('合同描述'),
      field: 'strcontractdesc',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('质保金比例'),
      field: 'dblmassassurescale',
      component: 'Input',
      componentProps: {
        maxlength: 51,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('质保金额度'),
      field: 'dblmassassure',
      component: 'Input',
      componentProps: {
        maxlength: 51,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('cdefine1'),
      field: 'cdefine1',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('cdefine2'),
      field: 'cdefine2',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('cdefine3'),
      field: 'cdefine3',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('cdefine4'),
      field: 'cdefine4',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('cdefine5'),
      field: 'cdefine5',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('cdefine6'),
      field: 'cdefine6',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('cdefine7'),
      field: 'cdefine7',
      component: 'Input',
      componentProps: {
        maxlength: 51,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('cdefine8'),
      field: 'cdefine8',
      component: 'Input',
      componentProps: {
        maxlength: 4,
      },
    },
    {
      label: t('cdefine9'),
      field: 'cdefine9',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
    },
    {
      label: t('cdefine10'),
      field: 'cdefine10',
      component: 'Input',
      componentProps: {
        maxlength: 60,
      },
    },
    {
      label: t('cdefine11'),
      field: 'cdefine11',
      component: 'Input',
      componentProps: {
        maxlength: 120,
      },
    },
    {
      label: t('cdefine12'),
      field: 'cdefine12',
      component: 'Input',
      componentProps: {
        maxlength: 120,
      },
    },
    {
      label: t('cdefine13'),
      field: 'cdefine13',
      component: 'Input',
      componentProps: {
        maxlength: 120,
      },
    },
    {
      label: t('cdefine14'),
      field: 'cdefine14',
      component: 'Input',
      componentProps: {
        maxlength: 120,
      },
    },
    {
      label: t('cdefine15'),
      field: 'cdefine15',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('cdefine16'),
      field: 'cdefine16',
      component: 'Input',
      componentProps: {
        maxlength: 51,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('创建人'),
      field: 'strsetupperson',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('创建日期'),
      field: 'strsetupdate',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('结案人'),
      field: 'strendcaseperson',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('结案日期'),
      field: 'strendcasedate',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('生效人'),
      field: 'strinureperson',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('生效日期'),
      field: 'strinuredate',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('部门编码'),
      field: 'strdeptid',
      component: 'Input',
      componentProps: {
        maxlength: 30,
      },
    },
    {
      label: t('业务员编码'),
      field: 'strpersonid',
      component: 'Input',
      componentProps: {
        maxlength: 30,
      },
    },
    {
      label: t('变更单号'),
      field: 'intvaryid',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('变更原因码'),
      field: 'strvarycauseid',
      component: 'Input',
      componentProps: {
        maxlength: 30,
      },
    },
    {
      label: t('变更日期'),
      field: 'dtvarydate',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('变更申请人'),
      field: 'strvarypersonid',
      component: 'Input',
      componentProps: {
        maxlength: 30,
      },
    },
    {
      label: t('变更生效人'),
      field: 'strvarypasspersonid',
      component: 'Input',
      componentProps: {
        maxlength: 30,
      },
    },
    {
      label: t('期初标志'),
      field: 'intpre',
      component: 'Input',
      componentProps: {
        maxlength: 1,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('收支方向'),
      field: 'strway',
      component: 'Input',
      componentProps: {
        maxlength: 50,
      },
    },
    {
      label: t('币种'),
      field: 'strcurrency',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
    },
    {
      label: t('汇率'),
      field: 'dblexchange',
      component: 'Input',
      componentProps: {
        maxlength: 51,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('变更制单人'),
      field: 'strvaryperson',
      component: 'Input',
      componentProps: {
        maxlength: 50,
      },
    },
    {
      label: t('时间戳'),
      field: 'tstime',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('备用字段1'),
      field: 'strspare1',
      component: 'Input',
      componentProps: {
        maxlength: 128,
      },
    },
    {
      label: t('备用字段2'),
      field: 'strspare2',
      component: 'Input',
      componentProps: {
        maxlength: 128,
      },
    },
    {
      label: t('备用字段3'),
      field: 'strspare3',
      component: 'Input',
      componentProps: {
        maxlength: 128,
      },
    },
    {
      label: t('备用'),
      field: 'strsource',
      component: 'Input',
      componentProps: {
        maxlength: 50,
      },
    },
    {
      label: t('备用'),
      field: 'dbltotalcurrency',
      component: 'Input',
      componentProps: {
        maxlength: 51,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('备用'),
      field: 'dblexeccurrency',
      component: 'Input',
      componentProps: {
        maxlength: 51,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('备用'),
      field: 'dbltotalquantity',
      component: 'Input',
      componentProps: {
        maxlength: 51,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('备用'),
      field: 'dblexecquqantity',
      component: 'Input',
      componentProps: {
        maxlength: 51,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('业务类型'),
      field: 'cbustype',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('运输方式'),
      field: 'csccode',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
    },
    {
      label: t('收付款计划'),
      field: 'cgatheringplan',
      component: 'Input',
      componentProps: {
        maxlength: 15,
      },
    },
    {
      label: t('是否工作流控制'),
      field: 'iswfcontrolled',
      component: 'Input',
      componentProps: {
        maxlength: 1,
      },
    },
    {
      label: t('控制流控制状态'),
      field: 'iverifystate',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('返回工作流审核次数'),
      field: 'ireturncount',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('审核标志'),
      field: 'intauditsymbol',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('质保金计算方式'),
      field: 'czbjcomputemode',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
    },
    {
      label: t('质保金开始日期'),
      field: 'dtzbjstartdate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('质保金结束日期'),
      field: 'dtzbjenddate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('是否启用阶段'),
      field: 'busestage',
      component: 'Input',
      componentProps: {
        maxlength: 1,
      },
      required: true,
    },
    {
      label: t('阶段组编码'),
      field: 'cstagegroupcode',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('制单时间'),
      field: 'dtcreatetime',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('修改时间'),
      field: 'dtmodifytime',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('修改时间'),
      field: 'dtmodifydate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('生效时间'),
      field: 'dteffecttime',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('修改人'),
      field: 'cmodifer',
      component: 'Input',
      componentProps: {
        maxlength: 30,
      },
    },
    {
      label: t('变更单制单时间'),
      field: 'dtvarycreatedate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('变更单制单时间'),
      field: 'dtvarycreatetime',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('变更单修改时间'),
      field: 'dtvarymodifytime',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('变更单修改日期'),
      field: 'dtvarymodifydate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('变更单生效时间'),
      field: 'dtvaryeffecttime',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('变更单修改人'),
      field: 'cvarymodifer',
      component: 'Input',
      componentProps: {
        maxlength: 30,
      },
    },
    {
      label: t('合同状态'),
      field: 'istatus',
      component: 'Input',
      componentProps: {
        maxlength: 3,
      },
      rules: [{ required: true }, { pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('时效控制方式'),
      field: 'intexeccontroltype',
      component: 'Input',
      componentProps: {
        maxlength: 1,
      },
      rules: [{ required: true }, { pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('时效控制环节'),
      field: 'cexeccontrolvouch',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
      required: true,
    },
    {
      label: t('打印次数'),
      field: 'iprintcount',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ required: true }, { pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('贸易术语'),
      field: 'iincotermid',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('最迟装船日期'),
      field: 'dlastladedate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('装运港'),
      field: 'csportcode',
      component: 'Input',
      componentProps: {
        maxlength: 40,
      },
    },
    {
      label: t('转运港'),
      field: 'ctportcode',
      component: 'Input',
      componentProps: {
        maxlength: 40,
      },
    },
    {
      label: t('目的港'),
      field: 'caportcode',
      component: 'Input',
      componentProps: {
        maxlength: 40,
      },
    },
    {
      label: t('委托方编码'),
      field: 'centrustcode',
      component: 'Input',
      componentProps: {
        maxlength: 40,
      },
    },
    {
      label: t('代理费计算方式'),
      field: 'cagencycalmethod',
      component: 'Input',
      componentProps: {
        maxlength: 2,
      },
    },
    {
      label: t('代理费计费费率'),
      field: 'decagencyfeebillrates',
      component: 'Input',
      componentProps: {
        maxlength: 28,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('从量收费标准'),
      field: 'decspecficcharges',
      component: 'Input',
      componentProps: {
        maxlength: 28,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('代理费'),
      field: 'decagencyfees',
      component: 'Input',
      componentProps: {
        maxlength: 28,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('委托方收款金额'),
      field: 'decentgathering',
      component: 'Input',
      componentProps: {
        maxlength: 28,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('委托方收款日期'),
      field: 'dentgatheringdate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('联系人编码'),
      field: 'ccontactcode',
      component: 'Input',
      componentProps: {
        maxlength: 60,
      },
    },
    {
      label: t('单据条码'),
      field: 'csysbarcode',
      component: 'Input',
      componentProps: {
        maxlength: 60,
      },
    },
    {
      label: t('当前审核人'),
      field: 'ccurrentauditor',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('来源类别'),
      field: 'csourcetype',
      component: 'Input',
      componentProps: {
        maxlength: 2,
      },
    },
    {
      label: t('商机编码'),
      field: 'ioppid',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('商机主题'),
      field: 'coppcode',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await cmContractBForm(data);
    record.value = (res.cmContractB || {}) as CmContractB;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        strcontractid: record.value.strcontractid,
        guid: record.value.guid,
      };
      // console.log('submit', params, data, record);
      const res = await cmContractBSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
