export default {
  api: {
    operationFailed: 'Operation failed',
    errorTip: 'System Tip',
    errorMessage: 'The operation failed, the system is abnormal!',
    timeoutMessage: 'Login timed out, please log in again!',
    apiTimeoutMessage: 'The interface request timed out, please refresh the page and try again!',
    apiRequestFailed: 'The interface request failed, please try again later!',
    networkException: 'network anomaly',
    networkExceptionMsg: 'The network is abnormal, please try again later!',

    errMsg401: 'The user does not have permission (token, user name, password error)!',
    errMsg403: 'The user is authorized, but access is forbidden!',
    errMsg404: 'Network request error, the resource was not found!',
    errMsg405: 'Network request error, request method not allowed!',
    errMsg408: 'Network request timed out!',
    errMsg500: 'Server error, please contact the administrator!',
    errMsg501: 'The network is not implemented!',
    errMsg502: 'Network Error!',
    errMsg503: 'The service is unavailable, the server is temporarily overloaded or maintained!',
    errMsg504: 'Network timeout!',
    errMsg505: 'The http version does not support the request!',
  },
  message: {
    error: 'failure,error',
    warning: 'no,not,Not,already exists',
    success: 'success,completion',
  },
  app: {
    logoutTip: 'Reminder',
    logoutMessage: 'Confirm to exit the system?',
    menuLoading: 'Please wait, loading...',
  },
  errorLog: {
    tableTitle: 'Error log list',
    tableColumnType: 'Type',
    tableColumnDate: 'Time',
    tableColumnFile: 'File',
    tableColumnMsg: 'Error message',
    tableColumnStackMsg: 'Stack info',

    tableActionDesc: 'Details',

    modalTitle: 'Error details',

    fireVueError: 'Fire vue error',
    fireResourceError: 'Fire resource error',
    fireAjaxError: 'Fire ajax error',

    enableMessage: 'Only effective when useErrorHandle is true in `projectSetting.ts`.',
  },
  exception: {
    backLogin: 'Back Login',
    backHome: 'Back Home',
    subTitle403: "Sorry, you don't have access to this page.",
    subTitle404: 'Sorry, the page you visited does not exist.',
    subTitle500: 'Sorry, the server is reporting an error.',
    noDataTitle: 'No data on the current page.',
    networkErrorTitle: 'Network Error',
    networkErrorSubTitle:
      'Sorry，Your network connection has been disconnected, please check your network!',
  },
  lock: {
    unlock: 'Click to unlock',
    alert: 'Lock screen password error',
    backToLogin: 'Back to login',
    entry: 'Enter the system',
    placeholder: 'Please enter the lock screen password or user password',
  },
  login: {
    backSignIn: 'Back sign in',
    mobileSignInFormTitle: 'Mobile sign in',
    qrSignInFormTitle: 'Qr code sign in',
    signInFormTitle: 'Sign in',
    signUpFormTitle: 'Sign up',
    forgetFormTitle: 'Reset password',

    signInTitle: 'Backstage management system',
    signInDesc: 'Enter your personal details and get started!',
    policy: 'I agree to the JeeSite Privacy Policy',
    scanSign: `scanning the code to complete the login`,

    loginButton: 'Sign in',
    registerButton: 'Sign up',
    rememberMe: 'Remember me',
    forgetPassword: 'Forget Password?',
    otherSignIn: 'Sign in with',

    // notify
    loginSuccessTitle: 'Login successful',
    loginSuccessDesc: 'Welcome back',

    // placeholder
    accountPlaceholder: 'Please input username',
    passwordPlaceholder: 'Please input password',
    smsPlaceholder: 'Please input sms code',
    mobilePlaceholder: 'Please input mobile',
    policyPlaceholder: 'Register after checking',
    diffPwd: 'The two passwords are inconsistent',
    corpPlaceholder: 'Please selection tenant',

    userName: 'Username',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    validCode: 'Valid Code',
    email: 'Email',
    smsCode: 'SMS code',
    mobile: 'Mobile',
  },
  account: {
    center: 'Account Center',
    userInfo: 'User Info',
    modifyPwd: 'Modify Password',
    modifyPwdTip: 'Change the password periodically',
    modifyPqa: 'Security Question',
    modifyPqaTip: 'Please change the security periodically',

    basicTab: 'Basic Setting',
    securityTab: 'Security Setting',
    bindingTab: 'Account Binding',

    userName: 'Nick name',
    email: 'Email',
    mobile: 'Mobile',
    phone: 'Phone',
    sign: 'Sign',
    changeAvatar: 'Change Avatar',
    updateBtn: 'Update Info',

    oldPassword: 'Current Password',
    newPassword: 'New Password',
    confirmNewPassword: 'Password',
    newPasswordInputTip: 'Please input new password',
    newPasswordNotBlank: 'Password not blank',
    newPasswordNotEquals: 'The two password entries are inconsistent',
  },
  msg: {
    title: 'Message',
    listTitle: 'Message List',
    viewTitle: 'Message Details',
    viewButton: 'Details',
    mergeMsgTitle: 'System Message',
    mergeMsgContent:
      'You have {0} new message, because there are too many messages, here for you to merge, please click the Details button to see.',
    viewAllMsgButton: 'View All Message',
    unreadMsgTitle: 'Unread Message',
    readMsgTitle: 'Read Message',
    readAllMsgButton: 'All are marked read',
    readAllMsgConfirm: 'Do you want to mark all unread information as read?',
  },
  empUser: {
    用户管理: 'User manage',
    新增用户: 'New user',
    编辑用户: 'Edit user',
    停用用户: 'Disable user',
    是否确认停用用户: 'Are you sure want to disable this user?',
    启用用户: 'Enable user',
    是否确认启用用户: 'Are you sure want to enable this user?',
    删除用户: 'Delete user',
    是否确认删除用户: 'Are you sure want to delete this user?',
    用户分配角色: 'User assign roles',
    分配角色: 'Assign roles',
    用户分配数据权限: 'User assign data scopes',
    数据权限: 'Data scopes',
    用户密码重置: 'User password reset',
    是否确认重置密码: 'Are you sure want to reset password this user?',
    重置密码: 'Reset password',

    归属机构: 'Organization',
    归属公司: 'Company',
    登录账号: 'Account',
    用户昵称: 'Nick name',
    电子邮箱: 'Email',
    手机号码: 'Mobile',
    办公电话: 'Phone',
    权重: 'Weight',
    '排序，权重越大排名越靠前，请填写数字。': 'Weight big, ranking the front, fill Numbers.',

    员工工号: 'Work No',
    员工姓名: 'Name',
    所在岗位: 'Position',
    英文名: 'English name',

    附属机构: 'Auxiliary organization',
    附属岗位: 'Auxiliary post',

    角色名称: 'Role name',
    角色编码: 'Role code',

    租户名称: 'Tenant name',
    租户代码: 'Tenant code',

    新增管理员: 'New administrator',
    编辑管理员: 'Edit administrator',
    二级管理员: 'Secondary administrator',
    可管理的数据权限: 'Manage data permissions',

    导入用户数据: 'Import user data',
    选择文件: 'Select file',
    是否更新已经存在的用户数据: 'Whether to update existing user data',
    下载模板: 'Download template',
    '如果用户编码已经存在，更新这条数据。': 'Update this data if the user code already exists.',
    '提示：仅允许导入“xls”或“xlsx”格式文件！':
      "Tip: only 'xls' or 'xlsx' files are allowed to be imported!",
    '文件不正确，请选择后缀为“xls”或“xlsx”的文件。':
      "Incorrect file. Please select a file with suffix 'xls' or 'xlsx'.",
  },
  office: {
    新增机构: 'New organization',
    编辑机构: 'Edit organization',
    停用机构: 'Disable organization',
    是否确认停用机构: 'Are you sure want to disable this organization?',
    启用机构: 'Enable organization',
    是否确认启用机构: 'Are you sure want to enable this organization?',
    删除机构: 'Delete organization',
    是否确认删除机构: 'Are you sure want to delete this organization and sub organization?',
    新增下级机构: 'New sub organization',

    上级机构: 'Parent',
    机构名称: 'Name',
    机构代码: 'Code',
    机构全称: 'Full name',
    机构类型: 'Type',
    负责人: 'Principal',
    办公电话: 'Phone',
    联系地址: 'Address',
    电子邮箱: 'E-mail',
    邮政编码: 'Zip code',
  },
  company: {
    新增公司: 'New company',
    编辑公司: 'Edit company',
    停用公司: 'Disable company',
    是否确认停用机构: 'Are you sure want to disable this company?',
    启用公司: 'Enable company',
    是否确认启用机构: 'Are you sure want to enable this company?',
    删除公司: 'Delete company',
    是否确认删除机构: 'Are you sure want to delete this company and sub company?',
    新增下级公司: 'New sub company',

    上级公司: 'Parent',
    公司名称: 'Name',
    公司代码: 'Code',
    公司全称: 'Full name',
    归属区域: 'Area',
    包含机构: 'Organization',
  },
  post: {
    新增岗位: 'New position',
    编辑岗位: 'Edit position',
    停用岗位: 'Disable position',
    是否确认停用岗位: 'Are you sure want to disable this position?',
    启用岗位: 'Enable position',
    是否确认启用岗位: 'Are you sure want to enable this position?',
    删除岗位: 'Delete position',
    是否确认删除岗位: 'Are you sure want to delete this position?',

    岗位名称: 'Name',
    岗位代码: 'Code',
    岗位分类: 'Type',
    关联角色: 'Role',
  },
  role: {
    用户类型: 'User type',
    系统角色: 'System role',
    角色分类: 'Category',
    数据范围: 'Data scope',
    业务范围: 'Business scope',
    桌面地址: 'Desktop URL',
    是否可见: 'Visible',

    角色编码: 'Role code',
    角色名称: 'Role name',
    角色名称已存在: 'Role name already exists',
    授权功能菜单: 'Authorization menu',

    未设置: 'None',
    全部数据: 'All data',
    自定义数据: 'Custom data',
    本部门数据: 'Current organization data',
    本公司数据: 'Current company data',
    本部门和本公司数据: 'Current organization and company data',
    控制业务范围: 'Business scope',

    新增角色: 'New role',
    编辑角色: 'Edit role',
    停用角色: 'Disable role',
    是否确认停用角色: 'Are you sure you want to disable role?',
    启用角色: 'Enable role',
    是否确认启用角色: 'Are you sure you want to enable role?',
    删除角色: 'Delete role',
    是否确认删除角色: 'Are you sure you want to delete role?',
    角色分配功能权限: 'Role assign function permissions',
    授权菜单: 'Authorization Menu',
    角色分配数据权限: 'Role assign data permissions',
    授权数据权限: 'Authorization data permissions',
    数据权限: 'Data permissions',
    分配用户: 'Assign user ',
    角色分配用户: 'Role assign user',

    登录账号: 'Account',
    用户昵称: 'Nick name',
    电子邮箱: 'Email',
    手机号码: 'Mobile',
    办公电话: 'Phone',
    添加用户: 'Add user',
    批量取消: 'Batch cancel',
    请在列表选中要取消角色的用户: 'Please select the user to cancel the role in the list',
    没有选择要授权的用户: 'There are no users selected to authorize',
    取消授权: 'Cancel authorization',
    是否确认取消选中用户的角色: 'Are you sure you want cancel user role?',
  },
  menu: {
    新增菜单: 'New menu',
    编辑菜单: 'Edit menu',
    停用菜单: 'Disable menu',
    是否确认停用菜单: 'Are you sure want to disable this menu?',
    启用菜单: 'Enable menu',
    是否确认启用菜单: 'Are you sure want to enable this menu?',
    删除菜单: 'Delete menu',
    是否确认删除菜单: 'Are you sure want to delete this menu and sub menu?',
    新增下级菜单: 'New sub menu',

    上级菜单: 'Parent menu',
    菜单类型: 'Menu type',
    菜单名称: 'Menu name',
    归属模块: 'Module',
    链接地址: 'Href',
    链接目标: 'Target',
    组件路径: 'Comp name',
    组件参数: 'Comp params',
    权限标识: 'Permission',
    菜单图标: 'Icon',
    字体颜色: 'Color',
    页签标题: 'Tab title',
    是否可见: 'Visible',
    菜单权重: 'Weight',
    其它信息: 'Other information',

    地址: 'Href',
    类型: 'Type',
    排序: 'Sort',
  },
  module: {
    新增模块: 'New module',
    编辑模块: 'Edit module',
    停用模块: 'Disable module',
    是否确认停用模块: 'Are you sure you want to disable the module?',
    启用模块: 'Enable module',
    是否确认启用模块: 'Are you sure you want to enable this module?',
    删除模块: 'Delete module',
    是否确认删除模块: 'Are you sure you want to delete this module?',

    模块名称: 'Name',
    模块编码: 'Code',
    主类全名: 'Class name',
    模块描述: 'Description',
    当前版本: 'Current version',
    升级信息: 'Upgrade info',

    版本: 'Version',
    未知: 'Unknown',
    未安装: 'Uninstalled',
  },
  config: {
    新增参数: 'New config',
    编辑参数: 'Edit config',
    删除参数: 'Delete config',
    是否确认删除参数: 'Are you sure you want to delete this config?',

    参数名称: 'Config name',
    参数键名: 'Config key',
    参数键名已存在: 'Config key already exists',
    参数键值: 'Config value',
    参数描述: 'Config description',
    系统内置: 'System',
  },
  dictType: {
    新增字典: 'New dictionary type',
    编辑字典: 'Edit dictionary type',
    停用字典: 'Disable dictionary type',
    是否确认停用字典: 'Are you sure you want to disable the dictionary type?',
    启用字典: 'Enable dictionary type',
    是否确认启用字典: 'Are you sure you want to enable the dictionary type?',
    删除字典: 'Delete dictionary type',
    是否确认删除字典: 'Are you sure you want to delete the dictionary type?',
    字典数据: 'The data dictionary',

    字典名称: 'Name',
    字典类型: 'Type',
    系统字典: 'System',
  },
  dictData: {
    字典选项: 'Dictionary option',
    新增选项: 'New dictionary option',
    编辑选项: 'Edit dictionary option',
    停用选项: 'Disable dictionary option',
    是否确认停用选项: 'Are you sure you want to disable the dictionary option?',
    启用选项: 'Enable dictionary option',
    是否确认启用选项: 'Are you sure you want to enable the dictionary option?',
    删除选项: 'Delete dictionary option',
    是否确认删除选项: 'Are you sure you want to delete the dictionary option?',
    新增下级选项: 'New sub dictionary',

    上级选项: 'Parent',
    选项标签: 'Label',
    选项键值: 'Key',
    系统内置: 'System',
    选项描述: 'Description',
    选项图标: 'Icon',
    CSS样式: 'CSS style',
    CSS类名: 'CSS class',
  },
  area: {
    区域管理: 'Area manage',
    新增区域: 'New area',
    编辑区域: 'Edit area',

    停用区域: 'Disable area',
    是否确认停用区域: 'Are you sure want to disable this area?',
    启用区域: 'Enable area',
    是否确认启用区域: 'Are you sure want to enable this area?',
    删除区域: 'Delete area',
    是否确认删除区域: 'Are you sure want to delete this area and sub area?',
    新增下级区域: 'New sub area',

    上级区域: 'Parent Area',
    区域名称: 'Area name',
    区域代码: 'Area code',
    区域类型: 'Area type',
  },
  log: {
    接入日志: 'Access log',
    修改日志: 'Modify log',
    查询日志: 'Query log',

    登录登出: 'Login logout',
    日志标题: 'Log title',
    请求地址: 'Request address',
    日志类型: 'Type',
    操作时间: 'Date',
    客户端IP: 'Client IP',
    设备名称: 'Device',
    浏览器名: 'Browser',
    响应时间: 'Time',
    异常: 'Exception',
    今日: 'Today',
    本周: 'This week',
    上周: 'Last week',
    本月: 'This month',
    本季度: 'This quarter',
    上1个月: 'Last month',
    上3个月: 'Last 3 months',
    近1个周: 'Nearly 1 week',
    近1个月: 'Nearly 1 months',
    近3个月: 'Nearly 3 months',

    操作用户: 'Operation user',
    业务主键: 'Business Key',
    业务类型: 'Business Type',
    系统登录: 'System Login',
    未知操作: 'Unknown',
    日志详情: 'Log Detail',
    请求数据: 'Submitted data',
    操作账号: 'Operation account',
    用户代理: 'User agent',
  },
  online: {
    操作用户: 'Operation user',
    查询所有在线: 'All online',
    包含3分钟以上未操作的用户: 'Contains users who have been inactive for more than 3 minutes',
    包含游客用户: 'Visitor users',
    包含未登录的用户: 'Contains unlogged users',
    用户名称: 'User name',
    游客: 'tourists',
    最后访问: 'Last visit',
    超时时间: 'Timeout',
    客户主机: 'Client host',
    设备类型: 'Device type',
    踢出在线用户: 'Kick out online users',
    是否要踢出在线状态: 'Are you sure you want to kick the user online?',
  },
};
