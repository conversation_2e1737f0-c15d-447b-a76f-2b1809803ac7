/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../../model/baseModel';
import { UploadApiResult } from '../../../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface BillmanagerPayApplyC extends BasicModel<BillmanagerPayApplyC> {
  parentId?: string; // 主表ID
  payType?: string; // 款项类型
  venCode?: string; // 供应商
  cexchName?: string; // 币种
  orgAmount?: number; // 原币金额
  deptCode?: string; // 部门
  sourceId?: string; // 来源id
  contractId?: string; // 合同号
  useOfFunds?: string; // 资金用途
  u8Status?: string; // U8生单状态
}

export const billmanagerPayApplyCList = (params?: BillmanagerPayApplyC | any) =>
  defHttp.get<BillmanagerPayApplyC>({ url: adminPath + '/bankdirectlink/payapply/billmanagerPayApplyC/list', params });

export const billmanagerPayApplyCListData = (params?: BillmanagerPayApplyC | any) =>
  defHttp.post<Page<BillmanagerPayApplyC>>({ url: adminPath + '/bankdirectlink/payapply/billmanagerPayApplyC/listData', params });

export const billmanagerPayApplyCForm = (params?: BillmanagerPayApplyC | any) =>
  defHttp.get<BillmanagerPayApplyC>({ url: adminPath + '/bankdirectlink/payapply/billmanagerPayApplyC/form', params });

export const billmanagerPayApplyCSave = (params?: any, data?: BillmanagerPayApplyC | any) =>
  defHttp.postJson<BillmanagerPayApplyC>({ url: adminPath + '/bankdirectlink/payapply/billmanagerPayApplyC/save', params, data });

export const billmanagerPayApplyCImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/bankdirectlink/payapply/billmanagerPayApplyC/importData',
      onUploadProgress,
    },
    params,
  );

export const billmanagerPayApplyCDelete = (params?: BillmanagerPayApplyC | any) =>
  defHttp.get<BillmanagerPayApplyC>({ url: adminPath + '/bankdirectlink/payapply/billmanagerPayApplyC/delete', params });
