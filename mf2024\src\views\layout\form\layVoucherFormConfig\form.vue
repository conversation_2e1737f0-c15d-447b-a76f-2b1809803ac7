<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'layout:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherFormConfigForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { LayVoucherFormConfig, layVoucherFormConfigSave, layVoucherFormConfigForm } from '../../../../api/layout/form/layVoucherFormConfig';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('layout.layVoucherFormConfig');
  const { showMessage } = useMessage();
  const record = ref<LayVoucherFormConfig>({} as LayVoucherFormConfig);
  const getTitle = computed(() => ({
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增表单全局配置') : t('编辑表单全局配置'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('布局标志'),
      field: 'viewCode',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
      required: true,
    },
    {
      label: t('是否流程表单'),
      field: 'isBpmForm',
      component: 'RadioGroup',
      componentProps: {
        dictType: 'sys_yes_no',
      },
    },
    {
      label: t('流程表单Key'),
      field: 'bpmFormKey',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('表单布局'),
      field: 'formColNum',
      component: 'Select',
      componentProps: {
        dictType: 'lay_form_col',
        allowClear: true,
      },
    },
    {
      label: t('表单标题'),
      field: 'formTitle',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await layVoucherFormConfigForm(data);
    record.value = (res.layVoucherFormConfig || {}) as LayVoucherFormConfig;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await layVoucherFormConfigSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
