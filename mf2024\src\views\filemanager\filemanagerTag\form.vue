<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'filemanager:filemanagerTag:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="50%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsFilemanagerFilemanagerTagForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import {
    FilemanagerTag,
    filemanagerTagSave,
    filemanagerTagForm,
  } from '/@/api/filemanager/filemanagerTag';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('filemanager.filemanagerTag');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<FilemanagerTag>({} as FilemanagerTag);
  const getTitle = computed(() => ({
    icon: meta.icon || 'ant-design:tags-outlined',
    value: record.value.isNewRecord ? t('新增标签') : t('编辑标签'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('标签名称'),
      field: 'tagName',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('标签颜色'),
      field: 'tagColor',
      component: 'Input',
      componentProps: {
        maxlength: 50,
      },
    },
    {
      label: t('标签排序'),
      field: 'tagSort',
      component: 'InputNumber',
      defaultValue: '30',
      componentProps: {
        maxlength: 8,
      },
      required: true,
    },
    {
      label: t('系统内置'),
      field: 'isSys',
      component: 'RadioGroup',
      componentProps: {
        dictType: 'sys_yes_no',
      },
    },
    {
      label: t('备注信息'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 500,
      },
      colProps: { lg: 24, md: 24 },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 16, md: 16 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await filemanagerTagForm(data);
    record.value = (res.filemanagerTag || {}) as FilemanagerTag;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        tagId: record.value.tagId,
      };
      // console.log('submit', params, data, record);
      const res = await filemanagerTagSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
