import { useI18n } from '/@/hooks/web/useI18n';
import { BasicColumn, BasicTableProps, FormProps } from '/@/components/Table';
import { poPodetailsListData } from '/@/api/wms/weighbridge/dh';

const { t } = useI18n('weighbridge.dh');

const modalProps = {
  title: t('采购明细选择'),
};

const searchForm: FormProps = {
  baseColProps: { lg: 6, md: 8 },
  labelWidth: 120,
  showResetButton:false,
  schemas: [
    {
      label: t('carType'),
      field: 'carType',
      component: 'Input',
      show: false,
    },
    {
      label: t('存货编码'),
      field: 'cinvCode',
      component: 'Input',
    },
    {
      label: t('存货名称'),
      field: 'cinvName',
      component: 'Input',
    },
    {
      label: t('采购订单号'),
      field: 'cpoid',
      component: 'Input',
    },
    {
      label: t('供应商'),
      field: 'cvenName',
      component: 'Input',
    },
    {
      label: t('合同号'),
      field: 'contractCode',
      component: 'Input',
    },
   
  ],
};

const tableColumns: BasicColumn[] = [
  {
    title: t('采购订单号'),
    dataIndex: 'cpoid',
    key: 'a.cpoid',
    sorter: true,
    width: 150,
    // slot: 'firstColumn',
  },
  {
    title: t('合同号'),
    dataIndex: 'contractCode',
    key: 'a.contractCode',
    sorter: true,
    width: 150,
  },
  {
    title: t('采购订单日期'),
    dataIndex: 'dpoDate',
    key: 'a.dpoDate',
    sorter: true,
    width: 150,
    customRender: ({ record }) => {
      return record.dpoDate ? record.dpoDate.substring(0, 10) : '';
    },
  },
  {
    title: t('供应商编码'),
    dataIndex: 'cvenCode',
    key: 'a.cvenCode',
    sorter: true,
    width: 150,
  },
  {
    title: t('供应商名称'),
    dataIndex: 'cvenName',
    key: 'a.cvenName',
    sorter: true,
    width: 150,
  },
  {
    title: t('存货编码'),
    dataIndex: 'cinvCode',
    key: 'a.cinvCode',
    sorter: true,
    width: 150,
  },
  {
    title: t('存货名称'),
    dataIndex: 'cinvName',
    key: 'a.cinvName',
    sorter: true,
    width: 150,
  },
  {
    title: t('规格型号'),
    dataIndex: 'cinvStd',
    key: 'a.cinvStd',
    sorter: true,
    width: 150,
  },
  {
    title: t('剩余数量'),
    dataIndex: 'qty',
    key: 'a.qty',
    sorter: true,
    width: 150,
  },
  // {
  //   title: t('剩余退货数量'),
  //   dataIndex: 'qty',
  //   key: 'a.qty',
  //   sorter: true,
  //   width: 150,
  // },
];

const tableProps: BasicTableProps = {
  api: poPodetailsListData,
  beforeFetch: (params) => {
    params['isAll'] = true;
    return params;
  },
  columns: tableColumns,
  formConfig: searchForm,
  rowKey: 'id',
};

export default {
  modalProps,
  tableProps,
  itemCode: 'id',
  itemName: 'cinvName',
  isShowCode: true,
};
