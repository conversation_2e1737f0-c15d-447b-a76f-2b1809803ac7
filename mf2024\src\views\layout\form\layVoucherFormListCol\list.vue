<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button
          type="primary"
          @click="
            handleForm({
              viewCode: props.colpop.code,
            })
          "
          v-auth="'layout:edit'"
        >
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.viewCode }}
        </a>
      </template>
    </BasicTable>
    <InputForm :viewCode="props.colpop.code" @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherFormListColList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { layVoucherFormListColDelete, layVoucherFormListColListData } from '../../../../api/layout/form/layVoucherFormListCol';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('layout.layVoucherFormListCol');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: t('表单栏目管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('布局标志'),
        field: 'viewCode',
        component: 'Input',
      },
      {
        label: t('显示名称'),
        field: 'formLabel',
        component: 'Input',
      },
      {
        label: t('字段名称'),
        field: 'formField',
        component: 'Input',
      },
      {
        label: t('组件类型'),
        field: 'formComponent',
        component: 'Select',
        componentProps: {
          dictType: 'lay_comp_type',
          allowClear: true,
        },
      },
      {
        label: t('默认值'),
        field: 'formDefaultValue',
        component: 'Input',
      },
      {
        label: t('检验规则'),
        field: 'formRules',
        component: 'Input',
      },
      {
        label: t('是否显示'),
        field: 'formShow',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否禁用'),
        field: 'formDynamicDisable',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('栅格比列'),
        field: 'formColProps',
        component: 'Input',
      },
      {
        label: t('插槽'),
        field: 'formSlot',
        component: 'Input',
      },
      {
        label: t('最大长度'),
        field: 'compMaxLength',
        component: 'Input',
      },
      {
        label: t('是否允许清除'),
        field: 'compAllowClear',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否启用多选'),
        field: 'compMode',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('参数字段'),
        field: 'compDictType',
        component: 'Input',
      },
      {
        label: t('日期格式'),
        field: 'compFormat',
        component: 'Input',
      },
      {
        label: t('是否显示时分秒'),
        field: 'compShowTime',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('接口名称'),
        field: 'compApi',
        component: 'Input',
      },
      {
        label: t('参数地址'),
        field: 'compParams',
        component: 'Input',
      },
      {
        label: t('是否可选父级'),
        field: 'compCanSelectParent',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('加载时间'),
        field: 'compLoadTime',
        component: 'Input',
      },
      {
        label: t('表单主键'),
        field: 'compBizKey',
        component: 'Input',
      },
      {
        label: t('关联业务类型'),
        field: 'compBizType',
        component: 'Input',
      },
      {
        label: t('上传文件类型'),
        field: 'compUploadType',
        component: 'Select',
        componentProps: {
          dictType: 'lay_upload_type',
          allowClear: true,
        },
      },
      {
        label: t('排序'),
        field: 'sort',
        component: 'Input',
      },
      {
        label: t('原始名称'),
        field: 'originName',
        component: 'Input',
      },
      {
        label: t('权限标识'),
        field: 'auth',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('顺序号'),
      dataIndex: 'sortNum',
      key: 'a.sort_num',
      sorter: true,
      width: 80,
      align: 'center',
    },
    {
      title: t('归属页签'),
      dataIndex: 'layVouchFormTab.tabName',
      // dataLabel: 'layVouchFormTab.tabName',
      key: 'a.tab_code',
      sorter: true,
      width: 130,
      align: 'center',
      // dictType: 'lay_comp_type',
    },
    {
      title: t('布局标志'),
      dataIndex: 'viewCode',
      key: 'a.view_code',
      sorter: true,
      width: 230,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('原始名称'),
      dataIndex: 'originName',
      key: 'a.origin_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('显示名称'),
      dataIndex: 'formLabel',
      key: 'a.form_label',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('字段名称'),
      dataIndex: 'formField',
      key: 'a.form_field',
      sorter: true,
      width: 130,
      align: 'left',
    },
    
    {
      title: t('组件类型'),
      dataIndex: 'formComponent',
      key: 'a.form_component',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'lay_comp_type',
    },
    {
      title: t('默认值'),
      dataIndex: 'formDefaultValue',
      key: 'a.form_default_value',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('检验规则'),
      dataIndex: 'formRules',
      key: 'a.form_rules',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('是否显示'),
      dataIndex: 'formShow',
      key: 'a.form_show',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否必填'),
      dataIndex: 'formIsRequired',
      key: 'a.form_is_required',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否禁用'),
      dataIndex: 'formDynamicDisable',
      key: 'a.form_dynamic_disable',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('栅格比列'),
      dataIndex: 'formColProps',
      key: 'a.form_col_props',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('插槽'),
      dataIndex: 'formSlot',
      key: 'a.form_slot',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('最大长度'),
      dataIndex: 'compMaxLength',
      key: 'a.comp_max_length',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('是否允许清除'),
      dataIndex: 'compAllowClear',
      key: 'a.comp_allow_clear',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否启用多选'),
      dataIndex: 'compMode',
      key: 'a.comp_mode',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('参数字段'),
      dataIndex: 'compDictType',
      key: 'a.comp_dict_type',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('日期格式'),
      dataIndex: 'compFormat',
      key: 'a.comp_format',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('是否显示时分秒'),
      dataIndex: 'compShowTime',
      key: 'a.comp_show_time',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('接口名称'),
      dataIndex: 'compApi',
      key: 'a.comp_api',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('参数地址'),
      dataIndex: 'compParams',
      key: 'a.comp_params',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('是否可选父级'),
      dataIndex: 'compCanSelectParent',
      key: 'a.comp_can_select_parent',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('加载时间'),
      dataIndex: 'compLoadTime',
      key: 'a.comp_load_time',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('表单主键'),
      dataIndex: 'compBizKey',
      key: 'a.comp_biz_key',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('关联业务类型'),
      dataIndex: 'compBizType',
      key: 'a.comp_biz_type',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('上传文件类型'),
      dataIndex: 'compUploadType',
      key: 'a.comp_upload_type',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'lay_upload_type',
    },
    {
      title: t('权限标识'),
      dataIndex: 'auth',
      key: 'a.auth',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑表单栏目设计'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'layout:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除表单栏目设计'),
        popConfirm: {
          title: t('是否确认删除表单栏目设计'),
          confirm: handleDelete.bind(this, { id: record.id }),
        },
        auth: 'layout:edit',
      },
    ],
  };

  const props = defineProps({
    colpop: { type: Object, default: {} },
  });

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm }] = useTable({
    api: layVoucherFormListColListData,
    beforeFetch: (params) => {
      params.viewCode = props.colpop.code;
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: false,
    canResize: true,
    showIndexColumn:false,
    
  });

  watch(
    () => props.colpop,
    () => {
      reload();
    },
    // { immediate: true },
  );

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const res = await layVoucherFormListColDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleSuccess() {
    reload();
  }
</script>
