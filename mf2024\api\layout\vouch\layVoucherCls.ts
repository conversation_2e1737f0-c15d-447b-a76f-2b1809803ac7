/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { TreeDataModel, TreeModel } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface LayVoucherCls extends TreeModel<LayVoucherCls> {
  typeCode?: string; // 类别编码
  typeName?: string; // 类别名称
}

export const layVoucherClsList = (params?: LayVoucherCls | any) =>
  defHttp.get<LayVoucherCls>({ url: adminPath + '/layout/layVoucherCls/list', params });

export const layVoucherClsListData = (params?: LayVoucherCls | any) =>
  defHttp.post<LayVoucherCls[]>({ url: adminPath + '/layout/layVoucherCls/listData', params });

export const layVoucherClsForm = (params?: LayVoucherCls | any) =>
  defHttp.get<LayVoucherCls>({ url: adminPath + '/layout/layVoucherCls/form', params });

export const layVoucherClsCreateNextNode = (params?: LayVoucherCls | any) =>
  defHttp.get<LayVoucherCls>({ url: adminPath + '/layout/layVoucherCls/createNextNode', params });

export const layVoucherClsSave = (params?: any, data?: LayVoucherCls | any) =>
  defHttp.postJson<LayVoucherCls>({ url: adminPath + '/layout/layVoucherCls/save', params, data });

export const layVoucherClsDelete = (params?: LayVoucherCls | any) =>
  defHttp.get<LayVoucherCls>({ url: adminPath + '/layout/layVoucherCls/delete', params });

export const layVoucherClsTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/layout/layVoucherCls/treeData', params });
