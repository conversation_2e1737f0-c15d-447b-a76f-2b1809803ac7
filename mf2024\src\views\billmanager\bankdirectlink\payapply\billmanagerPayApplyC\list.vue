<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="default" @click="handleExport()">
          <Icon icon="i-ant-design:download-outlined" /> {{ t('导出') }}
        </a-button>
        <a-button type="default" @click="handleImport()">
          <Icon icon="i-ant-design:upload-outlined" /> {{ t('导入') }}
        </a-button>
        <a-button type="primary" @click="handleForm({})" v-auth="'bankdirectlink:payapply:billmanagerPayApplyC:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.parentId }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
    <FormImport @register="registerImportModal" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsBankdirectlinkPayapplyBillmanagerPayApplyCList">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { billmanagerPayApplyCDelete, billmanagerPayApplyCListData } from '/@/api/billmanager/bankdirectlink/payapply/billmanagerPayApplyC';
  import { useDrawer } from '/@/components/Drawer';
  import { useModal } from '/@/components/Modal';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import FormImport from './formImport.vue';

  const { t } = useI18n('bankdirectlink.payapply.billmanagerPayApplyC');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('付款申请单子表管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('主表ID'),
        field: 'parentId',
        component: 'Input',
      },
      {
        label: t('款项类型'),
        field: 'payType',
        component: 'Input',
      },
      {
        label: t('供应商'),
        field: 'venCode',
        component: 'Input',
      },
      {
        label: t('币种'),
        field: 'cexchName',
        component: 'Input',
      },
      {
        label: t('原币金额'),
        field: 'orgAmount',
        component: 'Input',
      },
      {
        label: t('部门'),
        field: 'deptCode',
        component: 'Input',
      },
      {
        label: t('备注'),
        field: 'remarks',
        component: 'Input',
      },
      {
        label: t('来源id'),
        field: 'sourceId',
        component: 'Input',
      },
      {
        label: t('合同号'),
        field: 'contractId',
        component: 'Input',
      },
      {
        label: t('资金用途'),
        field: 'useOfFunds',
        component: 'Input',
      },
      {
        label: t('U8生单状态'),
        field: 'u8Status',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('主表ID'),
      dataIndex: 'parentId',
      key: 'a.parent_id',
      sorter: true,
      width: 230,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('款项类型'),
      dataIndex: 'payType',
      key: 'a.pay_type',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('供应商'),
      dataIndex: 'venCode',
      key: 'a.ven_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('币种'),
      dataIndex: 'cexchName',
      key: 'a.cexch_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('原币金额'),
      dataIndex: 'orgAmount',
      key: 'a.org_amount',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('部门'),
      dataIndex: 'deptCode',
      key: 'a.dept_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('备注'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('来源id'),
      dataIndex: 'sourceId',
      key: 'a.source_id',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('合同号'),
      dataIndex: 'contractId',
      key: 'a.contract_id',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('资金用途'),
      dataIndex: 'useOfFunds',
      key: 'a.use_of_funds',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('U8生单状态'),
      dataIndex: 'u8Status',
      key: 'a.u8_status',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑付款申请单子表'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'bankdirectlink:payapply:billmanagerPayApplyC:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除付款申请单子表'),
        popConfirm: {
          title: t('是否确认删除付款申请单子表'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'bankdirectlink:payapply:billmanagerPayApplyC:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm }] = useTable({
    api: billmanagerPayApplyCListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleExport() {
    const { ctxAdminPath } = useGlobSetting();
    downloadByUrl({
      url: ctxAdminPath + '/bankdirectlink/payapply/billmanagerPayApplyC/exportData',
      params: getForm().getFieldsValue(),
    });
  }

  const [registerImportModal, { openModal: importModal }] = useModal();

  function handleImport() {
    importModal(true, {});
  }

  async function handleDelete(record: Recordable) {
    const params = { id: record.id };
    const res = await billmanagerPayApplyCDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
