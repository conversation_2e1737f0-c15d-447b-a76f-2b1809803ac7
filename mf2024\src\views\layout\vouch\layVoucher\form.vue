<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'layout:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import {
    LayVoucher,
    layVoucherSave,
    layVoucherForm,
    checkBeanID,
  } from '../../../../api/layout/vouch/layVoucher';
  import { layVoucherClsTreeData } from '/@/api/layout/vouch/layVoucherCls';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('layout.layVoucher');
  const { showMessage } = useMessage();
  const record = ref<LayVoucher>({} as LayVoucher);
  const getTitle = computed(() => ({
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增基础单据') : t('编辑基础单据'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('单据标志'),
      field: 'code',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      rules: [
        { required: true },
        { pattern: /^[a-zA-Z0-9_]*$/, message: t('请输入字母数字下划线') },
      ],
    },
    {
      label: t('单据类别'),
      field: 'typeCode',
      fieldLabel: 'voucherCls.typeName',
      component: 'TreeSelect',
      componentProps: {
        api: layVoucherClsTreeData,
        allowClear: true,
      },
      required: true,
    },
    {
      label: t('单据名称'),
      field: 'name',
      helpMessage: '精简描述如：存货档案',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
    },
    {
      label: t('实体类路径'),
      field: 'entityPath',
      component: 'Input',
      helpMessage: '例如: com.jeesite.modules.sys.entity.EmpUser',
      componentProps: {
        maxlength: 200,
      },
      required: true,
    },
    {
      label: t('bean ID'),
      field: 'beanId',
      component: 'Input',
      helpMessage: 'service名称',
      componentProps: {
        maxlength: 200,
      },
      rules: [
        // 自定义验证，举例：远程验证beanName是否存在
        {
          trigger: 'blur',
          validator(_rule, value) {
            return new Promise((resolve, reject) => {
              if (!value || value === '') return resolve();
              console.log(value);
              checkBeanID(value)
                .then((res) => (res ? resolve() : reject(t(res.message))))
                .catch((err) => reject(err.message || t('验证失败')));
            });
          },
        },
      ],
    },
    {
      label: t('单据描述'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 500,
      },
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('起始前缀类型'),
      field: 'stPrefixType',
      component: 'Select',
      componentProps: {
        dictType: 'st_prefix_type',
        allowClear: true,
      },
    },
    {
      label: t('中间前缀'),
      field: 'mdPrefix',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('启用日期'),
      field: 'buseDate',
      component: 'RadioGroup',
      componentProps: {
        dictType: 'sys_yes_no',
      },
      required: true,
    },
    {
      label: t('日期格式'),
      field: 'dateFmt',
      helpMessage: '日期格式如：yyyyMMdd、yyyyMM、yyMMdd',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('流水位数'),
      field: 'inum',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ required: true }, { pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('数据库表名'),
      field: 'tableName',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      rules: [
        { required: true },
        { pattern: /^[a-zA-Z0-9_]*$/, message: t('请输入字母数字下划线') },
      ],
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await layVoucherForm(data);
    record.value = (res.layVoucher || {}) as LayVoucher;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    updateSchema([
      {
        field: 'code',
        componentProps: {
          disabled: !record.value.isNewRecord,
        },
      },
    ]);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        code: record.value.code,
      };
      // console.log('submit', params, data, record);
      const res = await layVoucherSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
