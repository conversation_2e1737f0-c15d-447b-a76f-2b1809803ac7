/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface BillManagerPayApplyC1 extends BasicModel<BillManagerPayApplyC1> {
  parentId?: string; // 明细ID
  subject?: string; // 科目
  footType?: string; // 支付类型
  orgAmount?: number; // 原币金额
  sumAmount?: number; // 拆分金额
}

export const billManagerPayApplyC1List = (params?: BillManagerPayApplyC1 | any) =>
  defHttp.get<BillManagerPayApplyC1>({ url: adminPath + '/bankdirectlink/payapply/billManagerPayApplyC1/list', params });

export const billManagerPayApplyC1ListData = (params?: BillManagerPayApplyC1 | any) =>
  defHttp.post<Page<BillManagerPayApplyC1>>({ url: adminPath + '/bankdirectlink/payapply/billManagerPayApplyC1/listData', params });

export const billManagerPayApplyC1Form = (params?: BillManagerPayApplyC1 | any) =>
  defHttp.get<BillManagerPayApplyC1>({ url: adminPath + '/bankdirectlink/payapply/billManagerPayApplyC1/form', params });

export const billManagerPayApplyC1Save = (params?: any, data?: BillManagerPayApplyC1 | any) =>
  defHttp.postJson<BillManagerPayApplyC1>({ url: adminPath + '/bankdirectlink/payapply/billManagerPayApplyC1/save', params, data });

export const billManagerPayApplyC1Delete = (params?: BillManagerPayApplyC1 | any) =>
  defHttp.get<BillManagerPayApplyC1>({ url: adminPath + '/bankdirectlink/payapply/billManagerPayApplyC1/delete', params });

export const billManagerPayApplyC1BillSelection = (params?: any, data?: BillManagerPayApplyC1 | any) =>
  defHttp.postJson<BillManagerPayApplyC1>({ url: adminPath + '/bankdirectlink/payapply/billManagerPayApplyC1/billSelection', params, data });
