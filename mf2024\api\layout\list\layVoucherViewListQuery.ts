/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface LayVoucherViewListQuery extends BasicModel<LayVoucherViewListQuery> {
  code?: string; // 查询标志
  name?: string; // 查询名称
  viewCode?: string; // 布局标志
  vouchCode?: string; // 基础单据
}

export const layVoucherViewListQueryList = (params?: LayVoucherViewListQuery | any) =>
  defHttp.get<LayVoucherViewListQuery>({ url: adminPath + '/layout/listQuery/list', params });

export const layVoucherViewListQueryListData = (params?: LayVoucherViewListQuery | any) =>
  defHttp.post<Page<LayVoucherViewListQuery>>({ url: adminPath + '/layout/listQuery/listData', params });

export const layVoucherViewListQueryForm = (params?: LayVoucherViewListQuery | any) =>
  defHttp.get<LayVoucherViewListQuery>({ url: adminPath + '/layout/listQuery/form', params });

export const layVoucherViewListQuerySave = (params?: any, data?: LayVoucherViewListQuery | any) =>
  defHttp.postJson<LayVoucherViewListQuery>({ url: adminPath + '/layout/listQuery/save', params, data });

export const layVoucherViewListQueryDisable = (params?: LayVoucherViewListQuery | any) =>
  defHttp.get<LayVoucherViewListQuery>({ url: adminPath + '/layout/listQuery/disable', params });

export const layVoucherViewListQueryEnable = (params?: LayVoucherViewListQuery | any) =>
  defHttp.get<LayVoucherViewListQuery>({ url: adminPath + '/layout/listQuery/enable', params });

export const layVoucherViewListQueryDelete = (params?: LayVoucherViewListQuery | any) =>
  defHttp.get<LayVoucherViewListQuery>({ url: adminPath + '/layout/listQuery/delete', params });
