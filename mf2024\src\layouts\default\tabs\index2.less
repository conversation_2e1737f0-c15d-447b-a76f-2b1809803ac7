/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
@prefix-cls-2: ~'jeesite-multiple-tabs-2';
@multiple-height: 32px; // TABS_HEIGHT

html[data-theme='dark'] {
  .@{prefix-cls-2} {
    .ant-tabs.ant-tabs-card {
      background: #151515;

      .ant-tabs-nav {
        &::before {
          border-bottom: 0;
        }

        .ant-tabs-tab {
          color: #aaa !important;
          background: #151515;

          svg {
            fill: #aaa !important;
          }

          &:hover,
          .ant-tabs-tab-btn:hover {
            color: #ddd !important;
          }
        }

        .ant-tabs-tab-active {
          background: fade(#2a50ed, 85) !important;

          svg {
            fill: #fff !important;
          }

          .ant-tabs-tab-btn,
          .ant-tabs-tab-btn:hover {
            color: #fff !important;
          }
        }
      }
    }
  }
}

// html[data-theme='light'] {
//   .@{prefix-cls-2} {
//     .ant-tabs-tab:not(.ant-tabs-tab-active) {
//       border: 1px solid @border-color-base !important;
//       // box-shadow: 0 0 3px @border-color-base;
//     }
//   }
// }

.@{prefix-cls-2} {
  z-index: 10;
  // height: @multiple-height + 2;
  // line-height: @multiple-height + 2;
  background-color: @component-background;
  // border-bottom: 1px solid @header-light-bottom-border-color;

  .ant-tabs.ant-tabs-card {
    .ant-tabs-nav {
      height: @multiple-height;
      background-color: @component-background;
      margin: 0;
      border: 0;
      box-shadow: none;

      .ant-tabs-tab {
        height: calc(@multiple-height - 8px);
        line-height: calc(@multiple-height - 8px);
        color: @text-color-base;
        background-color: @component-background;
        transition: none;
        border-radius: 4px !important;
        margin: 4px 0;
        padding-left: 6px;
        padding-right: 6px;
        border: 0 !important;

        .ant-tabs-tab-btn {
          transition: none;
        }

        &:hover,
        .ant-tabs-tab-btn:hover {
          color: @text-color-base;
        }

        .anticon {
          opacity: 0.8;
          font-size: 16px;
          text-align: center;
          margin-right: 5px;

          svg {
            fill: @text-color-base;
          }
        }

        .ant-tabs-tab-remove {
          width: 15px;
          height: 21px;
          color: inherit;
          transition: none;
          padding: 0;

          .anticon {
            svg {
              width: 0.6em;
            }

            &:hover svg {
              width: 0.7em;
            }
          }
        }
      }

      .ant-tabs-tab:not(.ant-tabs-tab-active) {
        &:hover {
          color: @primary-color;
        }
      }

      .ant-tabs-tab-active {
        position: relative;
        // padding-left: 18px;
        // color: @white !important;
        color: @primary-color !important;
        background: fade(@primary-color, 10);
        // border-color: fade(@primary-color, 25);
        // height: calc(@multiple-height - 2px);
        border: 0;
        transition: none;
        text-shadow: none;

        // span {
        //   color: @primary-color !important;
        // }
        .ant-tabs-tab-btn {
          color: @primary-color !important;
          text-shadow: none;
        }

        .ant-tabs-tab-remove {
          opacity: 1;

          svg {
            fill: @primary-color;
          }
        }
      }
    }

    .ant-tabs-nav > div:nth-child(1) {
      margin-left: 15px;

      .ant-tabs-tab {
        margin-right: 3px !important;

        &:nth-last-child(2) {
          margin-right: 20px !important;
        }
      }
    }
  }

  .ant-tabs-extra-content {
    // margin-top: 2px;
    line-height: @multiple-height !important;
  }

  .ant-dropdown-trigger {
    display: inline-flex;
  }

  &.jeesite-multiple-tabs-hide-close {
    .ant-tabs-tab-remove {
      opacity: 0 !important;
    }
  }

  .jeesite-multiple-tabs-content {
    &__info {
      display: inline-block;
      width: 100%;
      cursor: pointer;
      user-select: none;
    }

    &__extra-redo {
      span[role='img'] {
        transform: rotate(0deg);
      }
    }

    &__extra-quick,
    &__extra-redo,
    &__extra-fold {
      display: inline-block;
      width: 36px;
      height: @multiple-height;
      line-height: @multiple-height;
      color: @text-color-secondary;
      text-align: center;
      cursor: pointer;
      border-left: 1px solid @header-light-bottom-border-color;

      &:hover {
        color: @text-color-base;
      }

      span[role='img'] {
        transform: rotate(90deg);
      }
    }
  }

  .ant-tabs .ant-tabs-nav {
    .ant-tabs-nav-more {
      padding-top: 5px;
    }
  }
}
