<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable" @row-click="handleTestDataChildRowClick">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
        <span>
          （{{ props.colpop.code + '-' + props.colpop.viewCode + '-' + props.colpop.vouchName }}）
        </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="add()" v-auth="'layout:edit'">
          <!-- @click="handleForm({queryCode: props.colpop.code})" -->
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.queryCode }}
        </a>
      </template>
      <template #vouchCode="{ record }">
        {{ record.vouchName }}
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
    <!-- <BasicDrawer
      v-bind="$attrs"
      :showFooter="true"
      :okAuth="'layout:edit'"
      @register="registerDrawer"
      @ok="handleSubmit"
      width="60%"
    >
      <template #title>
        <Icon :icon="getTitle.icon" class="pr-1 m-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <BasicForm @register="registerForm" />
    </BasicDrawer> -->
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherViewListQueryColList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import {
    layVoucherViewListQueryColDelete,
    layVoucherViewListQueryColListData,
  } from '/@/api/layout/list/layVoucherViewListQueryCol';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  // import { layVoucherClsTreeData } from '/@/api/layout/layVoucherCls';
  import { layVoucherViewListQueryColSave } from '/@/api/layout/list/layVoucherViewListQueryCol';

  const { t } = useI18n('layout.layVoucherViewListQueryCol');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: 'ant-design:book-outlined',
    value: t('查询栏目管理'),
  };
  let parameter = ref(router.currentRoute.value.query);
  // console.log(parameter.value);
  const props = defineProps({
    colpop: { type: Object, default: {} },
  });
  // console.log(props.colpop);

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('查询标志'),
        field: 'queryCode',
        component: 'Input',
      },
      {
        label: t('基础单据'),
        field: 'vouchCode',
        component: 'Input',
      },
      {
        label: t('标签名称'),
        field: 'formLabel',
        component: 'Input',
      },
      {
        label: t('字段名称'),
        field: 'formField',
        component: 'Input',
      },
      {
        label: t('组件类型'),
        field: 'formComponent',
        component: 'Select',
        componentProps: {
          dictType: 'lay_comp_type',
          allowClear: true,
        },
      },
      {
        label: t('默认值'),
        field: 'formDefaultValue',
        component: 'Input',
      },
      {
        label: t('是否显示'),
        field: 'formIsShow',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否禁用'),
        field: 'formDynamicDisable',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否允许删除'),
        field: 'compAllowClear',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否启用多选'),
        field: 'compMode',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('参数字典'),
        field: 'compDictType',
        component: 'Input',
      },
      {
        label: t('日期格式'),
        field: 'compFormat',
        component: 'Input',
      },
      {
        label: t('是否显示时分秒'),
        field: 'compShowTime',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('方法名称'),
        field: 'compApi',
        component: 'Input',
      },
      {
        label: t('参数地址'),
        field: 'compParams',
        component: 'Input',
      },
      {
        label: t('事件函数'),
        field: 'compMethods',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('顺序号'),
      dataIndex: 'sortNum',
      key: 'a.sort_num',
      sorter: true,
      width: 80,
      align: 'center',
      editRow: true,
      editComponent: 'InputNumber',
      editRule: true,
    },
    {
      title: t('查询标志'),
      dataIndex: 'queryCode',
      key: 'a.query_code',
      sorter: true,
      width: 130,
      align: 'center',
      ifShow: false,
      // slot: 'firstColumn',
    },
    {
      title: t('基础单据'),
      dataIndex: 'viewCode',
      dataLabel: 'layVouch.name',
      key: 'a.view_code',
      sorter: true,
      width: 130,
      align: 'center',
      ifShow: false,
      //slot: 'vouchCode',
      // editRow: true,
      // editComponent: 'TreeSelect',
      // editComponentProps: {
      //   maxlength: 64,
      //   api: layVoucherClsTreeData,
      //   canSelectParent: false,
      // },
    },
    {
      title: t('显示名称'),
      dataIndex: 'formLabel',
      key: 'a.form_label',
      sorter: true,
      width: 130,
      align: 'center',
      editRow: true,
      editComponent: 'Input',
      editRule: true,
    },
    {
      title: t('字段名称'),
      dataIndex: 'formField',
      key: 'a.form_filed',
      sorter: true,
      width: 130,
      align: 'center',
      editRow: true,
      editComponent: 'Input',
      editRule: true,
    },
    {
      title: t('数据库字段名'),
      dataIndex: 'cdbField',
      key: 'a.cdb_field',
      sorter: true,
      width: 130,
      align: 'center',
      editRow: true,
      editComponent: 'Input',
      editRule: true,
    },
    {
      title: t('数据库别名'),
      dataIndex: 'cdbName',
      key: 'a.cdb_name',
      sorter: true,
      width: 130,
      align: 'center',
      editRow: true,
      editComponent: 'Input',
      editRule: true,
    },
    {
      title: t('匹配方式'),
      dataIndex: 'queryType',
      key: 'a.query_type',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'query_type',
      editRow: true,
      editComponent: 'Select',
      editDefaultValue: 'EQ',
      editComponentProps: {
        dictType: 'query_type',
        // canSelectParent: false,
        // allowClear: true,
      },
      editRule: true,
    },
    {
      title: t('组件类型'),
      dataIndex: 'formComponent',
      key: 'a.form_component',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'lay_comp_type',
      editRow: true,
      editComponent: 'Select',
      editDefaultValue: 'Input',
      editComponentProps: {
        dictType: 'lay_comp_type',
        // canSelectParent: false,
        // allowClear: true,
      },
      editRule: true,
    },

    {
      title: t('树形选择label'), //一般树形选择时需要填写
      dataIndex: 'formFieldLabel',
      key: 'a.form_label',
      width: 130,
      align: 'center',
      editRow: true,
      editComponent: 'Input',
    },
    {
      title: t('默认值'),
      dataIndex: 'formDefaultValue',
      key: 'a.form_default_value',
      sorter: true,
      width: 130,
      align: 'center',
      editRow: true,
      editComponent: 'Input',
      // editRule: true,
    },
    {
      title: t('显示'),
      dataIndex: 'formShow',
      key: 'a.form_show',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
      editRow: true,
      editComponent: 'Select',
      editDefaultValue: '1',
      editComponentProps: {
        dictType: 'sys_yes_no',
        // allowClear: true,
      },
      editRule: true,
    },
    {
      title: t('禁用'),
      dataIndex: 'formDynamicDisable',
      key: 'a.form_dynamic_disable',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
      editRow: true,
      editComponent: 'Select',
      editDefaultValue: '0',
      editComponentProps: {
        dictType: 'sys_yes_no',
        // allowClear: true,
      },
      editRule: true,
    },
    {
      title: t('允许删除'),
      dataIndex: 'compAllowClear',
      key: 'a.comp_allow_clear',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
      editRow: true,
      editComponent: 'Select',
      editDefaultValue: '1',
      editComponentProps: {
        dictType: 'sys_yes_no',
        // allowClear: true,
      },
      editRule: true,
    },
    {
      title: t('启用多选'),
      dataIndex: 'compMode',
      key: 'a.comp_mode',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
      editRow: true,
      editComponent: 'Select',
      editDefaultValue: '0',
      editComponentProps: {
        dictType: 'sys_yes_no',
        // allowClear: true,
      },
    },
    {
      title: t('参数字典'),
      dataIndex: 'compDictType',
      key: 'a.comp_dict_type',
      sorter: true,
      width: 130,
      align: 'center',
      editRow: true,
      editComponent: 'Input',
      // editRule: true,
    },
    {
      title: t('日期格式'),
      dataIndex: 'compFormat',
      key: 'a.comp_format',
      sorter: true,
      width: 130,
      align: 'center',
      editRow: true,
      editComponent: 'Input',
      // editRule: true,
    },
    {
      title: t('是否显示时分秒'),
      dataIndex: 'compShowTime',
      key: 'a.comp_show_time',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
      editRow: true,
      editComponent: 'Select',
      editComponentProps: {
        dictType: 'sys_yes_no',
        allowClear: true,
      },
    },
    {
      title: t('方法名称'),
      dataIndex: 'compApi',
      key: 'a.comp_api',
      sorter: true,
      width: 130,
      align: 'center',
      editRow: true,
      editComponent: 'Input',
      // editRule: true,
    },
    {
      title: t('参数地址'),
      dataIndex: 'compParams',
      key: 'a.comp_params',
      sorter: true,
      width: 130,
      align: 'center',
      editRow: true,
      editComponent: 'Input',
      // editRule: true,
    },
    {
      title: t('事件函数'),
      dataIndex: 'compMethods',
      key: 'a.comp_methods',
      sorter: true,
      width: 130,
      align: 'center',
      editRow: true,
      editComponent: 'Input',
      // editRule: true,
    },
    {
      title: t('列表选择类型'),
      dataIndex: 'formSelectType',
      key: 'a.form_selectType', // sorter: true,
      width: 130,
      align: 'center',
      editRow: true,
      editComponent: 'Input', // editRule: true,
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      // {
      //   icon: 'clarity:note-edit-line',
      //   title: t('编辑查询栏目'),
      //   onClick: handleForm.bind(this, { id: record.id }),
      //   auth: 'layout:edit',
      // },
      {
        icon: 'ant-design:save-outlined',
        title: t('保存'),
        onClick: handleSave.bind(this, record),
        auth: 'layout:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除查询栏目'),
        popConfirm: {
          title: t('是否确认删除查询栏目'),
          confirm: handleDelete.bind(this, { id: record.id }),
        },
        auth: 'layout:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [
    registerTable,
    { reload, getForm, insertTableDataRecord, getDataSource, getColumns, deleteTableDataRecord },
  ] = useTable({
    api: layVoucherViewListQueryColListData,
    beforeFetch: (params) => {
      params.queryCode = props.colpop.code;
      params.viewCode = props.colpop.viewCode;
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: false,
    showIndexColumn: false,
    canResize: true,
    resizeHeightOffset: 60,
    pagination: false,
  });
  watch(
    () => props.colpop,
    () => {
      reload();
    },
    // { immediate: true },
  );
  // 表格新增一行
  function add() {
    let sortNum = (getDataSource().length + 1) * 10
    insertTableDataRecord({
      // id: new Date().getTime(),
      isNewRecord: true,
      editable: true,
      queryCode: props.colpop.code,
      viewCode: props.colpop.viewCode,
      layVouch: {
        name: props.colpop.vouchName,
      },
      queryType: 'EQ',
      sortNum,
      cdbName:'a',
      formComponent: 'Input',
      formShow: '1',
      formDynamicDisable: '0',
      compAllowClear: '1',
      compMode: '0',
    });

    // openDrawer(true, {});
  }
  function handleTestDataChildRowClick(record: Recordable) {
    record.onEdit?.(true, false);
  }

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    console.log(record.isNewRecord);

    deleteTableDataRecord(record);

    const res = await layVoucherViewListQueryColDelete(record);
    showMessage(res.message);
    // handleSuccess();
  }

  // 保存表格修改
  async function handleSave(record) {
    // let data = getColumns();
    console.log(getColumns());
    console.log(getDataSource());

    // console.log(record);
    // console.log(record.editValueRefs);
    // const res = await layVoucherViewListQueryColForm({ queryCode: record.queryCode });
    // console.log(res);
    // // res.layVoucherViewListQueryCol.__t = new Date().getTime();

    const params: any = {
      isNewRecord: record.isNewRecord ? true : false,
      id: record.id,
    };
    record.editValueRefs.queryCode = props.colpop.code;
    record.editValueRefs.viewCode = props.colpop.viewCode;
    // record.editValueRefs.__t = new Date().getTime();
    const res1 = await layVoucherViewListQueryColSave(params, record.editValueRefs);
    console.log(res1);
    reload();
  }
  defineExpose({
    handleSave1,
  });

  async function handleSave1() {
    // let data = getColumns();
    // console.log(getColumns());
    // console.log(getDataSource());

    // console.log(record);
    // console.log(record.editValueRefs);
    // const res = await layVoucherViewListQueryColForm({ queryCode: record.queryCode });
    // console.log(res);
    // // res.layVoucherViewListQueryCol.__t = new Date().getTime();
    getDataSource().forEach(async (item) => {
      console.log(item);
      const params: any = {
        isNewRecord: item.isNewRecord ? true : false,
        id: item.id,
      };
      item.editValueRefs.queryCode = props.colpop.code;
      item.editValueRefs.viewCode = props.colpop.viewCode;
      const res1 = await layVoucherViewListQueryColSave(params, item.editValueRefs);
      console.log(res1);
    });
    reload();
  }

  function handleSuccess() {
    reload();
  }
</script>
