<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'bankdirectlink:contract:viewContract:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ htcode: record.htcode })">
          {{ record.htcode }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsBankdirectlinkContractViewContractList">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { viewContractDelete, viewContractListData } from '/@/api/billmanager/bankdirectlink/contract/viewContract';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('bankdirectlink.contract.viewContract');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('合同管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('合同编码'),
        field: 'htcode',
        component: 'Input',
      },
      {
        label: t('合同名称'),
        field: 'htname',
        component: 'Input',
      },
      {
        label: t('供应商编码'),
        field: 'vencode',
        component: 'Input',
      },
      {
        label: t('供应商名称'),
        field: 'venname',
        component: 'Input',
      },
      {
        label: t('预算金额'),
        field: 'amount',
        component: 'Input',
      },
      {
        label: t('已使用金额'),
        field: 'useAmount',
        component: 'Input',
      },
      {
        label: t('使用使用金额'),
        field: 'syUseMount',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('合同编码'),
      dataIndex: 'htcode',
      key: 'a.htcode',
      sorter: true,
      width: 230,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('合同名称'),
      dataIndex: 'htname',
      key: 'a.htname',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('供应商编码'),
      dataIndex: 'vencode',
      key: 'a.vencode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('供应商名称'),
      dataIndex: 'venname',
      key: 'a.venname',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('预算金额'),
      dataIndex: 'amount',
      key: 'a.amount',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('已使用金额'),
      dataIndex: 'useAmount',
      key: 'a.use_amount',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('使用使用金额'),
      dataIndex: 'syUseMount',
      key: 'a.sy_use_mount',
      sorter: true,
      width: 130,
      align: 'right',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑合同'),
        onClick: handleForm.bind(this, { htcode: record.htcode }),
        auth: 'bankdirectlink:contract:viewContract:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除合同'),
        popConfirm: {
          title: t('是否确认删除合同'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'bankdirectlink:contract:viewContract:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload }] = useTable({
    api: viewContractListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { htcode: record.htcode };
    const res = await viewContractDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
