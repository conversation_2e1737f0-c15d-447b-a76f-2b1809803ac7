/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../../model/baseModel';
import { UploadApiResult } from '../../../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface BillmanagerPayApplyH extends BasicModel<BillmanagerPayApplyH> {
  djno?: string; // 单据编号
  ddate?: string; // 申请日期
  deptCode?: string; // 申请部门
  createByName?: string; // 创建人名称
  venCode?: string; // 供应商
  billmanagerPayApplyCList?: any[]; // 子表列表
}

export const billmanagerPayApplyHList = (params?: BillmanagerPayApplyH | any) =>
  defHttp.get<BillmanagerPayApplyH>({ url: adminPath + '/bankdirectlink/payapply/billmanagerPayApplyH/list', params });

export const billmanagerPayApplyHListData = (params?: BillmanagerPayApplyH | any) =>
  defHttp.post<Page<BillmanagerPayApplyH>>({ url: adminPath + '/bankdirectlink/payapply/billmanagerPayApplyH/listData', params });

export const billmanagerPayApplyHForm = (params?: BillmanagerPayApplyH | any) =>
  defHttp.get<BillmanagerPayApplyH>({ url: adminPath + '/bankdirectlink/payapply/billmanagerPayApplyH/form', params });

export const billmanagerPayApplyHSave = (params?: any, data?: BillmanagerPayApplyH | any) =>
  defHttp.postJson<BillmanagerPayApplyH>({ url: adminPath + '/bankdirectlink/payapply/billmanagerPayApplyH/save', params, data });

export const billmanagerPayApplyHImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/bankdirectlink/payapply/billmanagerPayApplyH/importData',
      onUploadProgress,
    },
    params,
  );

export const billmanagerPayApplyHDelete = (params?: BillmanagerPayApplyH | any) =>
  defHttp.get<BillmanagerPayApplyH>({ url: adminPath + '/bankdirectlink/payapply/billmanagerPayApplyH/delete', params });

export const billmanagerPayApplyHPayConfirmation = (params?: any, data?: BillmanagerPayApplyH | any) =>
  defHttp.postJson<BillmanagerPayApplyH>({ url: adminPath + '/bankdirectlink/payapply/billmanagerPayApplyH/payConfirmation', params, data });
