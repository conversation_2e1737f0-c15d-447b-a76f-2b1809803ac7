/**
  * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
  * No deletion without permission, or be held responsible to law.
  * <AUTHOR>
  */
@prefix-cls-3: ~'jeesite-multiple-tabs-3';
@multiple-height-large: 40px; // TABS_HEIGHT_LARGE

.@{prefix-cls-3} {
  z-index: 10;
  height: @multiple-height-large + 2;
  line-height: @multiple-height-large + 2;

  .ant-tabs.ant-tabs-card {
    .ant-tabs-nav {
      height: @multiple-height-large;
      background-color: @content-bg;
      margin: 0;
      border: 0;
      box-shadow: none;

      &::before {
        border-bottom: 0;
      }

      .ant-tabs-tab {
        height: calc(@multiple-height-large - 15px);
        line-height: calc(@multiple-height-large - 13px);
        color: @text-color-base;
        background-color: @component-background;
        transition: none;
        border-radius: 4px !important;
        margin: 12px 0 0;
        padding-left: 10px;
        padding-right: 11px;
        border: 0 !important;

        .ant-tabs-tab-btn {
          transition: none;
        }

        &:hover,
        .ant-tabs-tab-btn:hover {
          color: @text-color-base;
        }

        .anticon {
          opacity: 0.8;
          font-size: 16px;
          text-align: center;
          margin-right: 5px;

          svg {
            fill: @text-color-base;
          }
        }

        .ant-tabs-tab-remove {
          width: 12px;
          height: 23px;
          color: inherit;
          transition: none;
          padding: 0;

          .anticon {
            svg {
              width: 0.6em;
            }

            &:hover svg {
              width: 0.7em;
            }
          }
        }
      }

      .ant-tabs-tab:not(.ant-tabs-tab-active) {
        &:hover {
          color: @primary-color;
        }
      }

      .ant-tabs-tab-active {
        position: relative;
        // padding-left: 18px;
        color: @white !important;
        // color: @primary-color !important;
        background: fade(@primary-color, 90);
        // border-color: fade(@primary-color, 25);
        // height: calc(@multiple-height-large - 2px);
        border: 0;
        transition: none;
        text-shadow: none;

        // span {
        //   color: @white !important;
        // }
        .ant-tabs-tab-btn {
          color: @white !important;
          text-shadow: none;
        }

        .ant-tabs-tab-remove {
          opacity: 1;

          svg {
            fill: @white;
          }
        }
      }
    }

    .ant-tabs-nav > div:nth-child(1) {
      margin-left: 13px;

      .ant-tabs-tab {
        margin-right: 6px !important;

        &:nth-last-child(2) {
          margin-right: 20px !important;
        }
      }
    }
  }

  .ant-tabs-extra-content {
    margin-top: 2px;
    margin-right: 10px;
    line-height: @multiple-height-large !important;
  }

  .ant-dropdown-trigger {
    display: inline-flex;
  }

  &.jeesite-multiple-tabs-hide-close {
    .ant-tabs-tab-remove {
      opacity: 0 !important;
    }
  }

  .jeesite-multiple-tabs-content {
    &__info {
      display: inline-block;
      width: 100%;
      cursor: pointer;
      user-select: none;
    }

    &__extra-redo {
      span[role='img'] {
        transform: rotate(0deg);
      }
    }

    &__extra-quick,
    &__extra-redo,
    &__extra-fold {
      display: inline-block;
      width: 30px;
      // height: @multiple-height-large;
      // line-height: @multiple-height-large;
      padding-top: 7px;
      color: @text-color-secondary;
      text-align: center;
      cursor: pointer;
      // border-left: 1px solid @header-light-bottom-border-color;

      &:hover {
        color: @text-color-base;
      }

      span[role='img'] {
        transform: rotate(90deg);
      }
    }
  }

  .ant-tabs .ant-tabs-nav {
    .ant-tabs-nav-more {
      padding-top: 12px;
    }
  }
}

.ant-tabs-dropdown-menu {
  padding: 5px 0;
  max-height: 300px;

  &-title-content {
    display: flex;
    align-items: center;
    padding-left: 8px;
    .@{prefix-cls-3} {
      &-content__info {
        width: auto;
        margin-left: 0;
        line-height: 28px;
      }
    }

    .anticon:not(.anticon-close) {
      margin-left: -3px;
      margin-right: 3px;
    }
  }

  &-item-remove {
    margin-left: auto !important;
  }
}

html[data-theme='dark'] {
  .@{prefix-cls-3} {
    .ant-tabs.ant-tabs-card {
      .ant-tabs-nav {
        background: #000;

        &::before {
          border-bottom: 0;
        }

        .ant-tabs-tab {
          color: #aaa !important;
          background: #151515;

          svg {
            fill: #aaa !important;
          }

          &:hover,
          .ant-tabs-tab-btn:hover {
            color: #ddd !important;
          }
        }

        .ant-tabs-tab-active {
          background: fade(#2a50ed, 85) !important;

          svg {
            fill: #fff !important;
          }

          .ant-tabs-tab-btn,
          .ant-tabs-tab-btn:hover {
            color: #fff !important;
          }
        }
      }
    }
  }
}
