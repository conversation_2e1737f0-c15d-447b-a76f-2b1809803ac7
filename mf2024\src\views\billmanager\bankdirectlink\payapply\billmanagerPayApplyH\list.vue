<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="default" @click="handleExport()">
          <Icon icon="i-ant-design:download-outlined" /> {{ t('导出') }}
        </a-button>
        <a-button type="default" @click="handleImport()">
          <Icon icon="i-ant-design:upload-outlined" /> {{ t('导入') }}
        </a-button>
        <a-button type="primary" @click="handleForm({})" v-auth="'bankdirectlink:payapply:billmanagerPayApplyH:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleConfirmForm({ djno: record.djno })">
          {{ record.id }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
    <confirmForm @register="registerConfirmDrawer" @success="handleSuccess" />
    <FormImport @register="registerImportModal" @success="handleSuccess" />
    <BpmRuntimeTrace @register="registerTraceModal" />
    <!-- <component
      :is="inputFormComponent"
      v-model:visible="inputFormVisible"
      @register="registerDrawerBpmMyTask"
      @success="handleSuccess"
    /> -->
  </div>
</template>
<script lang="ts" setup name="ViewsBankdirectlinkPayapplyBillmanagerPayApplyHList">
  import { unref, shallowRef, ref, defineComponent } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { billmanagerPayApplyHDelete, billmanagerPayApplyHListData } from '/@/api/billmanager/bankdirectlink/payapply/billmanagerPayApplyH';
  import { useDrawer } from '/@/components/Drawer';
  import { useModal } from '/@/components/Modal';
  import { BpmRuntimeTrace } from '/@/components/Bpm';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import confirmForm from './confirmForm.vue';
  import FormImport from './formImport.vue';
  // import { bpmTaskDefForm } from '/@/api/bpm/myTask';
  // import { dynamicImport } from '/@/router/helper/routeHelper';
  // import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';
  // import qs from 'qs';

  const { t } = useI18n('bankdirectlink.payapply.billmanagerPayApplyH');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('付款申请单主表管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('申请日期'),
        field: 'ddate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('申请单号'),
        field: 'id',
        component: 'Input',
      },
      {
        label: t('申请部门'),
        field: 'deptCode',
        component: 'Input',
      },
      {
        label: t('创建人名称'),
        field: 'createByName',
        component: 'Input',
      },
      {
        label: t('状态'),
        field: 'status',
        component: 'Select',
        componentProps: {
          dictType: 'bpm_biz_status',
          allowClear: true,
          onChange: handleSuccess,
        },
      },
      {
        label: t('备注'),
        field: 'remarks',
        component: 'Input',
      },
      {
        label: t('供应商'),
        field: 'venCode',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('申请单号'),
      dataIndex: 'id',
      key: 'a.id',
      sorter: true,
      width: 130,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('申请日期'),
      dataIndex: 'ddate',
      key: 'a.ddate',
      sorter: true,
      width: 130,
      align: 'left',
      customRender: ({ record }) => {
        return formatDate(record.ddate);
      },
    },
    {
      title: t('申请部门'),
      dataIndex: 'office.officeName',
      key: 'a.dept_code',
      // sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('创建人名称'),
      dataIndex: 'createByName',
      key: 'a.create_by_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('更新时间'),
      dataIndex: 'updateDate',
      key: 'a.update_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'a.status',
      sorter: true,
      width: 130,
      align: 'left',
      dictType: 'bpm_biz_status',
    },
    {
      title: t('备注'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('供应商'),
      dataIndex: 'vendor.officeName',
      key: 'a.ven_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑付款申请单主表'),
        onClick: handleForm.bind(this, { djno: record.djno }),
        auth: 'bankdirectlink:payapply:billmanagerPayApplyH:edit',
      },
      // {
      //   label: t('confirmForm'),
      //   onClick: handleConfirm.bind(this, { djno: record.djno }),
      // },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除付款申请单主表'),
        popConfirm: {
          title: t('是否确认删除付款申请单主表'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'bankdirectlink:payapply:billmanagerPayApplyH:edit',
        ifShow: () => record.status == '9',
      },
      {
        icon: 'i-fluent:flowchart-20-regular',
        title: t('流程追踪'),
        onClick: handleTrace.bind(this, record),
        ifShow: () => record.status != '9',
      },
      // {
      //   icon: 'mdi:checkbox-marked-outline',
      //   title: t('任务办理'),
      //   onClick: handleBpmMyTask.bind(this, { id: record.id }),
      //   ifShow: () => record.status == '4' || record.status == '5' || record.status == '6',
      // },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerConfirmDrawer, { openDrawer: confirmDrawer }] = useDrawer();
  const [registerTable, { reload, getForm }] = useTable({
    api: billmanagerPayApplyHListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
    // record.isView = true;
    // confirmDrawer(true, record);
  }

  function handleConfirmForm(record: Recordable) {
    record.isView = true;
    confirmDrawer(true, record);
  }

  async function handleExport() {
    const { ctxAdminPath } = useGlobSetting();
    downloadByUrl({
      url: ctxAdminPath + '/bankdirectlink/payapply/billmanagerPayApplyH/exportData',
      params: getForm().getFieldsValue(),
    });
  }

  const [registerImportModal, { openModal: importModal }] = useModal();

  function handleImport() {
    importModal(true, {});
  }

  async function handleDelete(record: Recordable) {
    const params = { djno: record.djno };
    const res = await billmanagerPayApplyHDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }

  const [registerTraceModal, { openModal: traceModel }] = useModal();

  function handleTrace(record: Recordable) {
    traceModel(true, { formKey: 'peyApply', bizKey: record.id });
  }

  function handleConfirm(record: Recordable) {
    confirmDrawer(true, record);
  }

  // 格式化日期，只显示年月日
  function formatDate(dateStr: string): string {
    if (!dateStr) return '';

    try {
      // 如果日期字符串包含时间部分，只取日期部分
      if (dateStr.includes(' ')) {
        return dateStr.split(' ')[0];
      }

      // 如果是完整的日期格式，使用 Date 对象格式化
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) {
        return dateStr; // 如果无法解析，返回原始字符串
      }

      // 格式化为 YYYY-MM-DD
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      return `${year}-${month}-${day}`;
    } catch (error) {
      console.warn('日期格式化失败:', dateStr, error);
      return dateStr; // 出错时返回原始字符串
    }
  }

  const [registerDrawerBpmMyTask, { setDrawerData }] = useDrawer();
  const inputFormComponent = shallowRef<Nullable<any>>(null);
  const inputFormVisible = ref<Boolean>(false);
  async function handleBpmMyTask(record: Recordable) {
    const data = await bpmTaskDefForm({ formKey: 'peyApply', bizKey: record.id });
    if (data.result == 'true') {
      const url = data.pcUrl;
      const idx = url.indexOf('?');
      // component
      let component: ReturnType<typeof defineComponent>;
      const compStr = idx == -1 ? url : url.substring(0, idx);
      if (compStr && compStr != '') {
        const imp = dynamicImport(compStr);
        if (imp) component = createAsyncComponent(imp);
      }
      // params
      let params = {};
      const paramStr = idx == -1 ? '' : url.substring(idx + 1);
      if (paramStr && paramStr != '') {
        params = qs.parse(paramStr);
      }
      // open
      if (component) {
        inputFormComponent.value = component;
        inputFormVisible.value = true;
        setDrawerData(params);
      }
    } else {
      showMessage(data.message);
    }
  }
</script>
