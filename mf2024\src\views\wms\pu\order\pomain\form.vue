<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'wms:pu:order:pomain:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm">
      <template #childList>
        <BasicTable
          @register="registerPodetailsTable"
          @row-click="handlePodetailsRowClick"
        />
        <a-button class="mt-2" @click="handlePodetailsAdd" v-auth="'wms:pu:order:pomain:edit'">
          <Icon icon="i-ant-design:plus-circle-outlined" /> {{ t('新增') }}
        </a-button>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsWmsPuOrderPomainForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { Pomain, pomainSave, pomainForm } from '/@/api/wms/pu/order/pomain';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('wms/pu.order.pomain');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<Pomain>({} as Pomain);

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增采购订单') : t('编辑采购订单'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('id'),
      field: 'id',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ required: true }, { pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('订单号'),
      field: 'cpoid',
      component: 'Input',
      componentProps: {
        maxlength: 30,
      },
    },
    {
      label: t('订单日期'),
      field: 'ddate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        showTime: false,
      },
      required: true,
    },
    {
      label: t('供应商编码'),
      field: 'venCode',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('供应商名称'),
      field: 'venName',
      component: 'Input',
      componentProps: {
        maxlength: 98,
      },
    },
    {
      label: t('部门编码'),
      field: 'depCode',
      component: 'Input',
      componentProps: {
        maxlength: 12,
      },
    },
    {
      label: t('部门名称'),
      field: 'depName',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('备注'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 255,
      },
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('仓库'),
      field: 'whCode',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('仓库名称'),
      field: 'whName',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('合同号'),
      field: 'htno',
      component: 'Input',
      componentProps: {
        maxlength: 50,
      },
    },
    {
      label: t('关闭人'),
      field: 'closeBy',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('审核人'),
      field: 'verifBy',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('cdefine1'),
      field: 'cdefine1',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('cdefine2'),
      field: 'cdefine2',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('cdefine3'),
      field: 'cdefine3',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('cdefine4'),
      field: 'cdefine4',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('cdefine5'),
      field: 'cdefine5',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
    },
    {
      label: t('cdefine6'),
      field: 'cdefine6',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('cdefine7'),
      field: 'cdefine7',
      component: 'Input',
      componentProps: {
        maxlength: 51,
      },
    },
    {
      label: t('cdefine8'),
      field: 'cdefine8',
      component: 'Input',
      componentProps: {
        maxlength: 4,
      },
    },
    {
      label: t('cdefine9'),
      field: 'cdefine9',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
    },
    {
      label: t('cdefine10'),
      field: 'cdefine10',
      component: 'Input',
      componentProps: {
        maxlength: 60,
      },
    },
    {
      label: t('cdefine11'),
      field: 'cdefine11',
      component: 'Input',
      componentProps: {
        maxlength: 120,
      },
    },
    {
      label: t('cdefine12'),
      field: 'cdefine12',
      component: 'Input',
      componentProps: {
        maxlength: 120,
      },
    },
    {
      label: t('cdefine13'),
      field: 'cdefine13',
      component: 'Input',
      componentProps: {
        maxlength: 120,
      },
    },
    {
      label: t('cdefine14'),
      field: 'cdefine14',
      component: 'Input',
      componentProps: {
        maxlength: 120,
      },
    },
    {
      label: t('cdefine15'),
      field: 'cdefine15',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
    },
    {
      label: t('cdefine16'),
      field: 'cdefine16',
      component: 'Input',
      componentProps: {
        maxlength: 51,
      },
    },
    {
      label: t('采购订单明细'),
      field: 'childList',
      component: 'Input',
      colProps: { lg: 24, md: 24 },
      slot: 'childList',
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerPodetailsTable, podetailsTable] = useTable({
    actionColumn: {
      width: 60,
      actions: (record: Recordable) => [
        {
          icon: 'i-ant-design:delete-outlined',
          color: 'error',
          popConfirm: {
            title: '是否确认删除',
            confirm: handlePodetailsDelete.bind(this, record),
          },
          auth: 'wms:pu:order:pomain:edit',
        },
      ],
    },
    rowKey: 'id',
    pagination: false,
    bordered: true,
    size: 'small',
    inset: true,
  });

  async function setPodetailsTableData(_res: Recordable) {
    podetailsTable.setColumns([
      {
        title: t('行号'),
        dataIndex: 'ivouchrowno',
        width: 130,
        align: 'center',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 8,
        },
        editRule: false,
      },
      {
        title: t('是否称重'),
        dataIndex: 'bweight',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 1,
        },
        editRule: true,
      },
      {
        title: t('存货编码'),
        dataIndex: 'invCode',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 60,
        },
        editRule: false,
      },
      {
        title: t('存货名称'),
        dataIndex: 'invName',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 255,
        },
        editRule: false,
      },
      {
        title: t('规格型号'),
        dataIndex: 'invStd',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 255,
        },
        editRule: false,
      },
      {
        title: t('单位编码'),
        dataIndex: 'unitCode',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 35,
        },
        editRule: false,
      },
      {
        title: t('单位名称'),
        dataIndex: 'unitName',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 20,
        },
        editRule: false,
      },
      {
        title: t('cdefine22'),
        dataIndex: 'cdefine22',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 60,
        },
        editRule: false,
      },
      {
        title: t('cdefine23'),
        dataIndex: 'cdefine23',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 60,
        },
        editRule: false,
      },
      {
        title: t('cdefine24'),
        dataIndex: 'cdefine24',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 500,
        },
        editRule: false,
      },
      {
        title: t('cdefine25'),
        dataIndex: 'cdefine25',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 60,
        },
        editRule: false,
      },
      {
        title: t('cdefine26'),
        dataIndex: 'cdefine26',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 51,
        },
        editRule: false,
      },
      {
        title: t('cdefine27'),
        dataIndex: 'cdefine27',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 51,
        },
        editRule: false,
      },
      {
        title: t('cdefine28'),
        dataIndex: 'cdefine28',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 120,
        },
        editRule: false,
      },
      {
        title: t('cdefine29'),
        dataIndex: 'cdefine29',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 120,
        },
        editRule: false,
      },
      {
        title: t('cdefine30'),
        dataIndex: 'cdefine30',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 120,
        },
        editRule: false,
      },
      {
        title: t('cdefine31'),
        dataIndex: 'cdefine31',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 120,
        },
        editRule: false,
      },
      {
        title: t('cdefine32'),
        dataIndex: 'cdefine32',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 120,
        },
        editRule: false,
      },
      {
        title: t('cdefine33'),
        dataIndex: 'cdefine33',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 120,
        },
        editRule: false,
      },
      {
        title: t('cdefine34'),
        dataIndex: 'cdefine34',
        width: 130,
        align: 'center',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 8,
        },
        editRule: false,
      },
      {
        title: t('cdefine35'),
        dataIndex: 'cdefine35',
        width: 130,
        align: 'center',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 8,
        },
        editRule: false,
      },
      {
        title: t('cdefine36'),
        dataIndex: 'cdefine36',
        width: 215,
        align: 'center',
        editRow: true,
        editComponent: 'DatePicker',
        editComponentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
        editRule: false,
      },
      {
        title: t('cdefine37'),
        dataIndex: 'cdefine37',
        width: 215,
        align: 'center',
        editRow: true,
        editComponent: 'DatePicker',
        editComponentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
        editRule: false,
      },
      {
        title: t('订单数量'),
        dataIndex: 'iqty',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 28,
        },
        editRule: false,
      },
      {
        title: t('cbdefine1'),
        dataIndex: 'cbdefine1',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 51,
        },
        editRule: false,
      },
      {
        title: t('件数'),
        dataIndex: 'inum',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 28,
        },
        editRule: false,
      },
      {
        title: t('计划到货日期'),
        dataIndex: 'planDate',
        width: 130,
        align: 'center',
        editRow: true,
        editComponent: 'DatePicker',
        editComponentProps: {
          format: 'YYYY-MM-DD',
          showTime: false,
        },
        editRule: false,
      },
      {
        title: t('关闭人'),
        dataIndex: 'closeBy',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 20,
        },
        editRule: false,
      },
      {
        title: t('累计入库'),
        dataIndex: 'inQty',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 28,
        },
        editRule: false,
      },
      {
        title: t('剩余入库'),
        dataIndex: 'syInQty',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 29,
        },
        editRule: false,
      },
      {
        title: t('剩余需求'),
        dataIndex: 'syNeedQty',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 51,
        },
        editRule: false,
      },
    ]);
    podetailsTable.setTableData(record.value.childList || []);
  }

  function handlePodetailsRowClick(record: Recordable) {
    record.onEdit?.(true, false);
  }

  function handlePodetailsAdd() {
    podetailsTable.insertTableDataRecord({
      id: new Date().getTime(),
      isNewRecord: true,
      editable: true,
    });
  }

  function handlePodetailsDelete(record: Recordable) {
    podetailsTable.deleteTableDataRecord(record);
  }

  async function getChildList() {
    let childListValid = true;
    let childList: Recordable[] = [];
    for (const record of podetailsTable.getDataSource()) {
      if (!(await record.onEdit?.(false, true))) {
        childListValid = false;
      }
      childList.push({
        ...record,
        id: !!record.isNewRecord ? '' : record.id,
      });
    }
    for (const record of podetailsTable.getDelDataSource()) {
      if (!!record.isNewRecord) continue;
      childList.push({
        ...record,
        status: '1',
      });
    }
    if (!childListValid) {
      throw { errorFields: [{ name: ['childList'] }] };
    }
    return childList;
  }

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await pomainForm(data);
    record.value = (res.pomain || {}) as Pomain;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setPodetailsTableData(res);
    updateSchema([
      {
        field: 'id',
        componentProps: {
          disabled: !record.value.isNewRecord,
        },
      },
    ]);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      data.childList = await getChildList();
      // console.log('submit', params, data, record);
      const res = await pomainSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
