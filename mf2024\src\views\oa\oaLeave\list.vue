<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'oa:oaLeave:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.userName }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
    <BpmRuntimeTrace @register="registerTraceModal" />
  </div>
</template>
<script lang="ts" setup name="ViewsOaOaLeaveList">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { oaLeaveDelete, oaLeaveListData } from '/@/api/oa/oaLeave';
  import { officeTreeData } from '/@/api/sys/office';
  import { useDrawer } from '/@/components/Drawer';
  import { useModal } from '/@/components/Modal';
  import { BpmRuntimeTrace } from '/@/components/Bpm';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('oa.oaLeave');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const getTitle = {
    icon: meta.icon || 'ant-design:book-outlined',
    value: meta.title || t('请假管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('请假人'),
        field: 'userCode',
        component: 'TreeSelect',
        componentProps: {
          api: officeTreeData,
          params: { isLoadUser: true, userIdPrefix: '' },
          canSelectParent: false,
          allowClear: true,
        },
      },
      {
        label: t('所属部门'),
        field: 'officeCode',
        component: 'TreeSelect',
        componentProps: {
          api: officeTreeData,
          allowClear: true,
        },
      },
      {
        label: t('状态'),
        field: 'status',
        component: 'Select',
        componentProps: {
          dictType: 'bpm_biz_status',
          allowClear: true,
          onChange: handleSuccess,
        },
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('请假人'),
      dataIndex: 'userName',
      key: 'a.user_name',
      sorter: true,
      width: 130,
      align: 'center',
      slot: 'firstColumn',
    },
    {
      title: t('所属部门'),
      dataIndex: 'officeName',
      key: 'a.office_name',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('开始时间'),
      dataIndex: 'startTime',
      key: 'a.start_time',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('结束时间'),
      dataIndex: 'endTime',
      key: 'a.end_time',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('请假天数'),
      dataIndex: 'leaveDays',
      key: 'a.leave_days',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('请假类型'),
      dataIndex: 'leaveType',
      key: 'a.leave_type',
      sorter: true,
      width: 80,
      align: 'center',
      dictType: 'oa_leave_type',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'a.status',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'bpm_biz_status',
    },
    {
      title: t('更新时间'),
      dataIndex: 'updateDate',
      key: 'a.update_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑请假申请'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'oa:oaLeave:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除请假申请'),
        popConfirm: {
          title: t('是否确认删除请假申请'),
          confirm: handleDelete.bind(this, { id: record.id }),
        },
        auth: 'oa:oaLeave:edit',
        ifShow: () => record.status == '9',
      },
      {
        icon: 'i-fluent:flowchart-20-regular',
        title: t('流程追踪'),
        onClick: handleTrace.bind(this, record),
        ifShow: () => record.status != '9',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload }] = useTable({
    api: oaLeaveListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const res = await oaLeaveDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleSuccess() {
    reload();
  }

  const [registerTraceModal, { openModal: traceModel }] = useModal();

  function handleTrace(record: Recordable) {
    traceModel(true, { formKey: 'leave', bizKey: record.id });
  }
</script>
