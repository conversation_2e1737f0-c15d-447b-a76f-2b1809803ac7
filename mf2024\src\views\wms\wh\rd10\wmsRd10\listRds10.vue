<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
    <div>
        <!-- @close="handleSuccess" -->
        <BasicDrawer
        v-bind="$attrs"
        @register="registerDrawer"
        :title="getTitle.value"
        :showFooter="false"
        @ok="handleTrayPrintClick"
        width="90%">
        <!--  okText="打印托盘"  cancelText="全部关闭" -->
        <!-- <template #title>
          <span> 托盘打印 </span>
        </template> -->
        <BasicTable @register="registerTable">
            <template #firstColumn="{ record }">
                <a @click="handleForm({ id: record.id , djno : record.createDate })">
                {{ record.createDate }}
                </a>
            </template>
        </BasicTable>
      </BasicDrawer>
      
    </div>
    </template>
<script lang="ts" setup>
    import { ref, unref } from 'vue';
    import { useI18n } from '/@/hooks/web/useI18n';
    import { BasicModal, useModalInner  } from '/@/components/Modal';
    import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
    import { BasicTable, useTable ,BasicColumn, } from '/@/components/Table';
    import {  RadioGroup } from '/@/components/Form';
    import { useMessage } from '/@/hooks/web/useMessage';
    import { printSnNumber } from '/@/api/wms/barcode/encode';
    import { useGlobSetting } from '/@/hooks/setting';
    import { dictDataListData } from '/@/api/sys/dictData';
    import { downloadByUrl } from '/@/utils/file/download';
    import { router } from '/@/router';
    import { BarTypeEnum } from '/@/enums/defEnum';
    import { wmsRds10ListData } from '/@/api/wms/wh/rd10/wmsRds10';

    const { meta } = unref(router.currentRoute);
    const { ctxPath  } = useGlobSetting();
    const getTitle = {
        icon: meta.icon || 'i-ant-design:book-outlined',
        value: router.currentRoute.value.query.tabTitle || '入库明细',
    };

    const optionsValues = [];
    const showPrintModal = ref(false);

    const djno = ref('');

    const { showMessage, showMessageModal,createSuccessModal } = useMessage();
    const emit = defineEmits(['success', 'register']);
    const { t } = useI18n('bas.inv.basInv');
    const radioGroup = ref<String>('8080');
    const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data: any) => {
        djno.value = data.djno;
        const res = await wmsRds10ListData({ parentId: data.djno });
        registerTableList.setTableData([]);
        registerTableList.setTableData(res.list);
        // registerTableList.reload();
    });    

const tableColumns: BasicColumn[] = [
    {
      title: t('入库时间'),
      dataIndex: 'createDate',
      key: 'a.create_date',
      sorter: true,
      width: 70,
      align: 'left',
    //   slot: 'firstColumn',
    },
    {
      title: t('制单人'),
      dataIndex: 'createByName',
      key: 'a.create_by_name',
      sorter: true,
      width: 60,
      align: 'left',
    },
    {
      title: t('存货编码'),
      dataIndex: 'invCode',
      key: 'a.inv_code',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('存货名称'),
      dataIndex: 'basInv.invName',
      // key: 'a.inv_code',
      // sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('规格型号'),
      dataIndex: 'basInv.invStd',
      width: 130,
      align: 'center',
    },
    {
      title: t('单位'),
      dataIndex: 'basInv.unitName',
      width: 40,
      align: 'center',
    },
    {
      title: t('批次'),
      dataIndex: 'parent.cbatch',
      key: 'parent.cbatch',
      sorter: true,
      width: 80,
      align: 'left',
    },
    {
      title: t('仓库'),
      dataIndex: 'parent.whCode',
      key: 'parent.wh_code',
      sorter: true,
      width: 60,
      align: 'center',
    },
    {
      title: t('入库数量'),
      dataIndex: 'iqty',
      key: 'a.iqty',
      sorter: true,
      width: 50,
      align: 'left',
    },
    {
      title: t('入库件数'),
      dataIndex: 'inum',
      key: 'a.inum',
      sorter: true,
      width: 50,
      align: 'left',
    },
    {
      title: t('货位名称'),
      dataIndex: 'basPos.posName',
      // key: 'basPos.pos_name',
      // sorter: true,
      width: 70,
      align: 'center',
    },
    {
      title: t('货位编码'),
      dataIndex: 'basPos.posCode',
      // key: 'basPos.pos_name',
      // sorter: true,
      width: 70,
      align: 'center',
    },
    {
      title: t('申请单号'),
      dataIndex: 'parent.djno',
      key: 'parent.djno',
      sorter: true,
      width: 70,
      align: 'center',
    },
    {
      title: t('申请日期'),
      dataIndex: 'parent.ddate',
      key: 'parent.ddate',
      sorter: true,
      width: 50,
      align: 'left',
    },
    { 
      title: t('单据状态'),
      dataIndex: 'parent.djStatus',
      key: 'parent.dj_status',
      sorter: true,
      width: 80,
      align: 'center',
      dictType: 'wms_rd10_status',
      fixed: 'left',
      ifShow: false,
    },
    {
      title: t('数量'),
      dataIndex: 'parent.iqty',
      key: 'parent.iqty',
      sorter: true,
      width: 80,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('件数'),
      dataIndex: 'parent.inum',
      key: 'parent.inum',
      sorter: true,
      width: 60,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('剩余入库数量'),
      dataIndex: 'parent.syQty',
      // key: 'parent.sy_qty',
      // sorter: true,
      width: 100,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('剩余入库件数'),
      dataIndex: 'parent.syNum',
      // key: 'parent.sy_num',
      // sorter: true,
      width: 100,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('累计入库数量'),
      dataIndex: 'parent.sumQty',
      key: 'parent.sum_qty',
      sorter: true,
      width: 90,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('累计入库件数'),
      dataIndex: 'parent.sumNum',
      key: 'parent.sum_num',
      sorter: true,
      width: 90,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('最大托盘序号'),
      dataIndex: 'parnt.maxTpSeq',
      key: 'a.max_tp_seq',
      sorter: true,
      width: 130,
      align: 'center',
      ifShow: false,
    },
    {
      title: t('update_date'),
      dataIndex: 'updateDate',
      key: 'a.update_date',
      sorter: true,
      width: 130,
      align: 'center',
      ifShow: false,
    },
    {
      title: t('remarks'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 130,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('修改人'),
      dataIndex: 'updateByName',
      key: 'a.update_by_name',
      sorter: true,
      width: 130,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('托盘容量'),
      dataIndex: 'parent.packSize',
      key: 'parent.pack_size',
      sorter: true,
      width: 80,
      align: 'center',
      ifShow: false,
    },
    {
      title: t('单件容量'),
      dataIndex: 'parent.pieceQty',
      key: 'parent.piece_qty',
      sorter: true,
      width: 80,
      align: 'left',
      ifShow: false,
    },
  ];

const [registerTable, registerTableList] = useTable({
    // api: wmsRds10ListData,
    beforeFetch: (params) => {
      // router.currentRoute.value.query.code ? params.parent.djno = router.currentRoute.value.query.code : null;
      console.log(djno.value, 'router===');
      djno.value ? params['parent.djno'] = djno.value : ''
      console.log('params',params,router.currentRoute.value.query.code)
      return params;
    },
    columns: tableColumns,
    // actionColumn: actionColumn,
    // formConfig: searchForm,
    // showTableSetting: true,
    // useSearchForm: true,
    canResize: true,
    // rowSelection: { type: 'checkbox' },
  });
    
        async function handleTrayPrintClick() {
        // 把得到的值传递给父组件
        try {
            const data = await traytable.getDataSource();
            console.log(data, 'data===');
            // 遍历查找data，如果maxTpSeq或者minTpSeq其中有一个值为空，则返回true，否则返回false
            const reqData = data.some((item) => !item.editValueRefs.maxTpSeq || !item.editValueRefs.minTpSeq);
            if (reqData) {
                showMessage('开始序号或托盘数不能为空！');
                return;
            }
            // 把data 里面的djno，minTpSeq， 取出来，用逗号隔开，然后传给后台
            const selIds = data.map((item) => item.djno).join(',');
            const minTpSeq = data.map((item) => item.editValueRefs.minTpSeq).join(',');
            const maxTpSeq = data.map((item) => {
              const min = parseFloat(item.editValueRefs.minTpSeq) || 0; // 转换为浮点数，如果转换失败则使用0
              const max = parseFloat(item.editValueRefs.maxTpSeq) || 0; // 同上
              console.log(min, max, 'min, max===', item.minTpSeq, item.maxTpSeq);
              return (min + max - 1).toString(); // 执行数值运算，然后转换为字符串
            }).join(','); // 将结果数组连接成逗号分隔的字符串
            const params = {
                selIds : selIds,
                minTpSeq: minTpSeq,
                maxTpSeq: maxTpSeq,
                barType: BarTypeEnum.TuoPan,
                barSizeType: radioGroup.value,
            };
            const res = await printSnNumber(params);
            if(res.result == "true"){
                console.log(res,'res===');
                createSuccessModal({ 
                    content: '打印 ' + res.fileName,
                    okText: '下载',
                    onOk() {
                        downloadByUrl({ url: ctxPath + res.pdfUrl });
                        setTimeout(closeDrawer);
                    },
                });
            } else {
                showMessage(res.message);
            }        
            } catch (error) {
                
            }
        };
    
        // function handleSuccess(record: Recordable) {
        //     reload({ record });
        // }
    </script>
    <style scoped>
    .tableTitle {
        margin-left: 10px;
        font-size: 18px;
        span{
            margin-right: 10px;
        }
     }
    </style>
      