import type { ProjectConfig } from '/#/config';
import { MenuTypeEnum, MenuModeEnum, TriggerEnum, MixSidebarTriggerEnum } from '/@/enums/menuEnum';
import { CacheTypeEnum } from '/@/enums/cacheEnum';
import {
  ContentEnum,
  PermissionModeEnum,
  ThemeEnum,
  RouterTransitionEnum,
  SettingButtonPositionEnum,
  SessionTimeoutProcessingEnum,
} from '/@/enums/appEnum';
import { SIDE_BAR_BG_COLOR_LIST, HEADER_PRESET_BG_COLOR_LIST } from './designSetting';
import { primaryColor } from '../../build/config/themeConfig';

// ! You need to clear the browser cache after the change
const setting: ProjectConfig = {
  showSettingButton: true,
  showDarkModeToggle: true,
  settingButtonPosition: 'auto',
  permissionMode: 'BACK',
  permissionCacheType: 0,
  sessionTimeoutProcessing: 0,
  themeColor: '#0096c7',
  grayMode: false,
  colorWeak: false,
  fullContent: false,
  contentMode: 'full',
  showLogo: true,
  showFooter: false,
  headerSetting: {
    bgColor: '#394664',
    fixed: true,
    show: true,
    theme: 'dark',
    useLockPage: true,
    showFullScreen: true,
    showDoc: true,
    showNotice: true,
    showSearch: true,
  },
  menuSetting: {
    bgColor: '#304156',
    fixed: true,
    collapsed: true,
    collapsedShowTitle: true,
    canDrag: false,
    show: true,
    hidden: false,
    menuWidth: 200,
    mode: 'inline',
    type: 'mix',
    theme: 'dark',
    split: true,
    topMenuAlign: 'start',
    trigger: 'HEADER',
    accordion: true,
    closeMixSidebarOnChange: false,
    mixSideTrigger: 'hover',
    mixSideFixed: false,
  },
  multiTabsSetting: {
    cache: false,
    show: true,
    style: '3',
    canDrag: true,
    showQuick: true,
    showRedo: true,
    showFold: false,
  },
  transitionSetting: {
    enable: true,
    basicTransition: 'fade-slide',
    openPageLoading: true,
    openNProgress: false,
  },
  openKeepAlive: true,
  fontSize: 14,
  lockTime: 0,
  showBreadCrumb: true,
  showBreadCrumbIcon: true,
  useErrorHandle: false,
  useOpenBackTop: true,
  canEmbedIFramePage: true,
  closeMessageOnSwitch: true,
  removeAllHttpPending: false,
};

export default setting;