{"name": "@jeesite/core", "version": "5.12.1", "private": true, "type": "module", "scripts": {"type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "uninstall": "rimraf node_modules", "update": "ncu -u -x codemirror,pinia,cropperjs"}, "dependencies": {"@vueuse/core": "13.1.0", "@vueuse/shared": "13.1.0", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "5.1.12", "@zxcvbn-ts/core": "3.0.4", "codemirror": "5.65.16", "cropperjs": "1.6.2", "crypto-js": "4.2.0", "echarts": "5.6.0", "nprogress": "0.2.0", "path-to-regexp": "8.2.0", "pinia": "2.3.1", "print-js": "1.6.0", "qrcode": "1.5.4", "qs": "6.14.0", "resize-observer-polyfill": "1.5.1", "showdown": "2.1.0", "sortablejs": "1.15.6", "spark-md5": "3.0.2", "vditor": "3.11.0", "vue-i18n": "11.1.3", "vue-json-pretty": "2.4.0", "vue-types": "6.0.0", "xlsx": "0.18.5"}, "devDependencies": {"@types/codemirror": "5.60.15", "@types/crypto-js": "4.2.2", "@types/lodash-es": "4.17.12", "@types/nprogress": "0.2.3", "@types/qrcode": "1.5.5", "@types/qs": "6.9.18", "@types/showdown": "2.0.6", "@types/sortablejs": "1.15.8", "@vitejs/plugin-vue": "5.2.4", "rollup-plugin-visualizer": "5.14.0", "vite-plugin-dts": "4.5.3", "vite-plugin-theme-vite3": "1.0.5"}, "homepage": "https://jeesite.com", "repository": {"type": "git", "url": "https://gitee.com/thinkgem/jeesite-vue.git"}, "bugs": {"url": "https://gitee.com/thinkgem/jeesite-vue/issues"}, "author": {"name": "ThinkGem", "email": "<EMAIL>", "url": "https://gitee.com/thinkgem"}}