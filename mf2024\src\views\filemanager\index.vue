<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <PageWrapper :sidebarWidth="230" :dense="!sidebar" :content-background="sidebar">
    <template #sidebar v-if="sidebar">
      <BasicTree
        :title="t('文件夹')"
        :search="true"
        :toolbar="true"
        :showIcon="true"
        :api="filemanagerFolderTreeData"
        :params="filemanagerFolderTreeDataParams"
        :defaultExpandLevel="2"
        @select="handleSelect"
      >
        <template #headerTitle>
          <Dropdown class="cursor-pointer" :trigger="['hover']" :dropMenuList="dropMenuList">
            {{ groupName }} <DownOutlined />
          </Dropdown>
        </template>
      </BasicTree>
    </template>
    <ListView :treeCode="treeCode" :groupType="groupType" />
  </PageWrapper>
</template>
<script lang="ts" setup name="ViewsFilemanagerIndex">
  import { onMounted, ref } from 'vue';
  import { DownOutlined } from '@ant-design/icons-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { PageWrapper } from '/@/components/Page';
  import { BasicTree } from '/@/components/Tree';
  import { Dropdown, DropMenu } from '/@/components/Dropdown';
  import { filemanagerFolderTreeData } from '/@/api/filemanager/filemanagerFolder';
  import ListView from './list.vue';
  import { useDict } from '/@/components/Dict';
  import { filemanagerIndex } from '/@/api/filemanager/filemanager';
  import { useQuery } from '/@/hooks/web/usePage';

  const { t } = useI18n('filemanager.filemanager');
  const treeCode = ref<string>('');

  const dropMenuList = ref<Array<DropMenu>>([]);
  const groupType = ref<string>('');
  const groupName = ref<string>('');
  const isOffice = ref<boolean>(true);
  const isSelf = ref<boolean>(true);
  const getQuery = useQuery();
  const sidebar = !!!getQuery.value.folderId;

  const filemanagerFolderTreeDataParams = ref<any>({});

  onMounted(async () => {
    var res = await filemanagerIndex();
    groupType.value = res.filemanager?.groupType;
    filemanagerFolderTreeDataParams.value.groupType = groupType.value;
    isOffice.value = res.isOffice;
    isSelf.value = res.isSelf;
    loadDropMenu();
  });

  async function loadDropMenu() {
    dropMenuList.value = (await useDict().initGetDictList('filemanager_group_type')).map((item) => {
      if (item.value == groupType.value) {
        groupName.value = item.name;
      }
      return {
        text: item.name,
        disabled:
          (!isOffice.value && item.value == 'office') || (!isSelf.value && item.value == 'self'),
        event: item.value,
        icon: 'i-radix-icons:dot',
        onClick: () => {
          groupType.value = item.value;
          groupName.value = item.name;
          filemanagerFolderTreeDataParams.value.groupType = groupType.value;
        },
      };
    });
  }

  function handleSelect(keys: string[]) {
    treeCode.value = keys[0];
  }
</script>
