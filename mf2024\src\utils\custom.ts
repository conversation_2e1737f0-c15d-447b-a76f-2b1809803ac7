import { treeSelect } from '/@/api/test/testData';

// 处理搜索列表数据
export function schemasData(arr) {
  const arrNew = arr.map((item) => {
    return {
      label: item.formLabel, //标签名
      field: item.formField, //字段名
      fieldLabel: item.formFieldLabel, //左边需要树形选择是需要填
      component: item.formComponent, //组件的类型   Select
      defaultValue: item.formDefaultValue, //默认值
      show: item.formShow == '0' ? false : true, //是否显示
      dynamicDisabled: item.formDynamicDisable, //是否禁用（默认false）
      componentProps: {
        allowClear: item.compAllowClear == '1' ? true : false, //是否允许直接清除（默认false）
        mode: item.compMode == '1' ? 'multiple' : '', // 下拉框模块，启用多选
        dictType: item.compDictType, //参数字典配置（下拉,单选框 ，多选必填）*****
        format: item.compFormat, //页面展示时间格式（时间选择必填）*****
        showTime: item.compShowTime, //是否可以选择时分秒（时间选择必填）*****
        api: item.compApi, // 地址 （树形选择必填）
        params: item.compParams, //地址 （树形选择必填）
        checkbox: item.compMode == '0' ? false : true,
        canSelectParent: item.compCanSelectParent == '1' ? true : false, //是否可以选择父项 （树形选择）
        selectType: item.formSelectType,
      },
    };
  });

  arrNew.forEach((item) => {
    if (item.component == 'DatePicker' || item.component == 'RangePicker') {
      delete item.componentProps.mode;
      if (item.componentProps.showTime == '0') {
        item.componentProps.showTime = false;
      }
      if (item.componentProps.showTime == '1') {
        item.componentProps.showTime = { format: 'HH:mm:ss' };
      }
      item.componentProps.valueFormat = item.componentProps.format;
    }
    if (item.component == 'ListSelect') {
      // componentProps: {
      //   // 内置列表选择类型：
      //   // 1）userSelect：用户选择；
      //   // 2）empUserSelect：员工选择（含组织机构信息）
      //   // 3）自定义，详见下一节
      //   selectType: 'userSelect',
      // },
      delete item.componentProps.allowClear;
    } else {
      delete item.componentProps.selectType;
    }
    if (item.component == 'TreeSelect') {
      // console.log(item.componentProps.api, '1111111111');
      const url = item.componentProps.api;
      item.componentProps.api = async () => {
        const res = await treeSelect({ url });
        return res;
      };
    }

    if (item.component == 'CheckboxGroup') {
      if(!item.componentProps.dictType){
        item.componentProps.options = [
          { label: '', value: '1' },
        ]
      }
      
    }

    // 解决警告处理
    if (item.componentProps.api == '') {
      delete item.componentProps.api;
    }
  });

  return arrNew;
}

// 处理表格全局数据
export function tabConfigData(obj) {
  const listTabConfig = {
    api: obj.api,
    autoCreateKey: obj.autoCreateKey == 1 ? true : false,
    bordered: obj.bordered == 1 ? true : false,
    canResize: obj.canResize == 1 ? true : false,
    clearSelectOnPageChange: obj.clearSelectOnPageChange == 1 ? true : false,
    clickToRowSelect: obj.clickToRowSelect == 1 ? true : false,
    ellipsis: obj.ellipsis == 1 ? true : false,
    emptyDataIsShowTable: obj.emptyDataIsShowTable == 1 ? true : false,
    immediate: obj.immediate == 1 ? true : false,
    inset: obj.inset == 1 ? true : false,
    isTreeTable: obj.isTreeTable == 1 ? true : false,
    loading: obj.loading == 1 ? true : false,
    pagination: obj.pagination == 1 ? true : false,
    showIndexColumn: obj.showIndexColumn == 1 ? true : false,
    showSummary: obj.showSummary == 1 ? true : false,
    showTableSetting: obj.showTableSetting == 1 ? true : false,
    striped: obj.striped == 1 ? true : false,
    useSearchForm: obj.useSearchForm == 1 ? true : false,
    tableSetting: {
      export: obj.isExport == 1 ? true : false,
      import: obj.isImport == 1 ? true : false,
    },
    titleHelpMessage: obj.titleHelpMessage,
    maxHeight: obj.maxHeight,
    resizeHeightOffset: obj.resizeHeightOffset,
    layoutCode: obj.viewCode,
  };

  return listTabConfig;
}

// 处理表单列表数据
export function formSchemas(arr, formColNum, data) {
  const arrNew = arr.map((item: any) => {
    return {
      label: item.originName, //标签名
      field: item.formField, //字段名
      fieldLabel: item.formLabel, //左边需要树形选择是需要填
      component: item.formComponent, //组件的类型   Select
      defaultValue: item.formDefaultValue, //默认值
      show: item.formShow == '0' ? false : true, //是否显示
      slot: item.formSlot,
      dynamicDisabled: item.formDynamicDisable == '1' ? true : false, //是否禁用（默认false）

      colProps: item.formColProps
        ? { lg: parseInt(item.formColProps), md: 24 }
        : { lg: parseInt(formColNum), md: 24 },
      componentProps: {
        allowClear: item.compAllowClear == '1' ? true : false, //是否允许直接清除（默认false）
        mode: item.compMode == '1' ? 'multiple' : '', // 下拉框模块，启用多选
        dictType: item.compDictType, //参数字典配置（下拉,单选框 ，多选必填）*****
        format: item.compFormat, //页面展示时间格式（时间选择必填）*****
        showTime: item.compShowTime, //是否可以选择时分秒（时间选择必填）*****
        api: item.compApi, // 地址 （树形选择必填）
        params: item.compParams, //地址 （树形选择必填）

        maxlength: item.compMaxLength, //最大长度
        canSelectParent: item.compCanSelectParent == '1' ? true : false, //是否可以选择父项 （树形选择）
        // 上传图片文件组件
        loadTime: data.__t, // number 加载时间戳new Date().getTime()，此为监听属性，方便刷新文件列表数据
        bizKey: data.id, // 当前表单主键值
        bizType: item.compBizType, // string 关联业务类型
        uploadType: item.compUploadType, // 上传文件类型（可选值：image、media、file、all）
        selectType: item.formSelectType,
        checkbox: item.compMode == '0' ? false : true,
      },
      formIsRequired: item.formIsRequired == '1' ? true : false,//是否必填
      formRules: item.formRules?item.formRules:'',
      formMessage: item.formMessage?item.formMessage:'',
      
    };
  });
  // // ListSelect组件不能有allowClear
  // // DatePicker组件不能有mode
  arrNew.forEach((item) => {
    if(item.component == 'Select'){
      item.itemProps= { validateTrigger: 'blur' }
    }

    if (item.component == 'DatePicker' || item.component == 'RangePicker') {
      delete item.componentProps.mode;
      if (item.componentProps.showTime == '0') {
        item.componentProps.showTime = false;
      }
      if (item.componentProps.showTime == '1') {
        item.componentProps.showTime = { format: 'HH:mm:ss' };
      }
      item.componentProps.valueFormat = item.componentProps.format;
    }
    if (item.component == 'ListSelect') {
      // componentProps: {
      //   // 内置列表选择类型：
      //   // 1）userSelect：用户选择；
      //   // 2）empUserSelect：员工选择（含组织机构信息）
      //   // 3）自定义，详见下一节
      //   selectType: 'userSelect',
      // },
      delete item.componentProps.allowClear;
    } else {
      delete item.componentProps.selectType;
    }
    if (item.component == 'TreeSelect') {
      const url = item.componentProps.api;
      item.componentProps.api = async () => {
        const res = await treeSelect({ url });
        return res;
      };
    }
    item.rules = [{ required: item.formIsRequired },{ pattern:new RegExp(item.formRules), message: item.formMessage }] 
    // 解决警告处理
    if (item.componentProps.api == '') {
      delete item.componentProps.api;
    }

    if (item.component == 'CheckboxGroup') {
      if(!item.componentProps.dictType){
        item.componentProps.options = [
          { label: '', value: '1' },
        ]
      }
      
    }
  });

  return arrNew;
}
