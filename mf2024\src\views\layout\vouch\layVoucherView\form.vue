<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicModal
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'layout:edit'"
    @register="registerModal"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherViewForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { LayVoucherView, layVoucherViewSave, layVoucherViewForm } from '../../../../api/layout/vouch/layVoucherView';
  import { layVoucherClsTreeData } from '/@/api/layout/vouch/layVoucherCls';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('layout.layVoucherView');
  const { showMessage } = useMessage();
  const record = ref<LayVoucherView>({} as LayVoucherView);
  const getTitle = computed(() => ({
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增页面视图') : t('编辑页面视图'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('单据标志'),
      field: 'vouchCode',
      fieldLabel: 'voucherCls.typeName',
      component: 'Input',
      dynamicDisabled: true,
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('布局标志'),
      field: 'code',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
      required: true,
    },
    {
      label: t('布局名称'),
      field: 'name',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('布局类型'),
      field: 'layType',
      component: 'Select',
      componentProps: {
        dictType: 'lay_type',
        allowClear: true,
      },
    },
    {
      label: t('布局描述'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 500,
      },
      colProps: { lg: 24, md: 24 },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ loading: true });
    await resetFields();
    const res = await layVoucherViewForm(data);
    record.value = (res.layVoucherView || {}) as LayVoucherView;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    updateSchema([
      {
        field: 'code',
        componentProps: {
          disabled: !record.value.isNewRecord,
        },
      },
    ]);
    setModalProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setModalProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        code: record.value.code,
      };
      // console.log('submit', params, data, record);
      const res = await layVoucherViewSave(params, data);
      showMessage(res.message);
      setTimeout(closeModal);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
