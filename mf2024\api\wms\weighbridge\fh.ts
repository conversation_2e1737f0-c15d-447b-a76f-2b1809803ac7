/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

import { UploadApiResult } from './upload';
import { UploadFileParams } from '/#/axios';
import { AxiosProgressEvent } from 'axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface CarplanFh extends BasicModel<CarplanFh> {
  
}

export const mfCarplanFhHListData = (params?: CarplanFh | any) =>
  defHttp.post<Page<CarplanFh>>({ url: adminPath + '/mf/fh/mfCarplanFhH/listData', params });

export const mfCarplanFhCListData = (params?: CarplanFh | any) =>
  defHttp.post<Page<CarplanFh>>({ url: adminPath + '/mf/fh/mfCarplanFhC/listData', params });

export const mfCarplanFhHinvalid = (params?: CarplanFh | any) =>
  defHttp.post<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhH/invalid', params });

export const updatePushStatusByHand = (params?: CarplanFh | any) =>
  defHttp.post<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhH/updatePushStatusByHand', params });

export const mfCarplanFhHDelete= (params?: CarplanFh | any) =>
  defHttp.get<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhH/delete', params });

export const mfCarplanFhHupdateCstatus= (params?: CarplanFh | any) =>
  defHttp.get<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhH/updateCstatus', params });

export const mfCarplanFhCDelete= (params?: CarplanFh | any) =>
  defHttp.get<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhC/delete', params });

export const updateCzStatusByHand = (params?: CarplanFh | any) =>
  defHttp.post<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhH/updateCzStatusByHand', params });

export const dispatchlistsListData = (params?: CarplanFh | any) =>
  defHttp.post<CarplanFh>({ url: adminPath + '/mf/fh/dispatchlist/dispatchlistsListData', params });

export const invPositionSumListData = (params?: CarplanFh | any) =>
  defHttp.post<CarplanFh>({ url: adminPath + '/mf/fh/invPositionSum/listData', params });



export const mfCarplanFhHimportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: AxiosProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/mf/fh/mfCarplanFhH/importData',
      onUploadProgress,
    },
    params,
  );


export const mfCarplanFhHForm = (params?: CarplanFh | any) =>
  defHttp.get<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhH/form', params });

export const mfCarplanFhHSave = (params?: any, data?: CarplanFh | any) =>
  defHttp.postJson<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhH/save', params, data });



export const poPodetailsListData = (params?: CarplanFh | any) =>
  defHttp.post<Page<CarplanFh>>({ url: adminPath + '/mf/dh/poPodetails/listData', params });



export const mfCarplanFhCForm = (params?: CarplanFh | any) =>
  defHttp.get<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhC/form', params });

export const mfCarplanFhCSave = (params?: any, data?: CarplanFh | any) =>
  defHttp.postJson<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhC/save', params, data });

export const mfCarplanFhCSaveData = (params?: any, data?: CarplanFh | any) =>
  defHttp.postJson<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhC/saveData', params, data });



export const mfCarplanFhCsaveChild = (params?: CarplanFh | any) =>
  defHttp.postJson<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhC/saveChild', params });


export const mfCarplanFhHformEdit= (params?: CarplanFh | any) =>
  defHttp.post<Page<CarplanFh>>({ url: adminPath + '/mf/fh/mfCarplanFhC/carPlanFhCForm', params });




export const mfCarplanFhCformView = (params?: CarplanFh | any) =>
  defHttp.post<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhC/formView', params });

export const mfCarplanFhCupdateBredStatus = (params?: CarplanFh | any) =>
  defHttp.post<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhC/updateBredStatus', params });


export const mfCarplanFhClistSave = (params?: CarplanFh | any) =>
  defHttp.postJson<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhC/listSave', params });

export const listPushData = (params?: CarplanFh | any) =>
  defHttp.post<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhH/listPushData', params });
// /mf/fh/mfCarplanFhH/pushdata
export const pushdata = (params?: CarplanFh | any) =>
  defHttp.post<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhH/pushdata', params });


export const getCarInfoByCarNo = (params?: CarplanFh | any) =>
  defHttp.get<CarplanFh>({ url: adminPath + '/mf/fh/mfCarplanFhH/getCarInfoByCarNo', params });