<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'wms/so:pallet:wmsPalletSendRds:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsWmsSoPalletWmsPalletSendRdsForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { WmsPalletSendRds, wmsPalletSendRdsSave, wmsPalletSendRdsForm } from '/@/api/wms/so/pallet/wmsPalletSendRds';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('wms/so.pallet.wmsPalletSendRds');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<WmsPalletSendRds>({} as WmsPalletSendRds);

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增托盘发出记录') : t('编辑托盘发出记录'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('托盘码'),
      field: 'cbatch',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      required: true,
    },
    {
      label: t('发货计划明细ID'),
      field: 'carplanCid',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
    },
    {
      label: t('车次'),
      field: 'carDjno',
      component: 'Input',
      componentProps: {
        maxlength: 50,
      },
    },
    {
      label: t('发货单号'),
      field: 'fhDjno',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('车牌号'),
      field: 'carNo',
      component: 'Input',
      componentProps: {
        maxlength: 50,
      },
    },
    {
      label: t('客户编码'),
      field: 'cusCode',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('客户名称'),
      field: 'cusName',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('存货编码'),
      field: 'invCode',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      required: true,
    },
    {
      label: t('存货名称'),
      field: 'invName',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
      required: true,
    },
    {
      label: t('规格型号'),
      field: 'invStd',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('单位'),
      field: 'unitName',
      component: 'Input',
      componentProps: {
        maxlength: 20,
      },
    },
    {
      label: t('合同号'),
      field: 'contract',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('发出仓库'),
      field: 'whName',
      component: 'Input',
      componentProps: {
        maxlength: 50,
      },
      required: true,
    },
    {
      label: t('发出重量'),
      field: 'fweight',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('发出人'),
      field: 'createByName',
      component: 'Input',
      componentProps: {
        maxlength: 50,
      },
      required: true,
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await wmsPalletSendRdsForm(data);
    record.value = (res.wmsPalletSendRds || {}) as WmsPalletSendRds;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await wmsPalletSendRdsSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
