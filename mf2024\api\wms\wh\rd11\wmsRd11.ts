/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WmsRd11 extends BasicModel<WmsRd11> {
  djno?: string; // 入库单号
  invCode?: string; // 存货编码
  appsid?: string; // 申请单子表ID
  appid?: string; // 申请单ID
  ccode?: string; // 申请单号
  ddate?: string; // 入库日期
  whCode?: string; // 仓库
  createByName?: string; // 创建人
  updateByName?: string; // 修改人
  iqty?: number; // 数量
  inum?: number; // 件数
  ichangeRate?: number; // 换算率
  childList?: any[]; // 子表列表
}

export interface WmsRds11 extends BasicModel<WmsRds11> {
  parentId?: string; // 父ID
  iqty?: number; // 数量
  inum?: number; // 件数
  cbatch?: string; // 批次
  posCode?: string; // 货位
  ichangeRate?: number; // 换算率
}

export const wmsRd11List = (params?: WmsRd11 | any) =>
  defHttp.get<WmsRd11>({ url: adminPath + '/wms/wh/rd11/wmsRd11/list', params });

export const wmsRd11ListData = (params?: WmsRd11 | any) =>
  defHttp.post<Page<WmsRd11>>({ url: adminPath + '/wms/wh/rd11/wmsRd11/listData', params });

export const wmsRd11subListData = (params?: WmsRd11 | any) =>
  defHttp.get<WmsRd11>({ url: adminPath + '/wms/wh/rd11/wmsRd11/subListData', params });

export const wmsRd11Form = (params?: WmsRd11 | any) =>
  defHttp.get<WmsRd11>({ url: adminPath + '/wms/wh/rd11/wmsRd11/form', params });

export const wmsRd11Save = (params?: any, data?: WmsRd11 | any) =>
  defHttp.postJson<WmsRd11>({ url: adminPath + '/wms/wh/rd11/wmsRd11/save', params, data });

export const wmsRd11Delete = (params?: WmsRd11 | any) =>
  defHttp.get<WmsRd11>({ url: adminPath + '/wms/wh/rd11/wmsRd11/delete', params });
