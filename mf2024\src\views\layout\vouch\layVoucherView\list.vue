<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
        <span> （{{ code + '-' + name }}） </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'layout:edit'">
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ code: record.code })">
          {{ record.name }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerModal" @success="handleSuccess" />
    <TableTabs @register="registerDrawer2" @success="handleSuccess" />
    <FormTabs @register="registerDrawer3" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherViewList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { layVoucherViewDelete, layVoucherViewListData } from '../../../../api/layout/vouch/layVoucherView';
  import { layVoucherViewDisable, layVoucherViewEnable } from '../../../../api/layout/vouch/layVoucherView';
  import { useModal } from '/@/components/Modal';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import TableTabs from './tableTabs.vue';
  import FormTabs from './formTabs.vue';

  const { t } = useI18n('layout.layVoucherView');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: router.currentRoute.value.meta.title || t('页面视图管理'),
  };

  let code = ref<string>(router.currentRoute.value.query.code as string);
  let name = ref<string>(router.currentRoute.value.query.name as string);
  let typeCode = ref<string>(router.currentRoute.value.query.typeCode as string);

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('布局名称'),
        field: 'name',
        component: 'Input',
      },
      {
        label: t('布局类型'),
        field: 'layType',
        component: 'Select',
        componentProps: {
          dictType: 'lay_type',
          allowClear: true,
        },
      },
      {
        label: t('基础单据'),
        field: 'vouchCode',
        component: 'Input',
      },
      {
        label: t('状态'),
        field: 'status',
        component: 'Select',
        componentProps: {
          dictType: 'sys_status',
          allowClear: true,
          onChange: handleSuccess,
        },
      },
      {
        label: t('布局描述'),
        field: 'remarks',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('布局标志'),
      dataIndex: 'code',
      key: 'a.code',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('单据标志'),
      dataIndex: 'vouchCode',
      key: 'a.vouch_code',
      sorter: true,
      width: 130,
      align: 'center',
      ifShow: false,
    },
    {
      title: t('布局名称'),
      dataIndex: 'name',
      key: 'a.name',
      sorter: true,
      width: 230,
      align: 'center',
      slot: 'firstColumn',
    },
    {
      title: t('布局类型'),
      dataIndex: 'layType',
      key: 'a.lay_type',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'lay_type',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'a.status',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_status',
    },
    {
      title: t('更新时间'),
      dataIndex: 'updateDate',
      key: 'a.update_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('布局描述'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑页面视图'),
        onClick: handleForm.bind(this, { code: record.code }),
        auth: 'layout:edit',
      },
      // {
      //   icon: 'ant-design:stop-outlined',
      //   color: 'error',
      //   title: t('停用页面视图'),
      //   popConfirm: {
      //     title: t('是否确认停用页面视图'),
      //     confirm: handleDisable.bind(this, { code: record.code }),
      //   },
      //   auth: 'layout:edit',
      //   ifShow: () => record.status === '0',
      // },
      // {
      //   icon: 'ant-design:check-circle-outlined',
      //   color: 'success',
      //   title: t('启用页面视图'),
      //   popConfirm: {
      //     title: t('是否确认启用页面视图'),
      //     confirm: handleEnable.bind(this, { code: record.code }),
      //   },
      //   auth: 'layout:edit',
      //   ifShow: () => record.status === '2',
      // },
      {
        icon: 'simple-line-icons:magnifier',
        color: 'error',
        title: t('查询方案'),
        onClick: handleQuery.bind(this, record),
        auth: 'layout:edit',
        ifShow: () => record.layType === '0',
      },
      {
        icon: 'ant-design:table-outlined',
        color: 'error',
        title: t('表格配置'),
        onClick: handleTable.bind(this, record),
        auth: 'layout:edit',
        ifShow: () => record.layType === '0',
      },
      {
        icon: 'ant-design:insert-row-left-outlined',
        color: 'error',
        title: t('表单配置'),
        onClick: handleFormTabs.bind(this, record),
        auth: 'layout:edit',
        ifShow: () => record.layType === '1',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除页面视图'),
        popConfirm: {
          title: t('是否确认删除页面视图'),
          confirm: handleDelete.bind(this, { code: record.code }),
        },
        auth: 'layout:edit',
      },
    ],
  };

  const [registerModal, { openModal }] = useModal();
  const [registerDrawer2, { openDrawer: openDrawer2 }] = useDrawer();
  const [registerDrawer3, { openDrawer: openDrawer3 }] = useDrawer();
  const [registerTable, { reload, getForm }] = useTable({
    api: layVoucherViewListData,
    beforeFetch: (params) => {
      params.vouchCode = code.value || 'unknown';
      // params.name = name.value || 'unknown';
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    record.vouchCode = code.value;
    openModal(true, record);
  }

  async function handleDisable(record: Recordable) {
    const res = await layVoucherViewDisable(record);
    showMessage(res.message);
    handleSuccess();
  }

  async function handleEnable(record: Recordable) {
    const res = await layVoucherViewEnable(record);
    showMessage(res.message);
    handleSuccess();
  }

  async function handleDelete(record: Recordable) {
    const res = await layVoucherViewDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  // function handleGoPage(record: Recordable) {
  //   router.push({
  //     path: '/layout/vouch/layVoucherView/list',
  //     query: {
  //       code: record.code,
  //       name: record.name,
  //     },
  //   });
  // }
  // 查询方案弹窗
  function handleQuery(record: Recordable) {
    // openModal(true, record);
    record.vouchCode = code.value;
    record.vouchName = name.value;
    router.push({
      path: '/layout/list/layVoucherViewListQuery/list',
      query: {
        ...record,
      },
    });
  }

  // 表格弹窗
  function handleTable(record: Recordable) {
    record.typeCode = typeCode.value
    record.typeName = name.value
    openDrawer2(true, record);
  }
  // 表单弹窗
  function handleFormTabs(record: Recordable) {
    record.typeCode = typeCode.value
    record.typeName = name.value
    openDrawer3(true, record);
  }

  function handleSuccess() {
    reload();
  }
</script>
