<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'billmanager:u8:contract:cmContractB:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ strcontractid: record.strcontractid })">
          {{ record.strcontractgrp }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsBillmanagerU8ContractCmContractBList">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { cmContractBDelete, cmContractBListData } from '/@/api/billmanager/u8/contract/cmContractB';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('billmanager.u8.contract.cmContractB');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('合同管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('合同分组编码'),
        field: 'strcontractgrp',
        component: 'Input',
      },
      {
        label: t('合同类型编码'),
        field: 'strcontracttype',
        component: 'Input',
      },
      {
        label: t('合同性质'),
        field: 'strcontractkind',
        component: 'Input',
      },
      {
        label: t('合同名称'),
        field: 'strcontractname',
        component: 'Input',
      },
      {
        label: t('对方单位'),
        field: 'strbisectionunit',
        component: 'Input',
      },
      {
        label: t('所属主合同编码'),
        field: 'strparentid',
        component: 'Input',
      },
      {
        label: t('保修期'),
        field: 'strrepair',
        component: 'Input',
      },
      {
        label: t('对方负责人'),
        field: 'strbisectionperson',
        component: 'Input',
      },
      {
        label: t('合同签定日期'),
        field: 'strcontractorderdate',
        component: 'Input',
      },
      {
        label: t('合同开始日期'),
        field: 'strcontractstartdate',
        component: 'Input',
      },
      {
        label: t('合同结束日期'),
        field: 'strcontractenddate',
        component: 'Input',
      },
      {
        label: t('合同描述'),
        field: 'strcontractdesc',
        component: 'Input',
      },
      {
        label: t('质保金比例'),
        field: 'dblmassassurescale',
        component: 'Input',
      },
      {
        label: t('质保金额度'),
        field: 'dblmassassure',
        component: 'Input',
      },
      {
        label: t('cdefine1'),
        field: 'cdefine1',
        component: 'Input',
      },
      {
        label: t('cdefine2'),
        field: 'cdefine2',
        component: 'Input',
      },
      {
        label: t('cdefine3'),
        field: 'cdefine3',
        component: 'Input',
      },
      {
        label: t('cdefine4'),
        field: 'cdefine4',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('cdefine5'),
        field: 'cdefine5',
        component: 'Input',
      },
      {
        label: t('cdefine6'),
        field: 'cdefine6',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('cdefine7'),
        field: 'cdefine7',
        component: 'Input',
      },
      {
        label: t('cdefine8'),
        field: 'cdefine8',
        component: 'Input',
      },
      {
        label: t('cdefine9'),
        field: 'cdefine9',
        component: 'Input',
      },
      {
        label: t('cdefine10'),
        field: 'cdefine10',
        component: 'Input',
      },
      {
        label: t('cdefine11'),
        field: 'cdefine11',
        component: 'Input',
      },
      {
        label: t('cdefine12'),
        field: 'cdefine12',
        component: 'Input',
      },
      {
        label: t('cdefine13'),
        field: 'cdefine13',
        component: 'Input',
      },
      {
        label: t('cdefine14'),
        field: 'cdefine14',
        component: 'Input',
      },
      {
        label: t('cdefine15'),
        field: 'cdefine15',
        component: 'Input',
      },
      {
        label: t('cdefine16'),
        field: 'cdefine16',
        component: 'Input',
      },
      {
        label: t('创建人'),
        field: 'strsetupperson',
        component: 'Input',
      },
      {
        label: t('创建日期'),
        field: 'strsetupdate',
        component: 'Input',
      },
      {
        label: t('结案人'),
        field: 'strendcaseperson',
        component: 'Input',
      },
      {
        label: t('结案日期'),
        field: 'strendcasedate',
        component: 'Input',
      },
      {
        label: t('生效人'),
        field: 'strinureperson',
        component: 'Input',
      },
      {
        label: t('生效日期'),
        field: 'strinuredate',
        component: 'Input',
      },
      {
        label: t('部门编码'),
        field: 'strdeptid',
        component: 'Input',
      },
      {
        label: t('业务员编码'),
        field: 'strpersonid',
        component: 'Input',
      },
      {
        label: t('变更单号'),
        field: 'intvaryid',
        component: 'Input',
      },
      {
        label: t('变更原因码'),
        field: 'strvarycauseid',
        component: 'Input',
      },
      {
        label: t('变更日期'),
        field: 'dtvarydate',
        component: 'Input',
      },
      {
        label: t('变更申请人'),
        field: 'strvarypersonid',
        component: 'Input',
      },
      {
        label: t('变更生效人'),
        field: 'strvarypasspersonid',
        component: 'Input',
      },
      {
        label: t('期初标志'),
        field: 'intpre',
        component: 'Input',
      },
      {
        label: t('收支方向'),
        field: 'strway',
        component: 'Input',
      },
      {
        label: t('币种'),
        field: 'strcurrency',
        component: 'Input',
      },
      {
        label: t('汇率'),
        field: 'dblexchange',
        component: 'Input',
      },
      {
        label: t('变更制单人'),
        field: 'strvaryperson',
        component: 'Input',
      },
      {
        label: t('时间戳'),
        field: 'tstime',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('备用字段1'),
        field: 'strspare1',
        component: 'Input',
      },
      {
        label: t('备用字段2'),
        field: 'strspare2',
        component: 'Input',
      },
      {
        label: t('备用字段3'),
        field: 'strspare3',
        component: 'Input',
      },
      {
        label: t('备用'),
        field: 'strsource',
        component: 'Input',
      },
      {
        label: t('备用'),
        field: 'dbltotalcurrency',
        component: 'Input',
      },
      {
        label: t('备用'),
        field: 'dblexeccurrency',
        component: 'Input',
      },
      {
        label: t('备用'),
        field: 'dbltotalquantity',
        component: 'Input',
      },
      {
        label: t('备用'),
        field: 'dblexecquqantity',
        component: 'Input',
      },
      {
        label: t('业务类型'),
        field: 'cbustype',
        component: 'Input',
      },
      {
        label: t('运输方式'),
        field: 'csccode',
        component: 'Input',
      },
      {
        label: t('收付款计划'),
        field: 'cgatheringplan',
        component: 'Input',
      },
      {
        label: t('是否工作流控制'),
        field: 'iswfcontrolled',
        component: 'Input',
      },
      {
        label: t('控制流控制状态'),
        field: 'iverifystate',
        component: 'Input',
      },
      {
        label: t('返回工作流审核次数'),
        field: 'ireturncount',
        component: 'Input',
      },
      {
        label: t('审核标志'),
        field: 'intauditsymbol',
        component: 'Input',
      },
      {
        label: t('质保金计算方式'),
        field: 'czbjcomputemode',
        component: 'Input',
      },
      {
        label: t('质保金开始日期'),
        field: 'dtzbjstartdate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('质保金结束日期'),
        field: 'dtzbjenddate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('是否启用阶段'),
        field: 'busestage',
        component: 'Input',
      },
      {
        label: t('阶段组编码'),
        field: 'cstagegroupcode',
        component: 'Input',
      },
      {
        label: t('制单时间'),
        field: 'dtcreatetime',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('修改时间'),
        field: 'dtmodifytime',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('修改时间'),
        field: 'dtmodifydate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('生效时间'),
        field: 'dteffecttime',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('修改人'),
        field: 'cmodifer',
        component: 'Input',
      },
      {
        label: t('变更单制单时间'),
        field: 'dtvarycreatedate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('变更单制单时间'),
        field: 'dtvarycreatetime',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('变更单修改时间'),
        field: 'dtvarymodifytime',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('变更单修改日期'),
        field: 'dtvarymodifydate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('变更单生效时间'),
        field: 'dtvaryeffecttime',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('变更单修改人'),
        field: 'cvarymodifer',
        component: 'Input',
      },
      {
        label: t('合同状态'),
        field: 'istatus',
        component: 'Input',
      },
      {
        label: t('时效控制方式'),
        field: 'intexeccontroltype',
        component: 'Input',
      },
      {
        label: t('时效控制环节'),
        field: 'cexeccontrolvouch',
        component: 'Input',
      },
      {
        label: t('打印次数'),
        field: 'iprintcount',
        component: 'Input',
      },
      {
        label: t('贸易术语'),
        field: 'iincotermid',
        component: 'Input',
      },
      {
        label: t('最迟装船日期'),
        field: 'dlastladedate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('装运港'),
        field: 'csportcode',
        component: 'Input',
      },
      {
        label: t('转运港'),
        field: 'ctportcode',
        component: 'Input',
      },
      {
        label: t('目的港'),
        field: 'caportcode',
        component: 'Input',
      },
      {
        label: t('委托方编码'),
        field: 'centrustcode',
        component: 'Input',
      },
      {
        label: t('代理费计算方式'),
        field: 'cagencycalmethod',
        component: 'Input',
      },
      {
        label: t('代理费计费费率'),
        field: 'decagencyfeebillrates',
        component: 'Input',
      },
      {
        label: t('从量收费标准'),
        field: 'decspecficcharges',
        component: 'Input',
      },
      {
        label: t('代理费'),
        field: 'decagencyfees',
        component: 'Input',
      },
      {
        label: t('委托方收款金额'),
        field: 'decentgathering',
        component: 'Input',
      },
      {
        label: t('委托方收款日期'),
        field: 'dentgatheringdate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('联系人编码'),
        field: 'ccontactcode',
        component: 'Input',
      },
      {
        label: t('单据条码'),
        field: 'csysbarcode',
        component: 'Input',
      },
      {
        label: t('当前审核人'),
        field: 'ccurrentauditor',
        component: 'Input',
      },
      {
        label: t('来源类别'),
        field: 'csourcetype',
        component: 'Input',
      },
      {
        label: t('商机编码'),
        field: 'ioppid',
        component: 'Input',
      },
      {
        label: t('商机主题'),
        field: 'coppcode',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('合同分组编码'),
      dataIndex: 'strcontractgrp',
      key: 'a.strcontractgrp',
      sorter: true,
      width: 230,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('合同类型编码'),
      dataIndex: 'strcontracttype',
      key: 'a.strcontracttype',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('合同性质'),
      dataIndex: 'strcontractkind',
      key: 'a.strcontractkind',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('合同名称'),
      dataIndex: 'strcontractname',
      key: 'a.strcontractname',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('对方单位'),
      dataIndex: 'strbisectionunit',
      key: 'a.strbisectionunit',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('所属主合同编码'),
      dataIndex: 'strparentid',
      key: 'a.strparentid',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('保修期'),
      dataIndex: 'strrepair',
      key: 'a.strrepair',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('对方负责人'),
      dataIndex: 'strbisectionperson',
      key: 'a.strbisectionperson',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('合同签定日期'),
      dataIndex: 'strcontractorderdate',
      key: 'a.strcontractorderdate',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('合同开始日期'),
      dataIndex: 'strcontractstartdate',
      key: 'a.strcontractstartdate',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('合同结束日期'),
      dataIndex: 'strcontractenddate',
      key: 'a.strcontractenddate',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('合同描述'),
      dataIndex: 'strcontractdesc',
      key: 'a.strcontractdesc',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('质保金比例'),
      dataIndex: 'dblmassassurescale',
      key: 'a.dblmassassurescale',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('质保金额度'),
      dataIndex: 'dblmassassure',
      key: 'a.dblmassassure',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('cdefine1'),
      dataIndex: 'cdefine1',
      key: 'a.cdefine1',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine2'),
      dataIndex: 'cdefine2',
      key: 'a.cdefine2',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine3'),
      dataIndex: 'cdefine3',
      key: 'a.cdefine3',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine4'),
      dataIndex: 'cdefine4',
      key: 'a.cdefine4',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('cdefine5'),
      dataIndex: 'cdefine5',
      key: 'a.cdefine5',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('cdefine6'),
      dataIndex: 'cdefine6',
      key: 'a.cdefine6',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('cdefine7'),
      dataIndex: 'cdefine7',
      key: 'a.cdefine7',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('cdefine8'),
      dataIndex: 'cdefine8',
      key: 'a.cdefine8',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine9'),
      dataIndex: 'cdefine9',
      key: 'a.cdefine9',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine10'),
      dataIndex: 'cdefine10',
      key: 'a.cdefine10',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine11'),
      dataIndex: 'cdefine11',
      key: 'a.cdefine11',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine12'),
      dataIndex: 'cdefine12',
      key: 'a.cdefine12',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine13'),
      dataIndex: 'cdefine13',
      key: 'a.cdefine13',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine14'),
      dataIndex: 'cdefine14',
      key: 'a.cdefine14',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine15'),
      dataIndex: 'cdefine15',
      key: 'a.cdefine15',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('cdefine16'),
      dataIndex: 'cdefine16',
      key: 'a.cdefine16',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('创建人'),
      dataIndex: 'strsetupperson',
      key: 'a.strsetupperson',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('创建日期'),
      dataIndex: 'strsetupdate',
      key: 'a.strsetupdate',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('结案人'),
      dataIndex: 'strendcaseperson',
      key: 'a.strendcaseperson',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('结案日期'),
      dataIndex: 'strendcasedate',
      key: 'a.strendcasedate',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('生效人'),
      dataIndex: 'strinureperson',
      key: 'a.strinureperson',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('生效日期'),
      dataIndex: 'strinuredate',
      key: 'a.strinuredate',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('部门编码'),
      dataIndex: 'strdeptid',
      key: 'a.strdeptid',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('业务员编码'),
      dataIndex: 'strpersonid',
      key: 'a.strpersonid',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('变更单号'),
      dataIndex: 'intvaryid',
      key: 'a.intvaryid',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('变更原因码'),
      dataIndex: 'strvarycauseid',
      key: 'a.strvarycauseid',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('变更日期'),
      dataIndex: 'dtvarydate',
      key: 'a.dtvarydate',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('变更申请人'),
      dataIndex: 'strvarypersonid',
      key: 'a.strvarypersonid',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('变更生效人'),
      dataIndex: 'strvarypasspersonid',
      key: 'a.strvarypasspersonid',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('期初标志'),
      dataIndex: 'intpre',
      key: 'a.intpre',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('收支方向'),
      dataIndex: 'strway',
      key: 'a.strway',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('币种'),
      dataIndex: 'strcurrency',
      key: 'a.strcurrency',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('汇率'),
      dataIndex: 'dblexchange',
      key: 'a.dblexchange',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('变更制单人'),
      dataIndex: 'strvaryperson',
      key: 'a.strvaryperson',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('时间戳'),
      dataIndex: 'tstime',
      key: 'a.tstime',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('备用字段1'),
      dataIndex: 'strspare1',
      key: 'a.strspare1',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('备用字段2'),
      dataIndex: 'strspare2',
      key: 'a.strspare2',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('备用字段3'),
      dataIndex: 'strspare3',
      key: 'a.strspare3',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('备用'),
      dataIndex: 'strsource',
      key: 'a.strsource',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('备用'),
      dataIndex: 'dbltotalcurrency',
      key: 'a.dbltotalcurrency',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('备用'),
      dataIndex: 'dblexeccurrency',
      key: 'a.dblexeccurrency',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('备用'),
      dataIndex: 'dbltotalquantity',
      key: 'a.dbltotalquantity',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('备用'),
      dataIndex: 'dblexecquqantity',
      key: 'a.dblexecquqantity',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('业务类型'),
      dataIndex: 'cbustype',
      key: 'a.cbustype',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('运输方式'),
      dataIndex: 'csccode',
      key: 'a.csccode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('收付款计划'),
      dataIndex: 'cgatheringplan',
      key: 'a.cgatheringplan',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('是否工作流控制'),
      dataIndex: 'iswfcontrolled',
      key: 'a.iswfcontrolled',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('控制流控制状态'),
      dataIndex: 'iverifystate',
      key: 'a.iverifystate',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('返回工作流审核次数'),
      dataIndex: 'ireturncount',
      key: 'a.ireturncount',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('审核标志'),
      dataIndex: 'intauditsymbol',
      key: 'a.intauditsymbol',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('质保金计算方式'),
      dataIndex: 'czbjcomputemode',
      key: 'a.czbjcomputemode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('质保金开始日期'),
      dataIndex: 'dtzbjstartdate',
      key: 'a.dtzbjstartdate',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('质保金结束日期'),
      dataIndex: 'dtzbjenddate',
      key: 'a.dtzbjenddate',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('是否启用阶段'),
      dataIndex: 'busestage',
      key: 'a.busestage',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('阶段组编码'),
      dataIndex: 'cstagegroupcode',
      key: 'a.cstagegroupcode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('制单时间'),
      dataIndex: 'dtcreatetime',
      key: 'a.dtcreatetime',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('修改时间'),
      dataIndex: 'dtmodifytime',
      key: 'a.dtmodifytime',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('修改时间'),
      dataIndex: 'dtmodifydate',
      key: 'a.dtmodifydate',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('生效时间'),
      dataIndex: 'dteffecttime',
      key: 'a.dteffecttime',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('修改人'),
      dataIndex: 'cmodifer',
      key: 'a.cmodifer',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('变更单制单时间'),
      dataIndex: 'dtvarycreatedate',
      key: 'a.dtvarycreatedate',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('变更单制单时间'),
      dataIndex: 'dtvarycreatetime',
      key: 'a.dtvarycreatetime',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('变更单修改时间'),
      dataIndex: 'dtvarymodifytime',
      key: 'a.dtvarymodifytime',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('变更单修改日期'),
      dataIndex: 'dtvarymodifydate',
      key: 'a.dtvarymodifydate',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('变更单生效时间'),
      dataIndex: 'dtvaryeffecttime',
      key: 'a.dtvaryeffecttime',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('变更单修改人'),
      dataIndex: 'cvarymodifer',
      key: 'a.cvarymodifer',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('合同状态'),
      dataIndex: 'istatus',
      key: 'a.istatus',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('时效控制方式'),
      dataIndex: 'intexeccontroltype',
      key: 'a.intexeccontroltype',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('时效控制环节'),
      dataIndex: 'cexeccontrolvouch',
      key: 'a.cexeccontrolvouch',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('打印次数'),
      dataIndex: 'iprintcount',
      key: 'a.iprintcount',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('贸易术语'),
      dataIndex: 'iincotermid',
      key: 'a.iincotermid',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('最迟装船日期'),
      dataIndex: 'dlastladedate',
      key: 'a.dlastladedate',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('装运港'),
      dataIndex: 'csportcode',
      key: 'a.csportcode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('转运港'),
      dataIndex: 'ctportcode',
      key: 'a.ctportcode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('目的港'),
      dataIndex: 'caportcode',
      key: 'a.caportcode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('委托方编码'),
      dataIndex: 'centrustcode',
      key: 'a.centrustcode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('代理费计算方式'),
      dataIndex: 'cagencycalmethod',
      key: 'a.cagencycalmethod',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('代理费计费费率'),
      dataIndex: 'decagencyfeebillrates',
      key: 'a.decagencyfeebillrates',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('从量收费标准'),
      dataIndex: 'decspecficcharges',
      key: 'a.decspecficcharges',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('代理费'),
      dataIndex: 'decagencyfees',
      key: 'a.decagencyfees',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('委托方收款金额'),
      dataIndex: 'decentgathering',
      key: 'a.decentgathering',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('委托方收款日期'),
      dataIndex: 'dentgatheringdate',
      key: 'a.dentgatheringdate',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('联系人编码'),
      dataIndex: 'ccontactcode',
      key: 'a.ccontactcode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('单据条码'),
      dataIndex: 'csysbarcode',
      key: 'a.csysbarcode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('当前审核人'),
      dataIndex: 'ccurrentauditor',
      key: 'a.ccurrentauditor',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('来源类别'),
      dataIndex: 'csourcetype',
      key: 'a.csourcetype',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('商机编码'),
      dataIndex: 'ioppid',
      key: 'a.ioppid',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('商机主题'),
      dataIndex: 'coppcode',
      key: 'a.coppcode',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑合同'),
        onClick: handleForm.bind(this, { strcontractid: record.strcontractid }),
        auth: 'billmanager:u8:contract:cmContractB:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除合同'),
        popConfirm: {
          title: t('是否确认删除合同'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'billmanager:u8:contract:cmContractB:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload }] = useTable({
    api: cmContractBListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { strcontractid: record.strcontractid };
    const res = await cmContractBDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
