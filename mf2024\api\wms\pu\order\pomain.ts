/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { U8DefHEntity, U8DefBEntity } from '/@/api/sys/u8.ts';

const { adminPath } = useGlobSetting();

export interface Pomain extends BasicModel<Pomain> {
  cpoid?: string; // 订单号
  ddate?: string; // 订单日期
  venCode?: string; // 供应商编码
  venName?: string; // 供应商名称
  depCode?: string; // 部门编码
  depName?: string; // 部门名称
  whCode?: string; // 仓库
  whName?: string; // 仓库名称
  htno?: string; // 合同号
  closeBy?: string; // 关闭人
  verifBy?: string; // 审核人
  extend?: U8DefHEntity; //自定义项
  childList?: any[]; // 子表列表
}

export interface podetails extends BasicModel<Pomain> {
  parentId?: string; // 父表ID
  parent?: Pomain; //父类
  ivouchrowno?: Number; // 行号
  bweight?: string; // 是否称重
  venName?: string; // 供应商名称
  invCode?: string; // 存货编码
  invName?: string; // 存货名称
  invStd?: string; // 规格型号
  unitCode?: string; // 单位编码
  unitName?: string; // 单位名称
  iqty?: Number; // 订单数量
  cbdefine1?: Number; // 自然数
  inum?: Number; // 件数
  planDate?: string; // 计划到货日期
  closeBy?: string; // 关闭人
  inQty?: Number; // 累计入库
  syInQty?: Number; // 剩余入库
  syNeedQty?: Number; // 剩余需求
  extend?: U8DefBEntity; //自定义项
}

export const pomainList = (params?: Pomain | any) =>
  defHttp.get<Pomain>({ url: adminPath + '/wms/pu/order/pomain/list', params });

export const pomainListData = (params?: Pomain | any) =>
  defHttp.post<Page<Pomain>>({ url: adminPath + '/wms/pu/order/pomain/listData', params });

export const pomainsubListData = (params?: Pomain | any) =>
  defHttp.get<Pomain>({ url: adminPath + '/wms/pu/order/pomain/subListData', params });

export const pomainForm = (params?: Pomain | any) =>
  defHttp.get<Pomain>({ url: adminPath + '/wms/pu/order/pomain/form', params });

export const pomainSave = (params?: any, data?: Pomain | any) =>
  defHttp.postJson<Pomain>({ url: adminPath + '/wms/pu/order/pomain/save', params, data });

export const pomainDelete = (params?: Pomain | any) =>
  defHttp.get<Pomain>({ url: adminPath + '/wms/pu/order/pomain/delete', params });
