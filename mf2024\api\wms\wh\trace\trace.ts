/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { U8DefHEntity, U8DefBEntity } from '/@/api/sys/u8.ts';

const { adminPath } = useGlobSetting();

export interface Trace extends BasicModel<Trace> {
  djno?: string; // 出库单据号
  ddate?: string; // 出库日期
  igty?: string; // 出库数量
  cbatch?: string; // 批次号
  maketime?: string; // 制单时间
  cmaker?: string; // 制单人
  carVenName?: string; // 运输单位
  cSoCode?: string; // 销售订单号
  soDate?: string; //订单日期
  cCusName?: string; // 客户名称
  htno?: string; // 合同号
}

export const batchTrace = (params?: Trace | any) =>
  defHttp.post<Trace>({ url: adminPath + '/wms/wh/trace/batchTrace', params });
