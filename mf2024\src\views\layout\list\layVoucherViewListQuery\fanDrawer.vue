<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer v-bind="$attrs" :showFooter="false" @register="registerDrawer" width="60%">
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ '查询栏目设计' }} </span>
    </template>
    <QueryCol :colpop="colpop" />
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherViewListQueryForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  // import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import QueryCol from '../layVoucherViewListQueryCol/list.vue';
  // import {
  //   LayVoucherViewListQuery,
  //   // layVoucherViewListQuerySave,
  //   // layVoucherViewListQueryForm,
  // } from '/@/api/layout/layVoucherViewListQuery';
  // import { layVoucherClsTreeData } from '/@/api/layout/layVoucherCls';

  // const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('layout.layVoucherViewListQuery');
  const { showMessage } = useMessage();
  // const record = ref<LayVoucherViewListQuery>({} as LayVoucherViewListQuery);
  const getTitle = computed(() => ({
    icon: 'ant-design:book-outlined',
    value: t('查询栏目管理'),
  }));

  let colpop = ref({});

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    colpop.value = data;
    // await resetFields();
    // const res = await layVoucherViewListQueryForm(data);
    // record.value = (res.layVoucherViewListQuery || {}) as LayVoucherViewListQuery;
    // record.value.__t = new Date().getTime();
    // setFieldsValue(record.value);
    // updateSchema([
    //   {
    //     field: 'code',
    //     componentProps: {
    //       disabled: !record.value.isNewRecord,
    //     },
    //   },
    // ]);
    setDrawerProps({ loading: false });
  });

  // async function handleSubmit() {
  //   try {
  //     const data = await validate();
  //     setDrawerProps({ confirmLoading: true });
  //     const params: any = {
  //       isNewRecord: record.value.isNewRecord,
  //       code: record.value.code,
  //     };
  //     // console.log('submit', params, data, record);
  //     const res = await layVoucherViewListQuerySave(params, data);
  //     showMessage(res.message);
  //     setTimeout(closeDrawer);
  //     emit('success', data);
  //   } catch (error: any) {
  //     if (error && error.errorFields) {
  //       showMessage(t('您填写的信息有误，请根据提示修正。'));
  //     }
  //     console.log('error', error);
  //   } finally {
  //     setDrawerProps({ confirmLoading: false });
  //   }
  // }
</script>
