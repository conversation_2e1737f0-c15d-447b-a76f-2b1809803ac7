import { useI18n } from '/@/hooks/web/useI18n';
import { BasicColumn, BasicTableProps, FormProps } from '/@/components/Table';
import { dispatchlistsListData } from '/@/api/wms/weighbridge/fh';

const { t } = useI18n('sys.empUser');

const modalProps = {
  title: t('销售发货管理'),
};

const searchForm: FormProps = {
  baseColProps: { lg: 6, md: 8 },
  labelWidth: 80,
  schemas: [
    {
      label: t('carType'),
      field: 'carType',
      component: 'Input',
      show: false,
    },
    {
      label: t('单据日期'),
      field: 'ddate',
      component: 'RangePicker',
    },
    {
      label: t('发货单号'),
      field: 'dlid.cdlcode',
      component: 'Input',
    },
    {
      label: t('合同号'),
      field: 'ccontractid',
      component: 'Input',
    },
    {
      label: t('客户名称'),
      field: 'dlid.ccusname',
      component: 'Input',
    },
    {
      label: t('存货编码'),
      field: 'cinvcode',
      component: 'Input',
    },
    {
      label: t('存货名称'),
      field: 'cinvname',
      component: 'Input',
    },
  ],
  fieldMapToTime: [['ddate', ['ddate_gte', 'ddate_lte']]],
};

const tableColumns: BasicColumn[] = [
  {
    title: t('发货单号'),
    dataIndex: 'dlid.cdlcode',
    key: 'dlid.cdlcode',
    sorter: true,
    width: 100,
  },
  {
    title: t('合同号'),
    dataIndex: 'ccontractid',
    key: 'dlid.cdlcode',
    sorter: true,
    width: 100,
  },
  {
    title: t('单据日期'),
    dataIndex: 'dlid.ddate',
    key: 'dlid.ddate',
    sorter: true,
    width: 100,
  },
  {
    title: t('客户名称'),
    dataIndex: 'dlid.ccusname',
    key: 'dlid.ccusname',
    sorter: true,
    width: 100,
  },
  {
    title: t('仓库名称'),
    dataIndex: 'cwhname',
    key: 'a.cwhname',
    sorter: true,
    width: 100,
  },
  {
    title: t('批次'),
    dataIndex: 'ibatch',
    key: 'a.ibatch',
    sorter: true,
    width: 80,
  },
  {
    title: t('存货编码'),
    dataIndex: 'cinvcode',
    key: 'a.cinvcode',
    sorter: true,
    width: 130,
  },
  {
    title: t('存货名称'),
    dataIndex: 'cinvname',
    key: 'a.cinvname',
    sorter: true,
    width: 230,
  },
  {
    title: t('规格型号'),
    dataIndex: 'cinvstd',
    key: 'a.cinvstd',
    sorter: true,
    width: 130,
  },
  {
    title: t('剩余发货数量'),
    dataIndex: 'qty',
    key: 'a.qty',
    sorter: true,
    width: 130,
    customRender: ({ record }) => {
      // 保留两位小数
      return record.qty ? Number(record.qty).toFixed(2) : '0.00';
    },
  },
];

const tableProps: BasicTableProps = {
  api: dispatchlistsListData,
  beforeFetch: (params) => {
    params['isAll'] = true;
    return params;
  },
  columns: tableColumns,
  formConfig: searchForm,
  rowKey: 'id',
};

export default {
  modalProps,
  tableProps,
  itemCode: 'cinvname',
  itemName: 'dlid.cdlcode',
  isShowCode: true,
};
