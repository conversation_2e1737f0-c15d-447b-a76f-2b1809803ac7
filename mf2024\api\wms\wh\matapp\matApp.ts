/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { U8DefHEntity, U8DefBEntity } from '/@/api/sys/u8.ts';

const { adminPath } = useGlobSetting();

export interface MatApp extends BasicModel<MatApp> {
  ccode?: string; // 单据号
  ddate?: string; // 单据日期
  crdCode?: string; // 出库类别
  crdName?: string; // 出库类别名称
  depCode?: string; // 部门编码
  depName?: string; // 部门名称
  closeBy?: string; // 关闭人
  verifBy?: string; // 审核人
  extend?: U8DefHEntity; //自定义项
  matAppsList?: any[]; // 子表列表
}

export interface MatApps extends BasicModel<MatApps> {
  parentId?: string; // 父表ID
  parent?: MatApp; //父类
  irowno?: Number; // 行号
  whCode?: string; // 仓库编码
  whName?: string; // 仓库
  invCode?: string; // 存货编码
  invName?: string; // 存货名称
  invStd?: string; // 规格型号
  unitCode?: string; // 单位编码
  unitName?: string; // 单位名称
  cbatch?: string; // 批次
  iqty?: Number; // 订单数量
  inum?: Number; // 件数
  planDate?: string; // 计划到货日期
  closeBy?: string; // 关闭人
  outQty?: Number; // 累计出库
  syOutQty?: Number; // 剩余出库
  extDefine3?: string; // 部门
  ichangeRate?: Number; // 换算率
  bfixExch?: string; // 单位组类别
  extend?: U8DefBEntity; //自定义项
}

export const matAppList = (params?: MatApp | any) =>
  defHttp.get<MatApp>({ url: adminPath + '/wms/wh/matapp/matApp/list', params });

export const matAppListData = (params?: MatApp | any) =>
  defHttp.post<Page<MatApp>>({ url: adminPath + '/wms/wh/matapp/matApp/listData', params });

export const matAppForm = (params?: MatApp | any) =>
  defHttp.get<MatApp>({ url: adminPath + '/wms/wh/matapp/matApp/form', params });

export const matAppSave = (params?: any, data?: MatApp | any) =>
  defHttp.postJson<MatApp>({ url: adminPath + '/wms/wh/matapp/matApp/save', params, data });

export const matAppDelete = (params?: MatApp | any) =>
  defHttp.get<MatApp>({ url: adminPath + '/wms/wh/matapp/matApp/delete', params });
