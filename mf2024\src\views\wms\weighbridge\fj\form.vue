<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'mf:fj:mfCarplanFjH:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsWmsWeighbridgeFjForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { CarplanFj,mfCarplanFjHForm , mfCarplanFjHSave} from '/@/api/wms/weighbridge/fj';
  const emit = defineEmits(['success', 'register']);
  const { t } = useI18n('sys.fj');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<CarplanFj>({} as CarplanFj);
  const getTitle = computed(() => ({
    icon: meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增废旧物资装车计划') : t('编辑废旧物资装车计划'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('ID'),
      field: 'id',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
      ifShow: false,
    },
    {
      label: t('基本信息'),
      field: 'info1',
      component: 'FormGroup',
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('到货车次'),
      field: 'djNo',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      dynamicDisabled: true,
    },
    {
      label: t('车牌号'),
      field: 'carNo',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
      required: true,
    },
    {
      label: t('计划发货日期'),
      field: 'planDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
      required: true,
    },
    {
      label: t('运输信息'),
      field: 'info2',
      component: 'FormGroup',
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('司机'),
      field: 'cdriver',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('司机电话'),
      field: 'driverPhone',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('押运员'),
      field: 'yyPerson',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('押运员电话'),
      field: 'yyPhone',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('运输单位'),
      field: 'carVenCode',
      fieldLabel: 'carVenName',
      // component: 'TreeSelect',
      // componentProps: {
      //   api: carVenTreeData,
      //   params: { isShowCode: '0', type: '1' },
      //   canSelectParent: false,
      //   allowClear: true,
      //   treeCheckable: true,
      // },
      component: 'ListSelect',
      componentProps: {
        selectType: 'carVenListSelect',
        checkbox: true,
      },
      required: true,
      colProps: { lg: 24, md: 24 },
    },

    {
      label: t('称重信息'),
      field: 'info4',
      component: 'FormGroup',
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('皮重'),
      field: 'pzWeight',
      component: 'InputNumber',
      componentProps: {
        maxlength: 100,
      },
      dynamicDisabled: true,
    },
    {
      label: t('毛重'),
      field: 'mzWeight',
      component: 'InputNumber',
      componentProps: {
        maxlength: 100,
      },
      dynamicDisabled: true,
    },
    {
      label: t('净重'),
      field: 'jzWeight',
      component: 'InputNumber',
      componentProps: {
        maxlength: 100,
      },
      dynamicDisabled: true,
    },
    {
      label: t('称重完成(毛重)'),
      field: 'wcDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
      dynamicDisabled: true,
    },
    {
      label: t('称重完成(皮重)'),
      field: 'pzDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
      dynamicDisabled: true,
    },
    {
      label: t('备注信息'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 500,
        rows: 2,
      },
      // slot: 'remarks',
      colProps: { lg: 24, md: 24 },
    },
    // {
    //   label: t('附件信息'),
    //   field: 'info5',
    //   component: 'FormGroup',
    //   colProps: { lg: 24, md: 24 },
    // },
    // {
    //   label: t(''),
    //   field: 'dataMap',
    //   component: 'Upload',
    //   componentProps: {
    //     loadTime: computed(() => record.value.__t),
    //     bizKey: computed(() => record.value.id),
    //     bizType: 'carplanFjH_image',
    //     uploadType: 'image',
    //     imageMaxWidth: -1,
    //     imageMaxHeight: -1,
    //   },
    //   colProps: { lg: 24, md: 24 },
    // },
  ];

  const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    autoSubmitOnEnter: true,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await mfCarplanFjHForm(data);
    record.value = (res.mfCarplanFjH || {}) as CarplanFj;
    setFieldsValue(record.value);
    setDrawerProps({ loading: false ,showOkBtn: record.value.cstatus != '3' && record.value.cstatus != '4' ? true: false});
    
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      const res = await mfCarplanFjHSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
