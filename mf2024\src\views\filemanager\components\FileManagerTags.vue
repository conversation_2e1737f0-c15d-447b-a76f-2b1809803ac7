<template>
  <span v-for="item in tags" :key="item.tagId" class="jeesite-filemanager-tags">
    <Tag :color="item.tagColor || '#ddd'" @click="handleClick(item)">
      {{ item.tagName }}
    </Tag>
  </span>
</template>
<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { Tag } from 'ant-design-vue';

  const props = defineProps({
    record: {
      type: Object as PropType<Recordable>,
    },
  });

  const emit = defineEmits(['click']);

  const tags = ref<any[]>([]);

  watch(
    () => props.record?.tagId,
    () => {
      tags.value = [];
      const row = props.record;
      if (row && row.tagId && row.tagId !== '' && row.tagName && row.tagName !== '') {
        let ids = row.tagId.split('|'),
          names = row.tagName.split('|'),
          colors = row.tagColor?.split('|');
        if (ids.length === names.length && ids.length === colors.length) {
          for (let i = 0; i < ids.length; i++) {
            tags.value.push({ tagId: ids[i], tagName: names[i], tagColor: colors[i] });
          }
        }
      }
    },
    {
      immediate: true,
    },
  );

  function handleClick(item: Recordable) {
    emit('click', item);
  }
</script>
<style lang="less">
  .jeesite-filemanager-tags {
    .ant-tag {
      border-radius: 15px;
      margin-left: 5px;
      font-size: 12px !important;
      cursor: pointer;
    }
  }
</style>
