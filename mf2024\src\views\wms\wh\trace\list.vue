<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <PageWrapper title="批次追溯管理" :contentFullHeight="true">
    <!-- 搜索条件，搜索头 -->
    <BasicForm @register="registerForm" @submit="handleSubmit" @reset="handleReset"/>
<!--  class="P-2 lg:flex" -->
    <div>
      <a-timeline>
        <List :data-source="dataSource" :pagination="paginationProp">
        <template #renderItem="{ item }">
          <!-- <ListItem> -->
            <a-timeline-item>
              <p class="text-xl">{{ item.maketime }}</p>
              <!-- <div v-for="(item2, index2) in item.expandedDataSource" :key="index2"> -->
                <!-- 占屏幕的80% 大屏幕的时候class="w-screen-xl" 小 -->
              <a-card title="销售出库">
                <ARow :gutter="24">
                  <ACol :span="6" class="mb-2">出库日期：{{ item.ddate }}</ACol>
                  <ACol :span="6" class="mb-2">数量：{{ item.iqty }} {{ item?.cComUnitName }}</ACol>
                  <ACol :span="6" class="mb-2">批次：{{ item.cbatch }}</ACol>
                  <ACol :span="6" class="mb-2">运输单位：{{ item.carVenName }}</ACol>
                  <ACol :span="6" class="mb-2">单据号：{{ item.djno }}</ACol>
                  <ACol :span="6" class="mb-2">车牌号：{{ item.carNo }}</ACol>
                  <ACol :span="6" class="mb-2">制单时间：{{ item.maketime }}</ACol>
                  <ACol :span="6" class="mb-2">制单人：{{ item.cmaker }}</ACol>
                </ARow>
              </a-card>
              <a-card title="销售订单" style="margin-top: 10px;">
                <ARow :gutter="24">
                  <ACol :span="6" class="mb-2">订单日期：{{ item.soDate }}</ACol>
                  <ACol :span="6" class="mb-2">订单号：{{ item.cSOCode }}</ACol>
                  <ACol :span="6" class="mb-2">合同号：{{ item.htno }}</ACol>
                  <ACol :span="6" class="mb-2">客户：{{ item.cCusName }}</ACol>
                  <ACol :span="6" class="mb-2">客户编码：{{ item.cCusCode }}</ACol>
                </ARow>
              </a-card>
              <a-card title="存货信息" style="margin-top: 10px;">
                <ARow :gutter="24">
                  <ACol :span="6">存货名称：{{ item.cinvname }}</ACol>
                  <ACol :span="6">存货编码：{{ item.cinvcode }}</ACol>
                  <ACol :span="6">规格型号：{{ item.cinvstd }}</ACol>
                </ARow>
              </a-card>
              <!-- </div> -->
            </a-timeline-item>
            <!-- </ListItem> -->
          </template>
        </List>
      </a-timeline>
      <!-- <InputForm @register="registerDrawer" @success="handleSuccess" /> -->
    </div>
  </PageWrapper>

</template>
<script lang="ts">
  import { Timeline, TimelineItem, Card, List, Row, Col } from 'ant-design-vue';
  import 'ant-design-vue/dist/reset.css';
  // 引入样式
  export default defineComponent({
    name: 'ViewsWmsCbatchList',
    components: {
      ATimeline: Timeline,
      ATimelineItem: TimelineItem,
      List: List,
      ACard: Card,
      ARow: Row,
      ACol: Col,
    },
  });
</script>
<script lang="ts" setup>
  import { defineComponent, watch, nextTick, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { router } from '/@/router';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { PageWrapper } from '/@/components/Page';
  import { batchTrace } from '/@/api/wms/wh/trace/trace';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { tpTypeEnum } from '/@/enums/defEnum';

  // 当form

  const { showMessage } = useMessage();
  //分页相关
  const page = ref(1);
  const pageSize = ref(2);
  const total = ref(0);
  const paginationProp = ref({
    showSizeChanger: false,
    showQuickJumper: true,
    pageSize,
    current: page,
    total,
    showTotal: (total: number) => `总 ${total} 条`,
    onChange: pageChange,
    onShowSizeChange: pageSizeChange,
  });
  function pageChange(p: number, pz: number) {
    page.value = p;
    pageSize.value = pz;
    handleSubmitSync();
  }
  function pageSizeChange(_current, size: number) {
    pageSize.value = size;
    handleSubmitSync();
  }
  async function fetch(p = {}) {
    const { api, params } = props;
    if (api && isFunction(api)) {
      const res = await api({ ...params, page: page.value, pageSize: pageSize.value, ...p });
      data.value = res.items;
      total.value = res.total;
    }
  }

  // 写一个方法把 2023-05-09 00:00:00 转换成 2023-05-09
  const dataSource = ref([]);

  const { t } = useI18n('bas.pos.basPosition');
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: router.currentRoute.value.meta.title || t('批次追溯管理'),
  };
  const schemas: FormSchema[] = [
    {
      label: t('批次追溯'),
      field: 'ctype',
      component: 'RadioGroup',
      componentProps: {
        dictType: 'mf_cbatch_zs',
        allowClear: true,
      },
      required: true,
    },
    {
      label: t('批次'),
      field: 'cbatch',
      component: 'Input',
      required: true,
    },
  ];
  const [registerForm, { validate }] = useForm({
    labelWidth: 100,
    schemas: schemas,
    baseColProps: { lg: 8, md: 24 },
    showActionButtonGroup: true,
    submitButtonOptions: {
      text: '开始追溯',
    },
  });
  async function handleSubmit() {
    try {
      const data = await validate();
      if(data.ctype == '0'){
        // data.cbatch  加上前缀
        data.cbatch = tpTypeEnum.tpType + data.cbatch;
      }
      data.pageNo = 1;
      data.pageSize = pageSize.value;
      fetchData(data);
    } catch (error: any) {
      dataSource.value = [];
      total.value = 0;
      page.value = 1;
      console.log('error', error);
    } 
  }
  async function handleSubmitSync() {
    try {
      const data = await validate();
      if(data.ctype == '0'){
        // data.cbatch  加上前缀
        data.cbatch = tpTypeEnum.tpType + data.cbatch;
      }
      data.pageNo = page.value;
      data.pageSize = pageSize.value;
      fetchData(data);
    } catch (error: any) {
      dataSource.value = [];
      total.value = 0;
      page.value = 1;
      console.log('error', error);
    }
  }
  async function fetchData(data: any) {
    try {
      const res = await batchTrace({ ...data });
      dataSource.value = res.list;
      total.value = res.count;
    } catch (error) {
      console.error('Error fetching data:', error);
      dataSource.value = [];
      total.value = 0;
    }
  }
  function handleReset() {
    dataSource.value = [];
    total.value = 0;
    page.value = 1;
    pageSize.value = 2;
  }
</script>
<style lang="less">
  .jeesite-basic-table {
    .ant-table-expand-icon-col {
      width: 15px;
    }
    .ant-table-row .ant-table-cell:first-child {
      text-align: center;
    }
    .ant-table-tbody .ant-table-wrapper:only-child {
      padding: 20px;
    }
  }

</style>
<style>
  :deep(.ant-list-pagination) {
    position: fixed;
  }
</style>
