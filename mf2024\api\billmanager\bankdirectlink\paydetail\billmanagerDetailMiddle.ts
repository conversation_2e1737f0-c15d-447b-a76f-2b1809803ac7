/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface BillmanagerDetailMiddle extends BasicModel<BillmanagerDetailMiddle> {
  requestSn?: string; // 请求序列码
  accNo1?: string; // 本方账号
  currCod?: string; // 币种
  accName?: string; // 本方账号名称
  accOrgan?: string; // 本方账号开户机构
  accState?: string; // 本方账号状态
  intr?: number; // 利率
  postStr?: string; // 定位串
  flag?: string; // 保留字段
  fileLocStr?: string; // 生成文件定位串
  tranDate?: string; // 交易日期
  tranTime?: string; // 交易时间
  creTyp?: string; // 凭证种类
  creNo?: string; // 凭证号码
  message?: string; // 摘要
  amt?: number; // 发生额
  amt1?: number; // 余额
  flag1?: string; // 借贷标志
  accNo2?: string; // 对方账号
  accName1?: string; // 对方户名
  flag2?: string; // 交易钞汇标志
  tranFlow?: string; // 交易流水号
  bflow?: string; // 企业支付流水号
  detNo?: string; // 活存账户明细号
  det?: string; // 备注
  realTranDate?: string; // 实际交易日期
  rltvAccNo?: string; // 关联账号
  cadBankNm?: string; // 对方账户开户行名称
  ovrlsttnTrckno?: string; // 全局跟踪号
  exoStmRyRmrk?: string; // 外系统支付备注
  u8_djno?: string; // U8单据号
  pushState?: string; // 推送状态
  pushDate?: string; // 推送时间
  pushResult?: string; // 推送结果
}

export const billmanagerDetailMiddleList = (params?: BillmanagerDetailMiddle | any) =>
  defHttp.get<BillmanagerDetailMiddle>({ url: adminPath + '/billmanager/bankdirectlink/paydetail/billmanagerDetailMiddle/list', params });

export const billmanagerDetailMiddleListData = (params?: BillmanagerDetailMiddle | any) =>
  defHttp.post<Page<BillmanagerDetailMiddle>>({ url: adminPath + '/billmanager/bankdirectlink/paydetail/billmanagerDetailMiddle/listData', params });

export const billmanagerDetailMiddleForm = (params?: BillmanagerDetailMiddle | any) =>
  defHttp.get<BillmanagerDetailMiddle>({ url: adminPath + '/billmanager/bankdirectlink/paydetail/billmanagerDetailMiddle/form', params });

export const billmanagerDetailMiddleSave = (params?: any, data?: BillmanagerDetailMiddle | any) =>
  defHttp.postJson<BillmanagerDetailMiddle>({ url: adminPath + '/billmanager/bankdirectlink/paydetail/billmanagerDetailMiddle/save', params, data });

export const billmanagerDetailMiddleDelete = (params?: BillmanagerDetailMiddle | any) =>
  defHttp.get<BillmanagerDetailMiddle>({ url: adminPath + '/billmanager/bankdirectlink/paydetail/billmanagerDetailMiddle/delete', params });

export const billmanagerDetailMiddleSyncFromBank = (params?: BillmanagerDetailMiddle | any) =>
  defHttp.post<Page<BillmanagerDetailMiddle>>({ url: adminPath + '/billmanager/bankdirectlink/paydetail/billmanagerDetailMiddle/syncFromBank', params });
