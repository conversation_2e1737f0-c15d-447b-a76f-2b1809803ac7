<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div style="text-align: right; margin: 0 15px 20px 0">
    <a-button
      type="primary"
      @click="handleSubmit"
      v-auth="'layout:edit'"
      style="margin-right: 5px"
      :loading="loading"
    >
      <Icon icon="ant-design:check-outlined" /> {{ t('保存') }}
    </a-button>
  </div>
  <BasicForm @register="registerForm" />
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherFormConfigForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  // import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import {
    LayVoucherFormConfig,
    layVoucherFormConfigSave,
    // layVoucherTabConfigForm,
    layVoucherFormConfigFindOne,
  } from '/@/api/layout/form/layVoucherFormConfig';

  // const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('layout.layVoucherFormConfig');
  const { showMessage } = useMessage();
  const record = ref<LayVoucherFormConfig>({} as LayVoucherFormConfig);
  const props = defineProps({
    colpop: { type: Object, default: {} },
  });

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('布局标志'),
      field: 'viewCode',
      component: 'Input',
      dynamicDisabled: true,
      componentProps: {
        maxlength: 200,
      },
      required: true,
    },
    {
      label: t('表单标题'),
      field: 'formTitle',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
      required: true,
    },
    {
      label: t('表单布局'),
      field: 'formColNum',
      component: 'Select',
      componentProps: {
        dictType: 'lay_form_col',
        allowClear: true,
      },
      required: true,
    },
    {
      label: t('流程表单Key'),
      field: 'bpmFormKey',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('是否流程表单'),
      field: 'isBpmForm',
      component: 'Checkbox',
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['isBpmForm'] = v.target.checked ? 1 : 0;
          },
        };
      },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 24, md: 24 },
  });

  // const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
  //   setDrawerProps({ loading: true });
  //   await resetFields();
  //   const res = await layVoucherTabConfigForm(data);
  //   record.value = (res.layVoucherTabConfig || {}) as LayVoucherTabConfig;
  //   record.value.__t = new Date().getTime();
  //   setFieldsValue(record.value);
  //   // updateSchema([
  //   //   {
  //   //     field: 'viewCode',
  //   //     componentProps: {
  //   //       disabled: !record.value.isNewRecord,
  //   //     },
  //   //   },
  //   // ]);
  //   setDrawerProps({ loading: false });
  // });
  watch(
    () => props.colpop,
    () => {
      configForm();
    },
    // { immediate: true },
  );
  async function configForm() {
    await resetFields();
    const res = await layVoucherFormConfigFindOne({ viewCode: props.colpop.code });
    record.value = (res || {}) as LayVoucherTabConfig;
    record.value.viewCode = props.colpop.code;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
  }
  let loading = ref(false);
  async function handleSubmit() {
    try {
      const data = await validate();

      loading.value = true;
      // setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await layVoucherFormConfigSave(params, data);
      record.value.id = res.data;
      record.value.isNewRecord = false;
      showMessage(res.message);
      loading.value = false;
      // setTimeout(closeDrawer);
      // emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      // setDrawerProps({ confirmLoading: false });
    }
  }
</script>
