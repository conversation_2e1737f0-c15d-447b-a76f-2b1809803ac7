import { useI18n } from '/@/hooks/web/useI18n';
import { BasicColumn, BasicTableProps, FormProps } from '/@/components/Table';
import { officeTreeData } from '/@/api/sys/office';
import { mfCarplanFhHListData } from '/@/api/wms/weighbridge/fh';

const { t } = useI18n('sys.empUser');

const modalProps = {
  title: t('销售发货装车计划管理'),
};

const searchForm: FormProps = {
  baseColProps: { lg: 6, md: 8 },
  labelWidth: 100,
  schemas: [
    {
      label: t('发货车次'),
      field: 'djNo',
      component: 'Input',
    },
    {
      label: t('计划发货日期'),
      field: 'planDate',
      component: 'RangePicker',
    },
    {
      label: t('车牌号'),
      field: 'carNo',
      component: 'Input',
    },
    {
      label: t('称重完成时间'),
      field: 'wcDate',
      component: 'RangePicker',
    },
    {
      label: t('状态'),
      field: 'cstatus',
      component: 'Input',
    },
  ],
};

const tableColumns: BasicColumn[] = [
  {
    title: t('发货车次'),
    dataIndex: 'djNo',
    key: 'a.dj_no',
    sorter: true,
    width: 100,
  },
  {
    title: t('计划发货日期'),
    dataIndex: 'planDate',
    key: 'a.plan_date',
    sorter: true,
    width: 100,
  },
  {
    title: t('车牌号'),
    dataIndex: 'carNo',
    key: 'a.car_no',
    sorter: true,
    width: 100,
  },
  {
    title: t('皮重'),
    dataIndex: 'pzWeight',
    key: 'a.pz_weight',
    sorter: true,
    width: 100,
  },
  {
    title: t('毛重'),
    dataIndex: 'mzWeight',
    key: 'a.mz_weight',
    sorter: true,
    width: 100,
  },
  {
    title: t('净重'),
    dataIndex: 'jzWeight',
    key: 'a.a.jz_weight',
    sorter: true,
    width: 80,
  },
  {
    title: t('称重完成时间'),
    dataIndex: 'wcDate',
    key: 'a.wc_date',
    sorter: true,
    width: 130,
  },
  {
    title: t('状态'),
    dataIndex: 'cstatus',
    key: 'a.cstatus',
    sorter: true,
    width: 130,
  },
  {
    title: t('备注'),
    dataIndex: 'remarks',
    key: 'a.remarks',
    sorter: true,
    width: 130,
  },
];

const tableProps: BasicTableProps = {
  api: mfCarplanFhHListData,
  beforeFetch: (params) => {
    params['isAll'] = true;
    return params;
  },
  columns: tableColumns,
  formConfig: searchForm,
  rowKey: 'djNo',
};

export default {
  modalProps,
  tableProps,
  itemCode: 'djNo',
  itemName: 'carNo',
  isShowCode: true,
};
