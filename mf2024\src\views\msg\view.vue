<!--
  * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
  * No deletion without permission, or be held responsible to law.
  * <AUTHOR>
-->
<template>
  <PageWrapper :sidebarWidth="230" title="false">
    <div class="jeesite-msg-title mb-5 ml-2 mr-2 pb-6 pt-4 text-center text-xl">
      {{ record.msgTitle }}
    </div>
    <BasicForm @register="registerForm" />
    <div class="flex justify-center">
      <a-button type="primary" @click="closeCurrent">
        <Icon icon="i-ant-design:close-outlined" /> {{ t('关闭') }}
      </a-button>
    </div>
  </PageWrapper>
</template>
<script lang="ts" setup name="ViewsMsgView">
  import { ref, onMounted } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { PageWrapper } from '/@/components/Page';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { Msg, msgReadMsg } from '/@/api/msg/msg';
  import { useTabs } from '/@/hooks/web/useTabs';
  import { useQuery } from '/@/hooks/web/usePage';
  import { useEmitter } from '/@/store/modules/user';

  const { t } = useI18n('sys.msg');
  const { closeCurrent } = useTabs();
  const getQuery = useQuery();
  const record = ref<Msg>({ id: getQuery.value.id } as Msg);
  const emitter = useEmitter();

  const inputFormSchemas: FormSchema[] = [
    {
      field: 'msgContentEntity.content',
      component: 'Text',
      componentProps: {
        isHtml: true,
      },
      colProps: { lg: 24, md: 24 },
    },
    {
      field: 'msgInfo',
      component: 'FormGroup',
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('发送者'),
      field: 'sendUserName',
      component: 'Text',
      colProps: { lg: 8, md: 24 },
    },
    {
      label: t('发送时间'),
      field: 'sendDate',
      component: 'Text',
      colProps: { lg: 8, md: 24 },
    },
    {
      label: t('读取时间'),
      field: 'readDate',
      component: 'Text',
      colProps: { lg: 8, md: 24 },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  onMounted(async () => {
    await resetFields();
    const res = await msgReadMsg(record.value);
    record.value = (res.msgPush || {}) as Msg;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    // 通知删除 Header 上的通知信息 ThinkGem
    emitter.emit('on-msg-notify-delete', record.value);
  });
</script>
<style lang="less">
  .jeesite-msg-title {
    border-bottom: 1px solid #ddd;
  }

  [data-theme='dark'] {
    .jeesite-msg-title {
      border-bottom: 1px solid #303030;
    }
  }
</style>
