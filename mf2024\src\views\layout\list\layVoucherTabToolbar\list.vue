<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button
          type="primary"
          @click="
            handleForm({
              viewCode: props.colpop.code,
              vouchCode: props.colpop.typeCode,
              'voucherCls.typeName': props.colpop.typeName,
            })
          "
          v-auth="'layout:edit'"
        >
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.viewCode }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherTabToolbarList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import {
    layVoucherTabToolbarDelete,
    layVoucherTabToolbarListData,
  } from '../../../../api/layout/list/layVoucherTabToolbar';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('layout.layVoucherTabToolbar');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: t('表格按钮自定义管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('布局名称'),
        field: 'viewCode',
        component: 'Input',
      },
      {
        label: t('基础单据'),
        field: 'vouchCode',
        component: 'Input',
      },
      {
        label: t('按钮类型'),
        field: 'btnType',
        component: 'Input',
      },
      {
        label: t('权限标识'),
        field: 'auth',
        component: 'Input',
      },
      {
        label: t('按钮图标'),
        field: 'icon',
        component: 'Input',
      },
      {
        label: t('按钮标题'),
        field: 'title',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('顺序号'),
      dataIndex: 'sortNum',
      key: 'a.sort_num',
      sorter: true,
      width: 80,
      align: 'center',
    },
    {
      title: t('布局名称'),
      dataIndex: 'viewCode',
      key: 'a.viewCode',
      sorter: true,
      width: 130,
      align: 'center',
      slot: 'firstColumn',
      ifShow:false,
    },
    {
      title: t('基础单据'),
      dataIndex: 'layVoucher.name',
      key: 'a.vouch_code',
      sorter: true,
      width: 230,
      align: 'center',
      ifShow:false,
    },
    {
      title: t('按钮类型'),
      dataIndex: 'btnType',
      key: 'a.btn_type',
      sorter: true,
      width: 130,
      align: 'center',
    },
    
    {
      title: t('权限标识'),
      dataIndex: 'auth',
      key: 'a.auth',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('按钮标志'),
      dataIndex: 'btnKey',
      key: 'a.btn_ey',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('按钮图标'),
      dataIndex: 'icon',
      key: 'a.icon',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('按钮标题'),
      dataIndex: 'title',
      key: 'a.title',
      sorter: true,
      width: 130,
      align: 'center',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑表格按钮自定义'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'layout:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除表格按钮自定义'),
        popConfirm: {
          title: t('是否确认删除表格按钮自定义'),
          confirm: handleDelete.bind(this, { id: record.id }),
        },
        auth: 'layout:edit',
      },
    ],
  };

  const props = defineProps({
    colpop: { type: Object, default: {} },
  });
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm }] = useTable({
    api: layVoucherTabToolbarListData,
    beforeFetch: (params) => {
      params.viewCode = props.colpop.code;
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: false,
    canResize: true,
    pagination: false,
    showIndexColumn:false,
  });
  watch(
    () => props.colpop,
    () => {
      reload();
    },
    // { immediate: true },
  );

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const res = await layVoucherTabToolbarDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleSuccess() {
    reload();
  }
</script>
