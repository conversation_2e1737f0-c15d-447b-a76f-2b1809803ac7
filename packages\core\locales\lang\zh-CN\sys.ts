export default {
  api: {
    operationFailed: '操作失败',
    errorTip: '系统提示',
    errorMessage: '操作失败，系统异常！',
    timeoutMessage: '登录超时，请重新登录！',
    apiTimeoutMessage: '接口请求超时，请刷新页面重试！',
    apiRequestFailed: '接口请求出错，请稍候重试！',
    networkException: '网络异常',
    networkExceptionMsg: '网络异常，请稍后重试！',

    errMsg401: '很抱歉，您没有权限（令牌、用户名、密码错误）！',
    errMsg403: '很抱歉，您没有权限访问此页面，若有疑问请联系管理员。',
    errMsg404: '网络请求错误，访问地址不存在！',
    errMsg405: '网络请求错误，请求方法未允许！',
    errMsg408: '网络请求超时！',
    errMsg500: '服务器错误，请联系管理员！',
    errMsg501: '网络未实现！',
    errMsg502: '网络错误！',
    errMsg503: '服务不可用，服务器暂时过载或维护！',
    errMsg504: '网络超时！',
    errMsg505: 'http版本不支持该请求！',
  },
  message: {
    error: '失败,错误,未完成',
    warning: '不能,不允许,必须,已存在,不需要,不正确',
    success: '成功,完成',
  },
  app: {
    logoutTip: '温馨提醒',
    logoutMessage: '是否确认退出系统？',
    menuLoading: '请稍后，即将进入系统...',
  },
  errorLog: {
    tableTitle: '错误日志列表',
    tableColumnType: '类型',
    tableColumnDate: '时间',
    tableColumnFile: '文件',
    tableColumnMsg: '错误信息',
    tableColumnStackMsg: 'stack信息',

    tableActionDesc: '详情',

    modalTitle: '错误详情',

    fireVueError: '模拟vue错误',
    fireResourceError: '模拟资源加载错误',
    fireAjaxError: '模拟ajax错误',

    enableMessage: '只在 `projectSetting.ts` 内的useErrorHandle为true时生效.',
  },
  exception: {
    backLogin: '返回登录',
    backHome: '返回首页',
    subTitle403: '抱歉，您无权访问此页面。',
    subTitle404: '抱歉，您访问的页面不存在。',
    subTitle500: '抱歉，服务器报告错误。',
    noDataTitle: '当前页无数据',
    networkErrorTitle: '网络错误',
    networkErrorSubTitle: '抱歉，您的网络连接已断开，请检查您的网络！',
  },
  lock: {
    unlock: '点击解锁',
    alert: '锁屏密码错误',
    backToLogin: '返回登录',
    entry: '进入系统',
    placeholder: '请输入锁屏密码或者用户密码',
  },
  login: {
    backSignIn: '返回',
    signInFormTitle: '登录',
    mobileSignInFormTitle: '手机登录',
    qrSignInFormTitle: '二维码登录',
    signUpFormTitle: '注册',
    forgetFormTitle: '重置密码',

    signInTitle: 'JeeSite 是当前最好用的快速开发平台',
    signInDesc: 'JeeSite 是一个专业的平台，是一个让你使用放心的平台。',
    policy: '我同意 JeeSite 隐私政策',
    scanSign: `扫码后点击"确认"，即可完成登录`,

    loginButton: '登录',
    registerButton: '注册',
    rememberMe: '记住我',
    forgetPassword: '忘记密码?',
    otherSignIn: '其他登录方式',

    // notify
    loginSuccessTitle: '登录成功',
    loginSuccessDesc: '欢迎回来',

    // placeholder
    accountPlaceholder: '请输入账号',
    userNamePlaceholder: '请输入姓名',
    passwordPlaceholder: '请输入密码',
    smsPlaceholder: '请输入验证码',
    mobilePlaceholder: '请输入手机号码',
    emailPlaceholder: '请输入邮箱',
    policyPlaceholder: '勾选后才能注册',
    diffPwd: '两次输入密码不一致',
    corpPlaceholder: '请选择租户',
    userPlaceholder: '请选择账号',

    account: '账号',
    userName: '姓名',
    password: '密码',
    confirmPassword: '确认密码',
    validCode: '验证码',
    mobile: '手机号码',
    email: '邮箱',
    smsCode: '短信验证码',
    emailCode: '邮箱验证码',
    getPwdQuestion: '获取保密问题',
    pwdQuestion: '保密问题',
    pwdQuestionAnswer: '请输入答案',
  },
  account: {
    center: '个人中心',
    userInfo: '个人信息',
    modifyPwd: '修改密码',
    modifyPwdTip: '请定期修改密码',
    modifyPqa: '修改密保',
    modifyPqaTip: '请定期修改密保',

    basicTab: '基础设置',
    securityTab: '安全设置',
    bindingTab: '账号绑定',

    userName: '用户昵称',
    email: '电子邮箱',
    mobile: '手机号码',
    phone: '办公电话',
    sign: '个性签名',
    changeAvatar: '更换头像',
    updateBtn: '更新信息',

    oldPassword: '当前密码',
    newPassword: '新密码',
    confirmNewPassword: '确认密码',
    newPasswordInputTip: '请输入新密码',
    newPasswordNotBlank: '密码不能为空',
    newPasswordNotEquals: '两次输入的密码不一致!',

    pwdQuestion: '保密问题',
    oldPwdQuestion: '旧保密问题',
    oldPwdQuestionAnswer: '旧保密答案',
    newPwdQuestion: '新保密问题',
    newPwdQuestionAnswer: '新保密答案',
  },
};
