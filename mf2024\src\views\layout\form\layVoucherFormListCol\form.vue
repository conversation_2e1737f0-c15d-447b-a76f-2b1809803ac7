<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'layout:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherFormListColForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { LayVoucherFormListCol, layVoucherFormListColSave, layVoucherFormListColForm, treeData } from '../../../../api/layout/form/layVoucherFormListCol';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('layout.layVoucherFormListCol');
  const { showMessage } = useMessage();
  const record = ref<LayVoucherFormListCol>({} as LayVoucherFormListCol);
  const getTitle = computed(() => ({
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增表单栏目') : t('编辑表单栏目'),
  }));
  const props = defineProps({
    viewCode: { type: String, default: '' },
  });

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('布局标志'),
      field: 'viewCode',
      component: 'Input',
      dynamicDisabled: true,
      componentProps: {
        maxlength: 200,
      },
      colProps: { lg: 24, md: 24 },
      required: true,
    },
    {
      label: t('归属页签'),
      field: 'tabCode',
      fieldLabel: 'tabName',
      component: 'TreeSelect',
      colProps: { lg: 24, md: 24 },
      // componentProps: {
      //   // dictType: 'lay_comp_type',
      //   api: treeData,
      //   params: {viewCode :props.viewCode},
      //   allowClear: true,
      // },
      componentProps: ({ formModel }) => {
        return {
          // options: [{ label: '', value: 1 }],
          api: treeData,
          params: {viewCode :props.viewCode},
          onClick: (v) => {
            console.log(v);
            console.log(v.target);
            console.log(formModel['tabCode']);
            // formModel['compMode'] = v.target.checked ? 1 : 0;
          },
        };
      },
      required: true,
    },
    {
      label: t('顺序号'),
      field: 'sortNum',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      rules: [{required: true},{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('标准名称'),
      field: 'originName',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
      required: true,
    },
    {
      label: t('显示名称'),
      field: 'formLabel',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
    },
    {
      label: t('字段名称'),
      field: 'formField',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
    },
    
    {
      label: t('组件类型'),
      field: 'formComponent',
      component: 'Select',
      componentProps: {
        dictType: 'lay_comp_type',
        allowClear: true,
      },
      required: true,
    },
    
    {
      label: t('默认值'),
      field: 'formDefaultValue',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('检验规则'),
      field: 'formRules',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('栅格比列'),
      field: 'formColProps',
      component: 'Select',
      componentProps: {
        dictType: 'lay_form_col',
        allowClear: true,
      },
    },
    {
      label: t('检验提示信息'),
      field: 'formMessage',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('是否必填'),
      field: 'formIsRequired',
      component: 'Checkbox',
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            console.log(v.target);
            formModel['formIsRequired'] = v.target.checked ? 1 : 0;
          },
        };
      },
    },
    {
      label: t('是否显示'),
      field: 'formShow',
      component: 'Checkbox',
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            console.log(v.target);
            formModel['formShow'] = v.target.checked ? 1 : 0;
          },
        };
      },
    },
    {
      label: t('是否禁用'),
      field: 'formDynamicDisable',
      component: 'Checkbox',
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            console.log(v.target);
            formModel['formDynamicDisable'] = v.target.checked ? 1 : 0;
          },
        };
      },
    },
    {
      label: t('是否允许清除'),
      field: 'compAllowClear',
      component: 'Checkbox',
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            console.log(v.target);
            formModel['compAllowClear'] = v.target.checked ? 1 : 0;
          },
        };
      },
    },
    {
      label: t('是否启用多选'),
      field: 'compMode',
      component: 'Checkbox',
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            console.log(v.target);
            formModel['compMode'] = v.target.checked ? 1 : 0;
          },
        };
      },
    },
    {
      label: t('是否可选父级'),
      field: 'compCanSelectParent',
      component: 'Checkbox',
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            console.log(v.target);
            formModel['compCanSelectParent'] = v.target.checked ? 1 : 0;
          },
        };
      },
    },
    {
      label: t('是否显示时分秒'),
      field: 'compShowTime',
      component: 'Checkbox',
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            console.log(v.target);
            formModel['compShowTime'] = v.target.checked ? 1 : 0;
          },
        };
      },
    },
    
    {
      label: t('日期格式'),
      field: 'compFormat',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
   
    // {
    //   label: t('栅格比列'),
    //   field: 'formColProps',
    //   component: 'Input',
    //   componentProps: {
    //     maxlength: 100,
    //   },
    // },
    
    {
      label: t('插槽'),
      field: 'formSlot',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('选择类型'),
      field: 'formSelectType',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('最大长度'),
      field: 'compMaxLength',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('字典'),
      field: 'compDictType',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    
    {
      label: t('接口名称'),
      field: 'compApi',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('参数地址'),
      field: 'compParams',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
   
    {
      label: t('加载时间'),
      field: 'compLoadTime',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('表单主键'),
      field: 'compBizKey',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('关联业务类型'),
      field: 'compBizType',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('上传文件类型'),
      field: 'compUploadType',
      component: 'Select',
      componentProps: {
        dictType: 'lay_upload_type',
        allowClear: true,
      },
    },
   
    {
      label: t('权限标识'),
      field: 'auth',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await layVoucherFormListColForm(data);
    record.value = (res.layVoucherFormListCol || {}) as LayVoucherFormListCol;
    record.value.__t = new Date().getTime();
    
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await layVoucherFormListColSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
