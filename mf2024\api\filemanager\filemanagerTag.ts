/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface FilemanagerTag extends BasicModel<FilemanagerTag> {
  tagId?: string; // 标签编号
  tagName?: string; // 标签名称
  tagColor?: string; // 标签颜色
  tagSort?: number; // 标签排序（升序）
  isSys?: string; // 系统内置
  useNum?: number; // 使用计数
}

export const filemanagerTagList = (params?: FilemanagerTag | any) =>
  defHttp.get<FilemanagerTag>({ url: adminPath + '/filemanager/filemanagerTag/list', params });

export const filemanagerTagListData = (params?: FilemanagerTag | any) =>
  defHttp.post<Page<FilemanagerTag>>({
    url: adminPath + '/filemanager/filemanagerTag/listData',
    params,
  });

export const filemanagerTagForm = (params?: FilemanagerTag | any) =>
  defHttp.get<FilemanagerTag>({ url: adminPath + '/filemanager/filemanagerTag/form', params });

export const filemanagerTagSave = (params?: any, data?: FilemanagerTag | any) =>
  defHttp.postJson<FilemanagerTag>({
    url: adminPath + '/filemanager/filemanagerTag/save',
    params,
    data,
  });

export const filemanagerTagDelete = (params?: FilemanagerTag | any) =>
  defHttp.get<FilemanagerTag>({ url: adminPath + '/filemanager/filemanagerTag/delete', params });

export const filemanagerTagAssign = (params?: FilemanagerTag | any) =>
  defHttp.get<FilemanagerTag>({ url: adminPath + '/filemanager/filemanagerTag/assign', params });

export const filemanagerTagAssignSave = (params?: FilemanagerTag | any) =>
  defHttp.post<FilemanagerTag>({
    url: adminPath + '/filemanager/filemanagerTag/assignSave',
    params,
  });
