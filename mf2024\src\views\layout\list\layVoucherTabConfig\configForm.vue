<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <!-- <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'layout:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer> -->
  <div style="text-align: right; margin: 0 15px 20px 0">
    <a-button
      type="primary"
      @click="handleSubmit"
      v-auth="'layout:edit'"
      style="margin-right: 5px"
      :loading="loading"
    >
      <Icon icon="ant-design:check-outlined" /> {{ t('保存') }}
    </a-button>
  </div>
  <BasicForm @register="registerForm" />
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherTabConfigForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  // import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  // import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import {
    LayVoucherTabConfig,
    layVoucherTabConfigSave,
    // layVoucherTabConfigForm,
    layVoucherTabConfigFindOne,
    exportMethod,
  } from '../../../../api/layout/list/layVoucherTabConfig';

  // const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('layout.layVoucherTabConfig');
  const { showMessage } = useMessage();
  const record = ref<LayVoucherTabConfig>({} as LayVoucherTabConfig);
  const props = defineProps({
    colpop: { type: Object, default: {} },
  });
  // const getTitle = computed(() => ({
  //   icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
  //   value: record.value.isNewRecord ? t('新增表格全局配置') : t('编辑表格全局配置'),
  // }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('布局标志'),
      field: 'viewCode',
      component: 'Input',
      dynamicDisabled: true,
      componentProps: {
        maxlength: 200,
      },
      required: true,
    },
    {
      label: t('请求接口'),
      field: 'api',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('表格标题'),
      field: 'title',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('表格右侧温馨提示'),
      field: 'titleHelpMessage',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('表格最大高度'),
      field: 'maxHeight',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('自适应高度偏量'),
      field: 'resizeHeightOffset',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('点击是否选中行'),
      field: 'clickToRowSelect',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      helpMessage: '点击行是否选中 checkbox 或者 radio。需要开启',
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['clickToRowSelect'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否显示表格设置工具'),
      field: 'showTableSetting',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      // helpMessage: '显示表格设置工具',
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['showTableSetting'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    // {
    //   label: t('是否显示表格设置工具配置'),
    //   field: 'tableSetting',
    //   component: 'Checkbox',
    //   // componentProps: {
    //   //   // dictType: 'sys_yes_no',
    //   //   options: [{ label: '', value: 1 }],
    //   // },
    //   componentProps: ({ formModel }) => {
    //     return {
    //       options: [{ label: '', value: 1 }],
    //       onChange: (v) => {
    //         formModel['tableSetting'] = v.target.checked ? 1 : 0;
    //       },
    //     };
    //   },
    // },
    {
      label: t('是否显示斑马纹'),
      field: 'striped',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['striped'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否取消表格默认padding'),
      field: 'inset',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['inset'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否自动生成key'),
      field: 'autoCreateKey',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['autoCreateKey'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否显示合计行'),
      field: 'showSummary',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['showSummary'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否显示表格'),
      field: 'emptyDataIsShowTable',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      helpMessage: '在启用搜索表单的前提下，是否在表格没有数据的时候显示表格',
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['emptyDataIsShowTable'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否树表'),
      field: 'isTreeTable',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['isTreeTable'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否延迟加载表格数据'),
      field: 'immediate',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['immediate'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否启用搜索表单'),
      field: 'useSearchForm',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['useSearchForm'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否显示行号'),
      field: 'showIndexColumn',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['showIndexColumn'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('文本超过宽度是否显示省略号'),
      field: 'ellipsis',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['ellipsis'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否自适应高度'),
      field: 'canResize',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['canResize'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('切换页面是否重置勾选状态'),
      field: 'clearSelectOnPageChange',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['clearSelectOnPageChange'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否显示表格边框'),
      field: 'bordered',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['bordered'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    // {
    //   label: t('是否显示表格loading状态'),
    //   field: 'loading',
    //   component: 'Checkbox',
    //   // componentProps: {
    //   //   // dictType: 'sys_yes_no',
    //   //   options: [{ label: '', value: 1 }],
    //   // },
    //   componentProps: ({ formModel }) => {
    //     return {
    //       options: [{ label: '', value: 1 }],
    //       onChange: (v) => {
    //         formModel['loading'] = v.target.checked ? 1 : 0;
    //       },
    //     };
    //   },
    //   rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    // },
    {
      label: t('是否分页'),
      field: 'pagination',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['pagination'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否导出'),
      field: 'isExport',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['isExport'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('是否导入'),
      field: 'isImport',
      component: 'Checkbox',
      // componentProps: {
      //   // dictType: 'sys_yes_no',
      //   options: [{ label: '', value: 1 }],
      // },
      componentProps: ({ formModel }) => {
        return {
          options: [{ label: '', value: 1 }],
          onChange: (v) => {
            formModel['isImport'] = v.target.checked ? 1 : 0;
          },
        };
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('数据源'),
      field: 'exportMethod',
      component: 'Select',
      helpMessage:
        '导出查询数据执行的方法（返回值为Page，参数为业务类型实体类， 默认为：findPage）',
      componentProps: () => {
        return {
          api: exportMethod,
          resultField: 'data',
          params: {
            beanName:
              (record.value.layVoucherView &&
                record.value.layVoucherView.layVouch &&
                record.value.layVoucherView.layVouch.beanId) ||
              '',
          },
        };
      },
    },
    {
      label: t('报表按钮展示'),
      field: 'exportPremise',
      component: 'Input',
      helpMessage:
        '格式1,2,3 <1代表在线打印,2代表PDF在线打印,3代表PDF在线预览打印,4代表导出PDF,5代表导出WORD,6代表导出EXCEL,7代表分页导出EXCEL,8代表分页分Sheet导出EXCEL>,9代表预览',
      componentProps: {
        maxlength: 200,
      },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 190,
    schemas: inputFormSchemas,
    baseColProps: { lg: 8, md: 24 },
  });

  // const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
  //   setDrawerProps({ loading: true });
  //   await resetFields();
  //   const res = await layVoucherTabConfigForm(data);
  //   record.value = (res.layVoucherTabConfig || {}) as LayVoucherTabConfig;
  //   record.value.__t = new Date().getTime();
  //   setFieldsValue(record.value);
  //   // updateSchema([
  //   //   {
  //   //     field: 'viewCode',
  //   //     componentProps: {
  //   //       disabled: !record.value.isNewRecord,
  //   //     },
  //   //   },
  //   // ]);
  //   setDrawerProps({ loading: false });
  // });
  watch(
    () => props.colpop,
    () => {
      configForm();
    },
    // { immediate: true },
  );
  async function configForm() {
    await resetFields();
    const res = await layVoucherTabConfigFindOne({ viewCode: props.colpop.code });
    record.value = (res || {}) as LayVoucherTabConfig;
    record.value.viewCode = props.colpop.code;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
  }
  let loading = ref(false);
  async function handleSubmit() {
    try {
      const data = await validate();

      loading.value = true;
      // setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };

      // console.log('submit', params, data, record);
      const res = await layVoucherTabConfigSave(params, data);
      record.value.id = res.data;
      record.value.isNewRecord = false;
      showMessage(res.message);
      loading.value = false;
      // setTimeout(closeDrawer);
      // emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      // setDrawerProps({ confirmLoading: false });
    }
  }
</script>
