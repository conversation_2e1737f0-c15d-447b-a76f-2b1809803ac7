/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface LayVoucherTableCol extends BasicModel<LayVoucherTableCol> {
  viewCode?: string; // 布局标志
  vouchCode?: string; // 基础单据
  title?: string; // 表头标题
  dataIndex?: string; // 字段名
  dbKey?: string; // 排序字段
  sorter?: string; // 是否可排序
  width?: number; // 列宽
  align?: string; // 对齐方式
  slot?: string; // 插槽
  dictType?: string; // 字典
}

export const layVoucherTableColList = (params?: LayVoucherTableCol | any) =>
  defHttp.get<LayVoucherTableCol>({ url: adminPath + '/layout/listTabCol/list', params });

export const layVoucherTableColListData = (params?: LayVoucherTableCol | any) =>
  defHttp.post<Page<LayVoucherTableCol>>({ url: adminPath + '/layout/listTabCol/listData', params });

export const layVoucherTableColForm = (params?: LayVoucherTableCol | any) =>
  defHttp.get<LayVoucherTableCol>({ url: adminPath + '/layout/listTabCol/form', params });

export const layVoucherTableColSave = (params?: any, data?: LayVoucherTableCol | any) =>
  defHttp.postJson<LayVoucherTableCol>({ url: adminPath + '/layout/listTabCol/save', params, data });

export const layVoucherTableColDelete = (params?: LayVoucherTableCol | any) =>
  defHttp.get<LayVoucherTableCol>({ url: adminPath + '/layout/listTabCol/delete', params });

  export const dictTreeData = (params?: LayVoucherTableCol | any) =>
  defHttp.get<LayVoucherTableCol>({ url: adminPath + '/sys/dictType/treeData', params });
