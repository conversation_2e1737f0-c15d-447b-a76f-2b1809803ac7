<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable" @fetchSuccess="fetchSuccess">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="default" @click="handlePrint({})" v-auth="'bas:pos:basPosition:edit'">
          <Icon icon="simple-line-icons:printer" /> {{ t('货位打印') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <span class="cursor-pointer" @click="expandCollapse(record)">
          ( {{ record.posCode }} )
        </span>
        <a @click="handleForm({ posCode: record.posCode })">
          {{ record.posCode }}
        </a>
      </template>
    </BasicTable>
    <!-- <InputForm @register="registerDrawer" @success="handleSuccess" /> -->
    <PrintModal @register="registerPrintModal" />
    <BasicModal
      v-bind="$attrs"
      @register="registerModal"
      title="标签打印"
      @ok="handleModalInvClick"
      :width="500"
      :height="300"
      okText="下载文件"
      cancelText="全部关闭"
    > 
       <div class="flex" style="justify-content: center;align-items: center;height: 100px;font-weight: bold;font-size: 20px;">
        {{ fileName }}
       </div>
      <!-- <BasicTable @register="registerPuArrTable" @row-click="handlePuArrRowClick" /> -->
    </BasicModal>
    <barSizeTypeForm @register="registerBarSizeTypeModal" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsWmsBarcodePrintListPos',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, watch, nextTick, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import {
    basPositionDelete,
    basPositionListData,
    basPositionListDataLevel,
  } from '/@/api/bas/pos/basPosition';
  import { printSnNumber } from '/@/api/wms/barcode/encode';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';
  import PrintModal from '/@/components/print/printModal.vue';
  import barSizeTypeForm from './barSizeTypeForm.vue';
  import { BarTypeEnum } from '/@/enums/defEnum';
  const props = defineProps({
    treeCode: String,
  });

  const { ctxPath } = useGlobSetting();
  const fileName = ref('');
  const pdfUrl = ref('');


  const { t } = useI18n('bas.pos.basPosition');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: router.currentRoute.value.meta.title || t('货位档案管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    autoAdvancedLine: 4,
    schemas: [
      {
        label: t('货位编码'),
        field: 'posCode',
        component: 'Input',
      },
      {
        label: t('货位名称'),
        field: 'posName',
        component: 'Input',
      },
      {
        label: t('仓库名称'),
        field: 'wareHouse.cwhname',
        component: 'Input',
      },
      // {
      //   label: t('条码尺寸'),
      //   field: 'barSizeType',
      //   component: 'RadioGroup',
      //   defaultValue: '8040',
      //   componentProps: {
      //     dictType: 'bar_size_type5',
      //     maxlength: 50,
      //   },
      //   required: true,
      //   // 独占一行
      //   colProps: { lg: 12, md: 24 },
      // },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('货位编码'),
      dataIndex: 'posCode',
      width: 230,
      align: 'left',
      //slot: 'firstColumn',
    },
    {
      title: t('货位名称'),
      dataIndex: 'posName',
      width: 230,
      align: 'left',
    },
    {
      title: t('仓库名称'),
      dataIndex: 'wareHouse.cwhname',
      width: 130,
      align: 'left',
    },
    {
      title: t('顺序号'),
      dataIndex: 'treeSort',
      width: 130,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('备注'),
      dataIndex: 'memo',
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑货位档案'),
        onClick: handleForm.bind(this, { posCode: record.posCode }),
        auth: 'bas:pos:basPosition:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除货位档案'),
        popConfirm: {
          title: t('是否确认删除货位档案'),
          confirm: handleDelete.bind(this, { posCode: record.posCode }),
        },
        auth: 'bas:pos:basPosition:edit',
      },
      {
        icon: 'fluent:add-circle-24-regular',
        title: t('新建下级货位档案'),
        onClick: handleForm.bind(this, {
          parentCode: record.id,
          parentName: record.posName,
        }),
        auth: 'bas:pos:basPosition:edit',
      },
    ],
  };

  const [registerPrintModal, { openModal: openPrintModal }] = useModal();
  const [registerModal, { openModal, closeModal }] = useModal();
  const [registerBarSizeTypeModal, { openModal: openBarSizeTypeModal }] = useModal();
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [
    registerTable,
    { reload, expandAll, collapseAll, expandCollapse, getForm, getSelectRows },
  ] = useTable({
    api: basPositionListDataLevel,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    //actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    pagination: true,
    canResize: true,
    rowSelection: {
      type: 'checkbox',
    },
  });

  watch(
    () => props.treeCode,
    async () => {
      await getForm().setFieldsValue({
        posCode: props.treeCode,
      });
      reload();
    },
  );

  function fetchSuccess() {
    if (props.treeCode) {
      nextTick(expandAll);
    }
  }

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const res = await basPositionDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleModalInvClick(record: Recordable) {
    // 下载
    // pdfUrl
    downloadByUrl({ url: ctxPath + pdfUrl.value });
    closeModal();
  }

  // 货位打印
  async function handlePrint() {
    let arr = await getSelectRows();
    // if (getSelectRows().length == 0) {
    //   showMessage(t('请先选择一行数据'));
    //   return;
    // }
    // const idsArr = arr.map((item) => {
    //   return item.posCode;
    // });
    // let params = {
    //   ids: idsArr.join(','),
    //   title: '货位标签打印',
    //   height: '0.9',
    //   width: '0.9',
    //   fileName: 'printHW',
    // };
    // openPrintModal(true, params);
    if (getSelectRows().length == 0) {
      showMessage(t('请先选择一行数据'));
      return;
    }
    openBarSizeTypeModal(true, { arr: arr, barSizeType: BarTypeEnum.Position });
    // const idsArr = arr.map((item) => {
    //   return item.id;
    // });
    // const prtqty = arr.map((item) => {
    //   return 1;
    // });
    // let params = {
    //   selIds: idsArr.join(','),
    //   barType: posBarType,
    //   // barType: 5,
    //   barSizeType: getForm().getFieldsValue().barSizeType,
    //   prtQtys: prtqty.join(','),
    // };
    // // 查询条件
    // const res = await printSnNumber(params);
    // if(res.result == "true"){
    //   fileName.value = res.fileName;
    //   pdfUrl.value = res.pdfUrl;
    //   await openModal(true, res);
    // } else {
    //   showMessage(res.message);
    // }
  }

  function handleSuccess() {
    reload();
  }
</script>
