/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface BasInvPack extends BasicModel<BasInvPack> {
  invCode?: string; // 存货编码
  packSize?: number; // 托盘容量
  pieceQty?: number; // 单件容量
}

export const basInvPackList = (params?: BasInvPack | any) =>
  defHttp.get<BasInvPack>({ url: adminPath + '/bas/inv/pack/basInvPack/list', params });

export const basInvPackListData = (params?: BasInvPack | any) =>
  defHttp.post<Page<BasInvPack>>({ url: adminPath + '/bas/inv/pack/basInvPack/listData', params });

export const basInvPackForm = (params?: BasInvPack | any) =>
  defHttp.get<BasInvPack>({ url: adminPath + '/bas/inv/pack/basInvPack/form', params });

export const basInvPackSave = (params?: any, data?: BasInvPack | any) =>
  defHttp.postJson<BasInvPack>({ url: adminPath + '/bas/inv/pack/basInvPack/save', params, data });

export const basInvPackImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/bas/inv/pack/basInvPack/importData',
      onUploadProgress,
    },
    params,
  );

export const basInvPackDelete = (params?: BasInvPack | any) =>
  defHttp.get<BasInvPack>({ url: adminPath + '/bas/inv/pack/basInvPack/delete', params });
