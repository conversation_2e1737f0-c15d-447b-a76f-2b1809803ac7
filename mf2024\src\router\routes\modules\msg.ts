import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const msg: AppRouteModule = {
  path: '/msg',
  name: 'Msg',
  component: LAYOUT,
  redirect: '/msg/list',
  meta: {
    icon: 'i-ant-design:message-outlined',
    title: t('sys.msg.listTitle'),
    orderNo: 90000,
  },
  children: [
    {
      path: 'list',
      name: 'MsgList',
      component: () => import('/@/views/msg/list.vue'),
      meta: {
        icon: 'i-ant-design:message-outlined',
        title: t('sys.msg.listTitle'),
      },
    },
    {
      path: 'view',
      name: 'MsgView',
      component: () => import('/@/views/msg/view.vue'),
      meta: {
        icon: 'i-ant-design:message-outlined',
        title: t('sys.msg.viewTitle'),
      },
    },
  ],
};

export default msg;
