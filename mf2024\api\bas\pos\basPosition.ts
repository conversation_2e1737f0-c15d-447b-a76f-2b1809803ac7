/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { TreeDataModel, TreeModel } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface BasPosition extends TreeModel<BasPosition> {
  posCode?: string; // 货位编码
  posName?: string; // 货位名称
  posGrade?: number; // 编码级次
  posEnd?: string; // 是否末级
  whcode?: string; // 仓库编码
  maxCubage?: number; // 最大体积
  maxWeight?: number; // 最大重量
  memo?: string; // 备注
  barcode?: string; // 对应条形码编码
  pubufts?: string; // 时间戳
}

export const basPositionList = (params?: BasPosition | any) =>
  defHttp.get<BasPosition>({ url: adminPath + '/bas/pos/basPosition/list', params });

export const basPositionListData = (params?: BasPosition | any) =>
  defHttp.post<BasPosition[]>({ url: adminPath + '/bas/pos/basPosition/listData', params });

export const basPositionListDataLevel = (params?: BasPosition | any) =>
  defHttp.post<BasPosition[]>({ url: adminPath + '/bas/pos/basPosition/listDataLevel', params });

export const basPositionForm = (params?: BasPosition | any) =>
  defHttp.get<BasPosition>({ url: adminPath + '/bas/pos/basPosition/form', params });

export const basPositionCreateNextNode = (params?: BasPosition | any) =>
  defHttp.get<BasPosition>({ url: adminPath + '/bas/pos/basPosition/createNextNode', params });

export const basPositionSave = (params?: any, data?: BasPosition | any) =>
  defHttp.postJson<BasPosition>({ url: adminPath + '/bas/pos/basPosition/save', params, data });

export const basPositionDelete = (params?: BasPosition | any) =>
  defHttp.get<BasPosition>({ url: adminPath + '/bas/pos/basPosition/delete', params });

export const basPositionTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/bas/pos/basPosition/treeData', params });
