/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WmsPalletSendRds extends BasicModel<WmsPalletSendRds> {
  cbatch?: string; // 托盘码
  carplanCid?: string; // 发货计划明细ID
  carDjno?: string; // 车次
  fhDjno?: string; // 发货单号
  carNo?: string; // 车牌号
  cusCode?: string; // 客户编码
  cusName?: string; // 客户名称
  invCode?: string; // 存货编码
  invName?: string; // 存货名称
  invStd?: string; // 规格型号
  unitName?: string; // 单位
  contract?: string; // 合同号
  whName?: string; // 发出仓库
  fweight?: number; // 发出重量
  createByName?: string; // 发出人
}

export const wmsPalletSendRdsList = (params?: WmsPalletSendRds | any) =>
  defHttp.get<WmsPalletSendRds>({ url: adminPath + '/wms/so/pallet/wmsPalletSendRds/list', params });

export const wmsPalletSendRdsListData = (params?: WmsPalletSendRds | any) =>
  defHttp.post<Page<WmsPalletSendRds>>({ url: adminPath + '/wms/so/pallet/wmsPalletSendRds/listData', params });

export const wmsPalletSendRdsForm = (params?: WmsPalletSendRds | any) =>
  defHttp.get<WmsPalletSendRds>({ url: adminPath + '/wms/so/pallet/wmsPalletSendRds/form', params });

export const wmsPalletSendRdsSave = (params?: any, data?: WmsPalletSendRds | any) =>
  defHttp.postJson<WmsPalletSendRds>({ url: adminPath + '/wms/so/pallet/wmsPalletSendRds/save', params, data });

export const wmsPalletSendRdsDelete = (params?: WmsPalletSendRds | any) =>
  defHttp.get<WmsPalletSendRds>({ url: adminPath + '/wms/so/pallet/wmsPalletSendRds/delete', params });
