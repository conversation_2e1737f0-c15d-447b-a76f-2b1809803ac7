<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button
          type="primary"
          @click="
            handleForm({
              viewCode: props.colpop.code,
              vouchCode: props.colpop.typeCode,
              'layVoucher.name': props.colpop.typeName,
            })
          "
          v-auth="'layout:edit'"
        >
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.viewCode }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherTabRightOpeList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import {
    layVoucherTabRightOpeDelete,
    layVoucherTabRightOpeListData,
  } from '../../../../api/layout/list/layVoucherTabRightOpe';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('layout.layVoucherTabRightOpe');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: t('表格右侧操作按钮管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('布局名称'),
        field: 'viewCode',
        component: 'Input',
      },
      {
        label: t('基础单据'),
        field: 'vouchCode',
        component: 'Input',
      },
      {
        label: t('按钮图标'),
        field: 'iocn',
        component: 'Input',
      },
      {
        label: t('按钮标题'),
        field: 'title',
        component: 'Input',
      },
      {
        label: t('按钮颜色'),
        field: 'color',
        component: 'Input',
      },
      {
        label: t('权限标识'),
        field: 'auth',
        component: 'Input',
      },
      {
        label: t('是否显示'),
        field: 'ifShow',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('行数据'),
        field: 'params',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('顺序号'),
      dataIndex: 'sortNum',
      key: 'a.sort_num',
      sorter: true,
      width: 80,
      align: 'center',
    },
    {
      title: t('布局名称'),
      dataIndex: 'viewCode',
      key: 'a.viewCode',
      sorter: true,
      width: 130,
      align: 'left',
      slot: 'firstColumn',
      ifShow:false,
    },
    {
      title: t('基础单据'),
      dataIndex: 'layVoucher.name',
      key: 'a.vouch_code',
      sorter: true,
      width: 230,
      align: 'left',
      ifShow:false,
    },
    {
      title: t('按钮图标'),
      dataIndex: 'icon',
      key: 'a.icon',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('按钮标题'),
      dataIndex: 'title',
      key: 'a.title',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('按钮颜色'),
      dataIndex: 'color',
      key: 'a.color',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('权限标识'),
      dataIndex: 'auth',
      key: 'a.auth',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('是否显示'),
      dataIndex: 'ifShow',
      key: 'a.if_show',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('行数据'),
      dataIndex: 'params',
      key: 'a.params',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑表格右侧操作按钮'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'layout:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除表格右侧操作按钮'),
        popConfirm: {
          title: t('是否确认删除表格右侧操作按钮'),
          confirm: handleDelete.bind(this, { id: record.id }),
        },
        auth: 'layout:edit',
      },
    ],
  };

  const props = defineProps({
    colpop: { type: Object, default: {} },
  });
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm }] = useTable({
    api: layVoucherTabRightOpeListData,
    beforeFetch: (params) => {
      params.viewCode = props.colpop.code;
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: false,
    canResize: true,
    pagination: false,
    showIndexColumn:false,
  });
  watch(
    () => props.colpop,
    () => {
      reload();
    },
    // { immediate: true },
  );

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const res = await layVoucherTabRightOpeDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleSuccess() {
    reload();
  }
</script>
