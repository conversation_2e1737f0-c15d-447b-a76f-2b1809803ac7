<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'billmanager:bankdirectlink:monitor:billmanagerHisAcceptanceExcute:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsBillmanagerBankdirectlinkMonitorBillmanagerHisAcceptanceExcuteForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BillmanagerHisAcceptanceExcute, billmanagerHisAcceptanceExcuteSave, billmanagerHisAcceptanceExcuteForm } from '/@/api/billmanager/bankdirectlink/monitor/billmanagerHisAcceptanceExcute';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('billmanager.bankdirectlink.monitor.billmanagerHisAcceptanceExcute');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<BillmanagerHisAcceptanceExcute>({} as BillmanagerHisAcceptanceExcute);

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增billmanager_his_acceptance_excute') : t('编辑billmanager_his_acceptance_excute'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('起始时间'),
      field: 'beginDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('结束时间'),
      field: 'endDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('同步结果描述'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 2000,
      },
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('同步用户信息'),
      field: 'createByName',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await billmanagerHisAcceptanceExcuteForm(data);
    record.value = (res.billmanagerHisAcceptanceExcute || {}) as BillmanagerHisAcceptanceExcute;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await billmanagerHisAcceptanceExcuteSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
