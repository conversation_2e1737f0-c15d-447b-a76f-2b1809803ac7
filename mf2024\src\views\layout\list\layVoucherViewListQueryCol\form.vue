<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'layout:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherViewListQueryColForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { LayVoucherViewListQueryCol, layVoucherViewListQueryColSave, layVoucherViewListQueryColForm } from '/@/api/layout/list/layVoucherViewListQueryCol';
import { layVoucherClsTreeData } from '/@/api/layout/vouch/layVoucherCls';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('layout.layVoucherViewListQueryCol');
  const { showMessage } = useMessage();
  const record = ref<LayVoucherViewListQueryCol>({} as LayVoucherViewListQueryCol);
  const getTitle = computed(() => ({
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增查询栏目') : t('编辑查询栏目'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('基础单据'),
      field: 'vouchCode',
      fieldLabel: 'voucherCls.typeName',
      component: 'TreeSelect',
      componentProps: {
        api: layVoucherClsTreeData,
        maxlength: 64,
        canSelectParent: false,
        allowClear: true,
      },
      required: true,
    },
    // {
    //   label: t('布局标志'),
    //   field: 'viewCode',
    //   component: 'Input',
    //   componentProps: {
    //     maxlength: 200,
    //   },
    //   required: true,
    // },
    {
      label: t('查询标志'),
      field: 'queryCode',
      component: 'Input',
      dynamicDisabled: true,
      componentProps: {
        maxlength: 200,
      },
      required: true,
    },
    {
      label: t('标签名称'),
      field: 'formLabel',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
    },
    {
      label: t('字段名称'),
      field: 'formField',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
    },
    {
      label: t('组件类型'),
      field: 'formComponent',
      component: 'Select',
      componentProps: {
        dictType: 'lay_comp_type',
        allowClear: true,
      },
      required: true,
    },
    {
      label: t('默认值'),
      field: 'formDefaultValue',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('是否显示'),
      field: 'formIsShow',
      component: 'RadioGroup',
      defaultValue: '1',
      componentProps: {
        dictType: 'sys_yes_no',
      },
    },
    {
      label: t('是否禁用'),
      field: 'formDynamicDisable',
      component: 'RadioGroup',
      defaultValue: '0',
      componentProps: {
        dictType: 'sys_yes_no',
      },
    },
    {
      label: t('是否允许删除'),
      field: 'compAllowClear',
      component: 'RadioGroup',
      defaultValue: '0',
      componentProps: {
        dictType: 'sys_yes_no',
      },
    },
    {
      label: t('是否启用多选'),
      field: 'compMode',
      component: 'RadioGroup',
      defaultValue: '0',
      componentProps: {
        dictType: 'sys_yes_no',
      },
    },
    {
      label: t('是否显示时分秒'),
      field: 'compShowTime',
      component: 'RadioGroup',
      defaultValue: '0',
      componentProps: {
        dictType: 'sys_yes_no',
      },
    },
    {
      label: t('参数字典'),
      field: 'compDictType',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('日期格式'),
      field: 'compFormat',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('接口名称'),
      field: 'compApi',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('参数地址'),
      field: 'compParams',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('事件函数'),
      field: 'compMethods',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('排序'),
      field: 'sort',
      helpMessage: '升序',
      component: 'InputNumber',
      componentProps: {
        maxlength: 200,
      },
      required: true,
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await layVoucherViewListQueryColForm(data);
    record.value = (res.layVoucherViewListQueryCol || {}) as LayVoucherViewListQueryCol;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await layVoucherViewListQueryColSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
