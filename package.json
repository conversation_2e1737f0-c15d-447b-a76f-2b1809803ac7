{"name": "@jeesite/root", "version": "5.12.1", "type": "module", "private": true, "scripts": {"bootstrap": "pnpm install", "dev": "cd web && pnpm run dev", "build": "turbo build --concurrency 20", "build:tomcat": "cd web && npm run build:tomcat", "build:preview": "pnpm run preview", "preview": "cd web && npm run preview", "preview:dist": "cd web && npm run preview:dist", "type:check": "turbo type:check --concurrency 20", "lint:eslint": "eslint --cache --max-warnings 0  \"./**/*.{ts,tsx,mjs,vue}\" --fix", "lint:prettier": "prettier --ignore-unknown --check --cache --write \"./**/*.{vue,tsx,less,scss}\"", "lint:stylelint": "stylelint \"./**/*.{vue,less,scss,css}\" --fix --custom-syntax postcss-html --cache --cache-location node_modules/.cache/stylelint/", "lint:all": "npm run bootstrap && npm run type:check && npm run lint:eslint && npm run lint:prettier && npm run lint:stylelint", "reinstall:force": "rimraf pnpm-lock.yaml node_modules && pnpm store prune && pnpm i --force && pnpm run dev --force", "reinstall": "turbo uninstall --concurrency 20 && rimraf pnpm-lock.yaml node_modules && npm run bootstrap", "update": "turbo update --concurrency 20 && ncu -u && npm run reinstall", "postinstall": "pnpm -r run stub --if-present", "preinstall": "npx only-allow pnpm", "serve": "npm run dev", "serve:mf2024": "cd mf2024 && pnpm run dev"}, "dependencies": {"@ant-design/colors": "8.0.0", "@ant-design/icons-vue": "7.0.1", "@jeesite/assets": "workspace:*", "@jeesite/vite": "workspace:*", "@jeesite/cms": "workspace:*", "@jeesite/core": "workspace:*", "@jeesite/dbm": "workspace:*", "@jeesite/dfm": "workspace:*", "@jeesite/test": "workspace:*", "@jeesite/types": "workspace:*", "@jeesite/web": "workspace:*", "ant-design-vue": "4.2.6", "axios": "1.9.0", "dayjs": "1.11.13", "lodash-es": "4.17.21", "vue": "3.5.13", "vue-eslint-parser": "10.1.3", "vue-router": "4.5.1", "vue-tsc": "2.2.10", "vue-types": "6.0.0"}, "devDependencies": {"@babel/eslint-parser": "7.27.1", "@iconify/json": "2.2.337", "@iconify/utils": "2.3.0", "@stylistic/stylelint-plugin": "3.1.2", "@types/node": "22.15.17", "@typescript-eslint/eslint-plugin": "8.32.1", "@typescript-eslint/parser": "8.32.1", "@unocss/eslint-config": "66.1.1", "@unocss/preset-wind3": "66.1.1", "@vue/compiler-sfc": "3.5.13", "@vue/runtime-core": "3.5.13", "@vue/shared": "3.5.13", "autoprefixer": "10.4.21", "cross-env": "7.0.3", "eslint": "9.26.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-prettier": "5.4.0", "eslint-plugin-vue": "10.1.0", "globals": "16.1.0", "less": "4.3.0", "npm-check-updates": "18.0.1", "pkg-types": "2.1.0", "postcss": "8.5.3", "postcss-html": "1.8.0", "postcss-less": "6.0.0", "prettier": "3.5.3", "prettier-plugin-packagejson": "2.5.12", "rimraf": "6.0.1", "stylelint": "16.19.1", "stylelint-config-recommended": "16.0.0", "stylelint-config-recommended-less": "3.0.1", "stylelint-config-recommended-vue": "1.6.0", "stylelint-config-standard": "38.0.0", "stylelint-config-standard-less": "3.0.1", "stylelint-less": "3.0.1", "stylelint-prettier": "5.0.3", "turbo": "2.5.3", "typescript": "5.8.3", "unocss": "66.1.1", "vite": "6.3.5"}, "keywords": ["typescript", "jeesite", "antdv", "vite", "vue"], "resolutions": {"bin-wrapper": "npm:bin-wrapper-china"}, "homepage": "https://jeesite.com", "repository": {"type": "git", "url": "https://gitee.com/thinkgem/jeesite-vue.git"}, "bugs": {"url": "https://gitee.com/thinkgem/jeesite-vue/issues"}, "author": {"name": "ThinkGem", "email": "<EMAIL>", "url": "https://gitee.com/thinkgem"}, "engines": {"node": "18 || >=20"}, "packageManager": "pnpm@10.11.0"}