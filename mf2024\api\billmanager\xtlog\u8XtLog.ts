/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface U8XtLog extends BasicModel<U8XtLog> {
  vouchId?: string; // 来源单据
  busType?: string; // 业务类型
  busNode?: string; // 业务节点
  sortNum?: number; // 顺序
  xtStatus?: string; // 业务节点状态
  cresult?: string; // 执行结果
  bstar?: string; // 协同开始标志
  bend?: string; // 协同结束标志
  ddate?: string; // 单据日期
  preDate?: string; // 就绪时间
  overDate?: string; // 完成时间
  croute?: string; // 协同路线
  routeStatus?: string; // 协同状态
  xtDjno?: string; // U8单据号
  reTimes?: number; // 重新协同次数
}

export const u8XtLogList = (params?: U8XtLog | any) =>
  defHttp.get<U8XtLog>({ url: adminPath + '/billmanager/xtlog/u8XtLog/list', params });

export const u8XtLogListData = (params?: U8XtLog | any) =>
  defHttp.post<Page<U8XtLog>>({ url: adminPath + '/billmanager/xtlog/u8XtLog/listData', params });

export const u8XtLogForm = (params?: U8XtLog | any) =>
  defHttp.get<U8XtLog>({ url: adminPath + '/billmanager/xtlog/u8XtLog/form', params });

export const reStartXt = (params?: U8XtLog | any) =>
  defHttp.post<U8XtLog>({ url: adminPath + '/billmanager/xtlog/u8XtLog/reStartXt', params });

export const closeXt = (params?: U8XtLog | any) =>
  defHttp.post<U8XtLog>({ url: adminPath + '/billmanager/xtlog/u8XtLog/closeXt', params });
