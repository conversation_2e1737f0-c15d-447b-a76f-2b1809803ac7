
- 官方网站：<https://jeesite.com>
- 使用文档：<https://jeesite.com/docs>
- 后端代码：<https://gitee.com/thinkgem/jeesite5>
- 前端代码：<https://gitee.com/thinkgem/jeesite-vue>

------

<div align="center">
 如果你喜欢 JeeSite，请给她一个 ⭐️ Star，您的支持将是我们前行的动力。
</div>

------

- 问题反馈：<https://gitee.com/thinkgem/jeesite-vue/issues> [【新手必读】](https://gitee.com/thinkgem/jeesite5/issues/I18ARR)
- 需求收集：<https://gitee.com/thinkgem/jeesite-vue/issues/new>
- QQ 群：`127515876`、`209330483`、`223507718`、`709534275`、`730390092`、`1373527`、`183903863(外包)`
- 微信群：添加客服微信 <http://s.jeesite.com> 邀请您进群
- 关注微信公众号，了解最新动态：

<p style="padding-left:40px">
 <img alt="JeeSite微信公众号" src="https://jeesite.com/assets/images/mp.png" width="220" height="220">
</p>
