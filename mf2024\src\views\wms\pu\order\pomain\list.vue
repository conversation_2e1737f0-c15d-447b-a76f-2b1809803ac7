<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'wms:pu:order:pomain:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.id }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsWmsPuOrderPomainList">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { pomainDelete, pomainListData } from '/@/api/wms/pu/order/pomain';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('wms/pu.order.pomain');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('采购订单管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('id'),
        field: 'id',
        component: 'Input',
      },
      {
        label: t('订单号'),
        field: 'cpoid',
        component: 'Input',
      },
      {
        label: t('订单日期起'),
        field: 'ddate_gte',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD',
          showTime: false,
        },
      },
      {
        label: t('订单日期止'),
        field: 'ddate_lte',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD',
          showTime: false,
        },
      },
      {
        label: t('供应商编码'),
        field: 'venCode',
        component: 'Input',
      },
      {
        label: t('供应商名称'),
        field: 'venName',
        component: 'Input',
      },
      {
        label: t('部门编码'),
        field: 'depCode',
        component: 'Input',
      },
      {
        label: t('部门名称'),
        field: 'depName',
        component: 'Input',
      },
      {
        label: t('制单人'),
        field: 'createBy',
        component: 'Input',
      },
      {
        label: t('仓库'),
        field: 'whCode',
        component: 'Input',
      },
      {
        label: t('仓库名称'),
        field: 'whName',
        component: 'Input',
      },
      {
        label: t('合同号'),
        field: 'htno',
        component: 'Input',
      },
      {
        label: t('关闭人'),
        field: 'closeBy',
        component: 'Input',
      },
      {
        label: t('审核人'),
        field: 'verifBy',
        component: 'Input',
      },
      {
        label: t('cdefine1'),
        field: 'cdefine1',
        component: 'Input',
      },
      {
        label: t('cdefine2'),
        field: 'cdefine2',
        component: 'Input',
      },
      {
        label: t('cdefine3'),
        field: 'cdefine3',
        component: 'Input',
      },
      {
        label: t('cdefine4'),
        field: 'cdefine4',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('cdefine5'),
        field: 'cdefine5',
        component: 'Input',
      },
      {
        label: t('cdefine6'),
        field: 'cdefine6',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('cdefine7'),
        field: 'cdefine7',
        component: 'Input',
      },
      {
        label: t('cdefine8'),
        field: 'cdefine8',
        component: 'Input',
      },
      {
        label: t('cdefine9'),
        field: 'cdefine9',
        component: 'Input',
      },
      {
        label: t('cdefine10'),
        field: 'cdefine10',
        component: 'Input',
      },
      {
        label: t('cdefine11'),
        field: 'cdefine11',
        component: 'Input',
      },
      {
        label: t('cdefine12'),
        field: 'cdefine12',
        component: 'Input',
      },
      {
        label: t('cdefine13'),
        field: 'cdefine13',
        component: 'Input',
      },
      {
        label: t('cdefine14'),
        field: 'cdefine14',
        component: 'Input',
      },
      {
        label: t('cdefine15'),
        field: 'cdefine15',
        component: 'Input',
      },
      {
        label: t('cdefine16'),
        field: 'cdefine16',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('id'),
      dataIndex: 'id',
      key: 'a.id',
      sorter: true,
      width: 230,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('订单号'),
      dataIndex: 'cpoid',
      key: 'a.cpoid',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('订单日期'),
      dataIndex: 'ddate',
      key: 'a.ddate',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('供应商编码'),
      dataIndex: 'venCode',
      key: 'a.ven_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('供应商名称'),
      dataIndex: 'venName',
      key: 'a.ven_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('部门编码'),
      dataIndex: 'depCode',
      key: 'a.dep_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('部门名称'),
      dataIndex: 'depName',
      key: 'a.dep_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('备注'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('仓库'),
      dataIndex: 'whCode',
      key: 'a.wh_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('仓库名称'),
      dataIndex: 'whName',
      key: 'a.wh_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('合同号'),
      dataIndex: 'htno',
      key: 'a.htno',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('关闭人'),
      dataIndex: 'closeBy',
      key: 'a.close_by',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('审核人'),
      dataIndex: 'verifBy',
      key: 'a.verif_by',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine1'),
      dataIndex: 'cdefine1',
      key: 'a.cdefine1',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine2'),
      dataIndex: 'cdefine2',
      key: 'a.cdefine2',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine3'),
      dataIndex: 'cdefine3',
      key: 'a.cdefine3',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine4'),
      dataIndex: 'cdefine4',
      key: 'a.cdefine4',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('cdefine5'),
      dataIndex: 'cdefine5',
      key: 'a.cdefine5',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('cdefine6'),
      dataIndex: 'cdefine6',
      key: 'a.cdefine6',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('cdefine7'),
      dataIndex: 'cdefine7',
      key: 'a.cdefine7',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('cdefine8'),
      dataIndex: 'cdefine8',
      key: 'a.cdefine8',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine9'),
      dataIndex: 'cdefine9',
      key: 'a.cdefine9',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine10'),
      dataIndex: 'cdefine10',
      key: 'a.cdefine10',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine11'),
      dataIndex: 'cdefine11',
      key: 'a.cdefine11',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine12'),
      dataIndex: 'cdefine12',
      key: 'a.cdefine12',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine13'),
      dataIndex: 'cdefine13',
      key: 'a.cdefine13',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine14'),
      dataIndex: 'cdefine14',
      key: 'a.cdefine14',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('cdefine15'),
      dataIndex: 'cdefine15',
      key: 'a.cdefine15',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('cdefine16'),
      dataIndex: 'cdefine16',
      key: 'a.cdefine16',
      sorter: true,
      width: 130,
      align: 'right',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑采购订单'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'wms:pu:order:pomain:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除采购订单'),
        popConfirm: {
          title: t('是否确认删除采购订单'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'wms:pu:order:pomain:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload }] = useTable({
    api: pomainListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { id: record.id };
    const res = await pomainDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
