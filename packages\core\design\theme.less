.bg-white {
  background-color: @component-background !important;
}

html[data-theme='light'] {
  .text-secondary {
    color: rgb(0 0 0 / 45%);
  }

  .ant-alert-success {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
  }

  .ant-alert-error {
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
  }

  .ant-alert-warning {
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
  }

  // :not(:root):fullscreen::backdrop {
  //   background-color: @layout-body-background !important;
  // }
}

html[data-theme='dark'] {
  .text-secondary {
    color: #8b949e;
  }

  .ant-card-grid-hoverable:hover {
    box-shadow:
      0 3px 6px -4px rgb(0 0 0 / 48%),
      0 6px 16px 0 rgb(0 0 0 / 32%),
      0 9px 28px 8px rgb(0 0 0 / 20%);
  }

  .ant-card-grid {
    box-shadow:
      1px 0 0 0 #434343,
      0 1px 0 0 #434343,
      1px 1px 0 0 #434343,
      1px 0 0 0 #434343 inset,
      0 1px 0 0 #434343 inset;
  }

  .ant-calendar-selected-day .ant-calendar-date {
    color: rgb(0 0 0 / 80%);
  }

  .ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {
    color: rgb(0 0 0 / 90%);
  }

  .table {
    border: 1px solid #333;

    th,
    td {
      border: 1px solid #333;
    }

    th {
      background-color: #222;
    }

    tr:hover {
      background-color: #222;

      .table {
        background-color: #111;
      }
    }
  }

  .ant-picker-input {
    & > input,
    & > input:hover,
    & > input:focus,
    & > input-focused {
      border-color: transparent;
      box-shadow: none;
    }
  }
}
