/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page, TreeDataModel } from '../../model/baseModel';
import { UploadFileParams } from '/#/axios';
import { UploadApiResult } from '../../sys/upload';

const { ctxPath, adminPath } = useGlobSetting();

export interface LayVoucher extends BasicModel<LayVoucher> {
  code?: string; // 单据标志
  typeCode?: string; // 单据类别
  name?: string; // 单据名称
  entityPath?: string; // 实体类路径
  stPrefixType?: string; // 起始前缀类型
  mdPrefix?: string; // 中间前缀
  buseDate?: string; // 启用日期
  dateFmt?: string; // 日期格式
  inum?: number; // 流水位数
}

export const layVoucherList = (params?: LayVoucher | any) =>
  defHttp.get<LayVoucher>({ url: adminPath + '/layout/layVoucher/list', params });

export const layVoucherListData = (params?: LayVoucher | any) =>
  defHttp.post<Page<LayVoucher>>({ url: adminPath + '/layout/layVoucher/listData', params });

export const layVoucherForm = (params?: LayVoucher | any) =>
  defHttp.get<LayVoucher>({ url: adminPath + '/layout/layVoucher/form', params });

export const layVoucherSave = (params?: any, data?: LayVoucher | any) =>
  defHttp.postJson<LayVoucher>({ url: adminPath + '/layout/layVoucher/save', params, data });

export const layVoucherDelete = (params?: LayVoucher | any) =>
  defHttp.get<LayVoucher>({ url: adminPath + '/layout/layVoucher/delete', params });

export const layVoucherTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/layout/layVoucherCls/treeData', params });

export const checkBeanID = (beanName?: string) =>
  defHttp.get({ url: adminPath + `/common/hasBeanName/${beanName}` });

export const importData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/common/import',
      onUploadProgress,
    },
    params,
  );
