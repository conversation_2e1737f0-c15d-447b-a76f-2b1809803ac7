/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import globals from 'globals';
import vuePlugin from 'eslint-plugin-vue';
import tsPlugin from '@typescript-eslint/eslint-plugin';
import prettierPlugin from 'eslint-plugin-prettier';
import tsParser from '@typescript-eslint/parser';
import vueParser from 'vue-eslint-parser';
import babelParser from '@babel/eslint-parser';

export default [
  // 基础忽略配置
  {
    ignores: [
      '**/node_modules/',
      '**/dist/',
      '**/public/',
      '**/build/',
      '**/.git/',
      '**/.vscode/',
      '**/.idea/',
      '**/.husky/',
      '**/.local/',
      '**/.turbo/',
      '**/Dockerfile',
      '**/*.sh',
      '**/*.md',
      '**/*.woff',
      '**/*.ttf',
      '**/*.d.ts',
      '**/__snapshots__/',
    ],
  },

  // 公共基础配置
  {
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        ...globals.browser,
        ...globals.node,
        defineOptions: 'readonly',
      },
      parserOptions: {
        requireConfigFile: false,
      },
    },
    rules: {
      // 通用规则
      'no-case-declarations': 'off',
      'no-extra-boolean-cast': 'off',
      'no-undef': 'off',
      'space-before-function-paren': 'off',
    },
  },

  // Vue 文件配置
  {
    files: ['**/*.vue'],
    plugins: {
      vue: vuePlugin,
    },
    languageOptions: {
      parser: vueParser,
      parserOptions: {
        parser: {
          ts: tsParser,
          tsx: tsParser,
          js: babelParser,
          '<template>': 'espree',
        },
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    rules: {
      // ...vuePlugin.configs['vue3-recommended'].rules,
      // 'vue/script-setup-uses-vars': 'error',
      'vue/no-reserved-component-names': 'off',
      'vue/custom-event-name-casing': 'off',
      'vue/attributes-order': 'off',
      'vue/one-component-per-file': 'off',
      'vue/html-closing-bracket-newline': 'off',
      'vue/max-attributes-per-line': 'off',
      'vue/multiline-html-element-content-newline': 'off',
      'vue/singleline-html-element-content-newline': 'off',
      'vue/attribute-hyphenation': 'off',
      'vue/require-default-prop': 'off',
      'vue/require-explicit-emits': 'off',
      'vue/html-self-closing': [
        'error',
        {
          html: {
            void: 'always',
            normal: 'never',
            component: 'always',
          },
          svg: 'always',
          math: 'always',
        },
      ],
      'vue/multi-word-component-names': 'off',
      'vue/no-v-html': 'off',
    },
  },

  // TypeScript 配置
  {
    files: ['**/*.ts', '**/*.tsx'],
    plugins: {
      '@typescript-eslint': tsPlugin,
    },
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        // project: './tsconfig.json',
        // jsxPragma: 'React',
        ecmaFeatures: {
          jsx: true, // 启用 JSX 支持
        },
      },
    },
    rules: {
      ...tsPlugin.configs.recommended.rules,
      '@typescript-eslint/ban-ts-comment': 'off',
      '@typescript-eslint/ban-types': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-empty-function': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/no-unused-expressions': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      '@typescript-eslint/no-use-before-define': 'off',
      '@typescript-eslint/no-var-requires': 'off',
      '@typescript-eslint/no-unsafe-function-type': 'off',
    },
  },

  // JavaScript 配置
  {
    files: ['**/*.js'],
    languageOptions: {
      parser: babelParser,
      parserOptions: {
        requireConfigFile: false,
        babelOptions: {
          presets: ['@babel/preset-env'],
          plugins: [['@babel/plugin-proposal-decorators', { legacy: true }]],
        },
      },
    },
  },

  // Prettier 集成配置
  {
    plugins: {
      prettier: prettierPlugin,
    },
    rules: {
      'prettier/prettier': [
        'error',
        {
          printWidth: 120,
          semi: true,
          singleQuote: true,
          trailingComma: 'all',
        },
      ],
      'property-sort-order': 'off',
    },
  },
];
