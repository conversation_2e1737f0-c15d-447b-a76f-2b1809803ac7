/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface LayVoucherFormListCol extends BasicModel<LayVoucherFormListCol> {
  viewCode?: string; // 布局标志
  formLabel?: string; // 显示名称
  formField?: string; // 字段名称
  formComponent?: string; // 组件类型
  formDefaultValue?: string; // 默认值
  formRules?: string; // 检验规则
  formShow?: string; // 是否显示
  formDynamicDisable?: string; // 是否禁用
  formColProps?: string; // 栅格比列
  formSlot?: string; // 插槽
  compMaxLength?: string; // 最大长度
  compAllowClear?: string; // 是否允许清除
  compMode?: string; // 是否启用多选
  compDictType?: string; // 参数字段
  compFormat?: string; // 日期格式
  compShowTime?: string; // 是否显示时分秒
  compApi?: string; // 接口名称
  compParams?: string; // 参数地址
  compCanSelectParent?: string; // 是否可选父级
  compLoadTime?: string; // 加载时间
  compBizKey?: string; // 表单主键
  compBizType?: string; // 关联业务类型
  compUploadType?: string; // 上传文件类型
  sort?: number; // 排序
  originName?: string; // 原始名称
  auth?: string; // 权限标识
}

export const layVoucherFormListColList = (params?: LayVoucherFormListCol | any) =>
  defHttp.get<LayVoucherFormListCol>({ url: adminPath + '/layout/formCol/list', params });

export const layVoucherFormListColListData = (params?: LayVoucherFormListCol | any) =>
  defHttp.post<Page<LayVoucherFormListCol>>({ url: adminPath + '/layout/formCol/listData', params });

export const layVoucherFormListColForm = (params?: LayVoucherFormListCol | any) =>
  defHttp.get<LayVoucherFormListCol>({ url: adminPath + '/layout/formCol/form', params });

export const layVoucherFormListColSave = (params?: any, data?: LayVoucherFormListCol | any) =>
  defHttp.postJson<LayVoucherFormListCol>({ url: adminPath + '/layout/formCol/save', params, data });

export const layVoucherFormListColDelete = (params?: LayVoucherFormListCol | any) =>
  defHttp.get<LayVoucherFormListCol>({ url: adminPath + '/layout/formCol/delete', params });

export const treeData = (params?: LayVoucherFormListCol | any) =>
  defHttp.get<LayVoucherFormListCol>({ url: adminPath + '/layout/formTab/treeData', params });


  
