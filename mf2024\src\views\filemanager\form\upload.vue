<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicUpload
    ref="upload"
    v-model:value="record.dataMap"
    :bizType="record.bizType"
    :loadTime="record.__t"
    :uploadType="'all'"
    :uploadText="t('上传文件')"
    :showPreview="false"
    :apiUploadUrl="ctxAdminPath + '/filemanager/upload'"
    :apiDownloadUrl="ctxAdminPath + '/filemanager/download'"
    :apiFileListUrl="ctxAdminPath + '/filemanager/fileList'"
    @click="handleClick"
    @change="handleSubmit"
  />
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { BasicUpload } from '/@/components/Upload';
  import { useGlobSetting } from '/@/hooks/setting';
  import { Filemanager, filemanagerForm, filemanagerSave } from '/@/api/filemanager/filemanager';

  const props = defineProps({
    folderId: String,
    groupType: String,
  });

  const emit = defineEmits(['success']);

  const { t } = useI18n('filemanager.filemanager');
  const { showMessage } = useMessage();
  const { ctxAdminPath } = useGlobSetting();
  const record = ref<Recordable>({} as Filemanager);

  async function handleClick() {
    const res = await filemanagerForm(props);
    record.value = (res.filemanager || {}) as Filemanager;
    record.value.__t = new Date().getTime();
    record.value.dataMap = {};
  }

  async function handleSubmit() {
    try {
      const data = record.value;
      const res = await filemanagerSave({}, data);
      showMessage(res.message);
      emit('success', data);
    } catch (error: any) {
      console.log('error', error);
    }
  }
</script>
