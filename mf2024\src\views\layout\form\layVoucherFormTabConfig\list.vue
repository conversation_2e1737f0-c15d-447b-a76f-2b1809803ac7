<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleForm({ viewCode: props.colpop.code })"
          v-auth="'layout:edit'"
        >
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.viewCode }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherFormTabConfigList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { layVoucherFormTabConfigDelete, layVoucherFormTabConfigListData } from '../../../../api/layout/form/layVoucherFormTabConfig';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('layout.layVoucherFormTabConfig');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: t('表单多页签管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('布局标志'),
        field: 'viewCode',
        component: 'Input',
      },
      {
        label: t('标签页名称'),
        field: 'tabName',
        component: 'Input',
      },
      {
        label: t('标签页地址'),
        field: 'tabAddress',
        component: 'Input',
      },
      {
        label: t('顺序号'),
        field: 'sort',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('顺序号'),
      dataIndex: 'sortNum',
      key: 'a.sort_num',
      sorter: true,
      width: 80,
      align: 'center',
    },
    {
      title: t('布局标志'),
      dataIndex: 'viewCode',
      key: 'a.view_code',
      sorter: true,
      width: 230,
      align: 'left',
      slot: 'firstColumn',
      ifShow:false,
    },
    {
      title: t('标签页名称'),
      dataIndex: 'tabName',
      key: 'a.tab_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('标签页地址'),
      dataIndex: 'tabAddress',
      key: 'a.tab_address',
      sorter: true,
      width: 130,
      align: 'left',
    },
    
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑多页签'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'layout:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除多页签'),
        popConfirm: {
          title: t('是否确认删除多页签'),
          confirm: handleDelete.bind(this, { id: record.id }),
        },
        auth: 'layout:edit',
      },
    ],
  };

  const props = defineProps({
    colpop: { type: Object, default: {} },
  });

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm }] = useTable({
    api: layVoucherFormTabConfigListData,
    beforeFetch: (params) => {
      params.viewCode = props.colpop.code;
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: false,
    canResize: true,
    showIndexColumn:false,
    
  });

  watch(
    () => props.colpop,
    () => {
      reload();
    },
    // { immediate: true },
  );

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const res = await layVoucherFormTabConfigDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleSuccess() {
    reload();
  }
</script>
