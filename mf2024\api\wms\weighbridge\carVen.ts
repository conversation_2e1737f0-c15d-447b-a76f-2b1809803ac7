/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { TreeDataModel, TreeModel } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface CarVen extends TreeModel<CarVen> {
  code?: string; // 运输单位编码
  name?: string; // 运输单位名称
}
export const carVenTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/mf/carven/carVen/treeData', params });
export const carVenTreeDataPc = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/mf/carven/carVen/pcTreeData', params });

export const carVenListData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/mf/carven/carVen/listData', params });
