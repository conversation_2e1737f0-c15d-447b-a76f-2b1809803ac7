<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicDrawer
      v-bind="$attrs"
      :showFooter="true"
      :okAuth="'wms.wh:rd10:wmsRd10:edit'"
      @register="registerDrawer"
      @ok="handleSubmit"
      width="60%"
    >
      <template #title>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} {{ getTitle.djno ? ' | ' + getTitle.djno : '' }} </span>
        <DictLabel dictType="wms_rd10_status" :dictValue="getTitle.djStatus" defaultValue="1"/>
        <!-- <div class="flex" v-if="!getTitle.isNewRecord">
          <a-button primary @click="handleForm({})" v-auth="'wms.wh:rd10:wmsRd10:edit'"> <Icon icon="i-ant-design:printer-outlined" /> {{ t('产品标签') }} </a-button>
          <a-button primary @click="handleForm({})" v-auth="'wms.wh:rd10:wmsRd10:edit'"><Icon icon="i-ant-design:printer-outlined" /> {{ t('补打(产品)') }}</a-button>
          <a-button primary @click="handlePrintTray({})" v-auth="'wms.wh:rd10:wmsRd10:edit'"><Icon icon="i-ant-design:printer-outlined" /> {{ t('托盘码') }}</a-button>
        </div> -->
      </template>
      <BasicForm @register="registerForm" ref="basicFormModel">
        <template #cbatch="{ model, field }">
          <a-input-group compact>
            <a-input v-model:value="model.cbatch" style="width: calc(100% - 500px)" disabled="true" />
            <a-button type="primary" @click="handleCbatchPreview(model, field)" v-if="model.djStatus == '1'">批次预览</a-button>
          </a-input-group>
        </template>
        <template #tuopaninfo="{ model, field }">

          是否启用托盘：<RadioGroup :options="optionsValues" :value="radioGroup" @click="handleRadioGroupClick" />
          <!-- <a-button type="primary" @click="handleBasInvPack(model, field)" v-auth="'wms.wh:rd10:wmsRd10:edit'" v-if="model.djStatus == '1'&&radioGroup=='1'"> {{ t('选择包装规格') }} </a-button>-->
          <ListSelect
            ref="listSelectRef"
            selectType="basInvPackSelect"
            @select="handleSelect"
            :selectList="selectListRef"
            :queryParams="queryParams"
            v-show="false"
          />
        </template>
      </BasicForm>
      <!-- <template #appendFooter>
      </template> -->
      <template #footer>
          <BpmButton
          v-model:bpmEntity="record"
          bpmEntityKey="id"
          formKey="mf_rd10_nottify"
          completeText="提交"
          draftText="保存"
          :completeModal="true"
          :loading="loadingRef"
          :auth="'wms.wh:rd10:wmsRd10:edit'"
          @validate="handleValidate"
          @complete="handleSubmit"
          @success="handleSuccess"
          @close="closeDrawer"
        />
    </template>
    </BasicDrawer>
    
  </div>
</template>
<script lang="ts" setup name="ViewsWmsWhRd10WmsRd10Form">
  import { ref, unref, computed, nextTick, onMounted } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm, formModel } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner, useDrawer } from '/@/components/Drawer';
  import { WmsRd10, wmsRd10Save, wmsRd10Form, wmsRd10CreateBatch } from '/@/api/wms/wh/rd10/wmsRd10';
  import { dictDataListData } from '/@/api/sys/dictData';
  import { BpmButton } from '/@/components/Bpm';
  import { basWarehousetreeData } from '/@/api/bas/house/basWarehouse';
  import { DictLabel } from '/@/components/Dict';
  import { officeTreeData } from '/@/api/sys/office';
  import { useGlobSetting } from '/@/hooks/setting';
  import { basInvPackListData } from '/@/api/bas/inv/pack/basInvPack';
  import { ListSelect } from '/@/components/ListSelect';
  import { OfficeTypeEnum, NumEnum } from '/@/enums/defEnum';
  import {  RadioGroup } from '/@/components/Form';
  import { toNumberFixed } from '/@/utils/index';
  const radioGroup = ref<String>('0');
  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('wms/wh.rd10.wmsRd10');
  const { showMessage, createSuccessModal } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<WmsRd10>({} as WmsRd10);
  const loadingRef = ref(false);
  const showPrintDrawer = ref(false);
  //批次管理
  const binvbatch = ref<boolean>(true);
  // 换算率
  const bfixExch = ref<string>('');

  const optionsValues = [];

  onMounted(async () => {
      const res = await dictDataListData({
          dictType: 'sys_yes_no',
      });
      res.forEach((item) => {
          optionsValues.push({
              label: item.dictLabelRaw,
              value: item.dictValue,
          });
      });
  });

  const basicFormModel = ref<any>(null);
// record.value.djno,
  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: t('入库通知'),
    djno: record.value.djno,
    djStatus: record.value.djStatus ? record.value.djStatus : 1,
    isNewRecord: record.value.isNewRecord,
  }));
  const selectListRef = ref<any>([]);
  let queryParams = ref< any >({});
  const listSelectRef = ref<any>(null);
  const inputFormSchemas: FormSchema[] = [
    {
      label: t('基本信息'),
      field: 'ddate1',
      component: 'FormGroup',
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('单据状态'),
      field: 'djStatus',
      component: 'Select',
      componentProps: {
        dictType: 'wms_rd10_status',
        defaultValue: '1',
      },
      dynamicDisabled: true,
      ifShow: false,
    },
    {
      label: t('单据日期'),
      field: 'ddate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        showTime: false,
      },
      required: true,
      dynamicDisabled: computed(() => !record.value.isNewRecord),
      colProps: { lg: 18, md: 24 },
    },
    {
      label: t('批号'),
      field: 'addCode',
      fieldLabel: 'addCode',
      component: 'ListSelect',
      componentProps: ({ tableAction, formModel }) => {
        return {
          selectType: 'InvPackSelect',
          onSelect: (values) => {
            console.log(values);
            binvbatch.value = values[0].basInv.binvbatch == '1' ? true : false;
            bfixExch.value = values[0].basInv.bfixExch;
            formModel['basInv.invStd'] = values[0].basInv.invStd;
            formModel['basInv.invName'] = values[0].basInv.invName;
            formModel['basInv.unitName'] = values[0].basInv.unitName;
            formModel['invCode'] = values[0].invCode;
            formModel['packSize'] = values[0].packSize;
            formModel['pieceQty'] = values[0].pieceQty;
            formModel['packName'] = values[0].packName;
            formModel['basInv.unitName'] = values[0].basInv.unitName;
            var bneedTuoPan = values[0].bneedTuoPan;
            if (bneedTuoPan) {
              radioGroup.value = values[0].bneedTuoPan;
            } else {
              radioGroup.value = '0';
            }
            formModel['defBatch'] = values[0].defBatch;

            if (values[0].office) {
              formModel['office.officeName'] = values[0].office.officeName;
              formModel['officeCode'] = values[0].office.officeCode;
            }
            if (values[0].basWare) {
              formModel['basWare.cwhname'] = values[0].basWare.cwhname;
              formModel['whCode'] = values[0].basWare.cwhcode;
            }
          },
        };
      },
      dynamicDisabled: computed(() => !record.value.isNewRecord),
      required: true,
    },
    {
      label: t('存货编码'),
      field: 'invCode',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      dynamicDisabled: true,
      required: true,
    },
    {
      label: t('存货名称'),
      field: 'basInv.invName',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
      dynamicDisabled: true,
      required: true,
    },
    {
      label: t('规格型号'),
      field: 'basInv.invStd',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      dynamicDisabled: true,
    },
    {
      label: t('单位'),
      field: 'basInv.unitName',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      dynamicDisabled: true,
    },
    {
      label: t('仓库名称'),
      field: 'basWare.cwhname',
      component: 'TreeSelect',
      required: true,
      //// record.value.djStatus 为1的时候才允许编辑
      dynamicDisabled: computed(() => record.value.djStatus != 1),
      // 如果
      componentProps: ({ formModel }) => {
        return {
          api: basWarehousetreeData,
          params: { isAll: true },
          canSelectParent: false,
          allowClear: true,
          onSelect: (value, node) => {
            console.log(node, 'node');
            formModel['whCode'] = node.id;
          },
        }
      }
    },
    {
      label: t('仓库编码'),
      field: 'whCode',
      component: 'Input',
      dynamicDisabled: true,
      componentProps: {
        maxlength: 100,
      },
      show: false,
    },
    {
      label: t('部门名称'),
      field: 'office.officeName',
      component: 'TreeSelect',
      required: true,
      dynamicDisabled: computed(() => record.value.djStatus != 1),
      componentProps: ({ formModel }) => {
        return {
          api: officeTreeData,
          params: { isAll: true, officeTypes: OfficeTypeEnum.SC },
          canSelectParent: false,
          allowClear: true,
          onSelect: (value, node) => {
            console.log(node);
            formModel['officeCode'] = node.id;
          },
        };
      },
    },
    {
      label: t('部门编码'),
      field: 'officeCode',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      dynamicDisabled: true,
      show: false,
    },
    {
      label: t('件数'),
      helpMessage: '自然吨',
      field: 'inum',
      component: 'Input',
      dynamicDisabled: computed(() => record.value.djStatus != 1),
      ifShow: computed(() => bfixExch.value == 0 ? false : true),
      componentProps: ({ formModel }) => {
        return {
          maxlength: 16,
          onBlur: (e) => {
            // formModel['ichangeRate'] ? formModel['iqty'] = (e.target.value * formModel['ichangeRate']).toFixed(NumEnum.scaleQty) : '';
            formModel['ichangeRate'] ? formModel['iqty'] = toNumberFixed(e.target.value * formModel['ichangeRate']) : '';
          },
        }
      },
      rules: [{ required: true }, { pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d{0,4})?$/, message: t('请输入一个数值，保留四位小数') }],
    },
    {
      label: t('含量'),
      field: 'ichangeRate',
      component: 'Input',
      dynamicDisabled: computed(() => record.value.djStatus != 1),
      componentProps: ({ formModel }) => {
        return {
          maxlength: 16,
          onBlur: (e) => {
            // formModel['inum'] ? formModel['iqty'] = ((formModel['inum'] * e.target.value)).toFixed(NumEnum.scaleQty) : '';
            formModel['inum'] ? formModel['iqty'] = toNumberFixed(formModel['inum'] * e.target.value) : '';
          },
        }
      },
      // rules 大于0，小于等于1
      // rules: [{ required: true },{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d{0,4})?$/, message: t('请输入一个数值，保留四位小数') }],
      rules: [
        { required: true },
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback(new Error(t('请输入一个数值')));
            } else if (isNaN(value) || value <= 0 || value >1) {
              callback(new Error(t('请输入一个0到1之间的数值')));
            } else {
              callback();
            }
          },
          trigger: 'blur'
        }
      ],
      // binvbatch.value 为 0 时不显示该字段
      ifShow: computed(() => bfixExch.value == 0 ? false : true),
    },
    {
      label: t('数量'),
      field: 'iqty',
      component: 'Input',
      // record.value.djStatus 为1 和 为2 的时候都可以编辑,
      dynamicDisabled: computed(() => bfixExch.value == 0 ? false : true || record.value.djStatus != 1 || record.value.djStatus != 2),
      // binvbatch.value 为 0 该字段可以手动输入
      rules: [{ required: true }, { pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d{0,4})?$/, message: t('请输入一个数值，保留四位小数') }],
    },
    {
      label: t('备注'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 500,
      },
      dynamicDisabled: computed(() => record.value.djStatus != 1),
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t(' '),
      field: 'white',
      component: 'FormGroup',
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('检验员'),
      field: 'checkBy',
      component: 'Select',
      componentProps: {
        dictType: 'mf_check_person',
        allowClear: true,
      },
      dynamicDisabled: computed(() => record.value.djStatus != 1),
      required: true,
    },
    {
      label: t('班次'),
      field: 'teamClass',
      component: 'Select',
      dynamicDisabled: computed(() => record.value.djStatus != 1),
      componentProps: {
        dictType: 'mf_team_class',
        allowClear: true,
      },
    },
    {
      label: t('班组'),
      field: 'cteam',
      component: 'Select',
      dynamicDisabled: computed(() => record.value.djStatus != 1),
      componentProps: ({ formModel }) => {
        return {
          dictType: 'mf_team',
           allowClear: true,
          onSelect: (values) => {
            console.log(values);
            if(values && formModel['cgrade'] && formModel['defBatch'] == '1'){
              formModel['lastChar'] = values + formModel['cgrade']
            }
          },
        };
      },
      required: true,
    },
    {
      label: t('品级'),
      field: 'cgrade',
      component: 'Select',
      dynamicDisabled: computed(() => record.value.djStatus != 1),
      componentProps: ({ formModel }) => {
        return {
          dictType: 'mf_grade',
           allowClear: true,
          onSelect: (values) => {
            console.log(values);
            if(formModel['cteam'] && values && formModel['defBatch'] == '1'){
              formModel['lastChar'] = formModel['cteam'] + values
            }
          },
        };
      },
      required: true,
    },
    {
      label: t('批次后2位'),
      field: 'lastChar',
      component: 'Input',
      // componentProps: {
      //   maxlength: 2,
      //   minlength: 2,
      // },
      dynamicDisabled: computed(() => record.value.djStatus != 1),
      componentProps: ({ formModel }) => {
        return {
          maxlength: 2,
          disabled:formModel.defBatch == '1'?true:false
        }
      },
      rules: [
        { required: true },
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback(new Error(t('')));
            } else if (value.length != '2') {
              callback(new Error(t('请输入2位数')));
            } else {
              callback();
            }
          },
          trigger: 'blur'
        }
      ],
    },
    {
      label: t('批次通用规则'),
      field: 'defBatch',
      component: 'Input',
      show:false
    },

    {
      label: t('批次信息'),
      field: 'picinfo',
      component: 'FormGroup',
      colProps: { lg: 24, md: 24 },
      ifShow: () => binvbatch.value == true,
    },
    {
      label: t('批次'),
      field: 'cbatch',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      colProps: { lg: 24, md: 24 },
      ifShow: () => binvbatch.value == true,
      slot: 'cbatch',
      dynamicDisabled: true,
    },
    {
      label: t('托盘信息'),
      field: 'tuopaninfo',
      component: 'FormGroup',
      colProps: { lg: 2, md: 24 },
    },

    {
      label: t(''),
      field: 'tuopaninfo1',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      colProps: { lg: 18, md: 24 },
      slot: 'tuopaninfo',
      show: true,
    },
    {
      label: t('包装名称'),
      field: 'packName',
      component: 'Input',
      dynamicDisabled: true,
    },
    {
      label: t('托盘容量'),
      field: 'packSize',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      dynamicDisabled: true,
      // required: true,
      // required : computed(()=>{  })
    },
    {
      label: t('单件容量'),
      field: 'pieceQty',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      dynamicDisabled: true,
    },

    {
      label: t('当前序号'),
      helpMessage: '当前已喷码的最大序号',
      field: 'maxTpSeq',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
      dynamicDisabled: true,
    },
    {
      label: t('入库信息'),
      field: 'white',
      component: 'FormGroup',
      colProps: { lg: 24, md: 24 },
    },

    {
      label: t('累计入库数量'),
      field: 'sumQty',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
      dynamicDisabled: true,
    },
    {
      label: t('累计入库件数'),
      field: 'sumNum',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
      dynamicDisabled: true,
    },
    {
      label: t('制单人'),
      field: 'createByName',
      component: 'Input',
      componentProps: {
        maxlength: 50,
      },
      ifShow: false,
    },
    {
      label: t('修改人'),
      field: 'updateByName',
      component: 'Input',
      componentProps: {
        maxlength: 50,
      },
      ifShow: false,
    },
    
  ];

  const [registerForm, { resetFields, setFieldsValue, validate, getForm, updateSchema }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await wmsRd10Form(data);
    console.log(res,'res===');
    
    record.value = (res.wmsRd10 || {}) as WmsRd10;
    record.value.__t = new Date().getTime();
    if(record.value.basInv){
      bfixExch.value = record.value.basInv.bfixExch;
      binvbatch.value = record.value.basInv.binvbatch;
    }
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  function handleRadioGroupClick(e: any = undefined) {
      const key = e?.target?.value || radioGroup.value;
      radioGroup.value = key;
  }

  async function handleValidate(_event: any, formData: any) {
    try {
      loadingRef.value = true;
      const data = event?.formData || (await validate()); // 接受 BpmButton 传递过来的表单数据
      data.bpm = Object.assign(data.bpm || {}, record.value.bpm); // 流程信息
      // data.status = record.value.status; // 提交状态
      data.status = 9; // 草稿状态
      data.djStatus = data.djStatus ? data.djStatus : 1;
      data.bneedTuoPan = radioGroup.value
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        djno: record.value.djno,
      };
      // console.log('submit', params, data, record);
      const res = await wmsRd10Save(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      setDrawerProps({ confirmLoading: false });
      record.value.isNewRecord = false;
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      loadingRef.value = false;
      setDrawerProps({ confirmLoading: false });
    }
  }

  async function handleSubmit(event: any) {
    try {
      if(record.value.isNewRecord){
        showMessage('请先保存单据再发送通知！！');
        return;
      }
      loadingRef.value = true;
      // const data = event?.formData || (await validate()); // 接受 BpmButton 传递过来的表单数据
      // event?.formData 和 await validate() 传递
      const data = await validate();
      data.bpm = Object.assign(data.bpm || {}, record.value.bpm); // 流程信息
      data.status = 4; // 提交状态
      data.djStatus = data.djStatus ? data.djStatus : 1;
      data.bneedTuoPan = radioGroup.value
      console.log('data', data);
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        djno: record.value.djno,
      };
      // console.log('submit', params, data, record);
      const res = await wmsRd10Save(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      record.value.isNewRecord = false;
      setDrawerProps({ confirmLoading: false });
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      loadingRef.value = false;
      setDrawerProps({ confirmLoading: false });
    }
  }

  async function handleCbatchPreview(model: any, field: any) {
    console.log(model,'model===');
    // console.log(model, field, 'model, field===');
    if (!model.addCode) {
      showMessage('请选择批号！！');
      return;
    }
    if (!model.checkBy) {
      showMessage('请选择检验员！！');
      return;
    }
    
    if (!model.cteam) {
      showMessage('请选择班组！！');
      return;
    }
    if (!model.cgrade) {
      showMessage('请选择品级！！');
      return;
    }
    if (!model.lastChar) {
      showMessage('请输入批次后2位！！');
      return;
    }
    
    // model.ddate M2 日期格式转换为 YYYY-MM-DD
    const ddate = model.ddate.$y + '-' + (model.ddate.$M + 1) + '-' + model.ddate.$D;
    const params = {
      invCode: model.invCode,
      addCode: model.addCode,
      checkBy: model.checkBy,
      teamClass: model.teamClass,
      cteam: model.cteam,
      cgrade: model.cgrade,
      ddate: ddate,
      invCls: model.invCls,
      lastChar: model.lastChar,
    }
    const res  = await wmsRd10CreateBatch(params);
    // 返回 cbatch
    model.cbatch = res.data.cbatch;
    record.value.cbatch = res.data.cbatch;
  }

  async function handleSuccess() {
    emit('success');
  }

  async function handleSelect(selectData) {
    await nextTick();
    // console.log(,'record.value===');
    const getFields = basicFormModel.value.getFieldsValue()
    const data = {
      ...getFields,
      packName: selectData[0]['packName'],
      pieceQty: selectData[0]['pieceQty'],
      packSize: selectData[0]['packSize'],
    }
    setFieldsValue(data);

  }

  async function handleBasInvPack(model: any, field: any) {
    console.log(model, field, 'model, field===',listSelectRef.value);
    if (!model.invCode) {
      showMessage('先请选择存货！！');
    }else{
      selectListRef.value = []
        queryParams.value = {
          invCode: model.invCode
        }
        setTimeout(()=>{
          listSelectRef.value.openSelectModal();
        })
    }   
  }

  function initialize(task: Recordable, bpm: Recordable) {
    console.log(task, 'task===', bpm);
  }
</script>
<style lang="less" scoped>
.jeesite-bpm-btns{
  width: auto !important;
}
.flex{
  margin-left: 20px;
  display: inline-block;
  button{
    margin-right: 20px;
    border: 1px solid #0096c7;
  }
}
</style>