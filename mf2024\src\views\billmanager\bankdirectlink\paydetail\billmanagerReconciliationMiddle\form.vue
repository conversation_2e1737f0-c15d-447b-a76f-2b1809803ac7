<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'billmanager:bankdirectlink:paydetail:billmanagerReconciliationMiddle:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsBillmanagerBankdirectlinkPaydetailBillmanagerReconciliationMiddleForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BillmanagerReconciliationMiddle, billmanagerReconciliationMiddleSave, billmanagerReconciliationMiddleForm } from '/@/api/billmanager/bankdirectlink/paydetail/billmanagerReconciliationMiddle';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('billmanager.bankdirectlink.paydetail.billmanagerReconciliationMiddle');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<BillmanagerReconciliationMiddle>({} as BillmanagerReconciliationMiddle);

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增billmanager_reconciliation_middle') : t('编辑billmanager_reconciliation_middle'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('请求序列码'),
      field: 'requestSn',
      component: 'Input',
      componentProps: {
        maxlength: 30,
      },
    },
    {
      label: t('本方账号'),
      field: 'accNo1',
      component: 'Input',
      componentProps: {
        maxlength: 50,
      },
    },
    {
      label: t('币种'),
      field: 'currCod',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
    },
    {
      label: t('本方账号名称'),
      field: 'accName',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('本方账号开户'),
      field: 'accOrgan',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('本方账号状态'),
      field: 'accState',
      component: 'Input',
      componentProps: {
        maxlength: 30,
      },
    },
    {
      label: t('利率'),
      field: 'intr',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('定位串'),
      field: 'postStr',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('保留字段'),
      field: 'flag',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
    },
    {
      label: t('生成文件定位'),
      field: 'fileLocStr',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('交易日期'),
      field: 'tranDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('交易时间'),
      field: 'tranTime',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
    },
    {
      label: t('凭证种类'),
      field: 'creTyp',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('凭证号码'),
      field: 'creNo',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('摘要'),
      field: 'message',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('发生额'),
      field: 'amt',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('余额'),
      field: 'amt1',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('借贷标志'),
      field: 'flag1',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
    },
    {
      label: t('对方账号'),
      field: 'accNo2',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('对方户名'),
      field: 'accName1',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('交易钞汇标志'),
      field: 'flag2',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
    },
    {
      label: t('交易流水号'),
      field: 'tranFlow',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('企业支付流水'),
      field: 'bflow',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('活存账户明细号'),
      field: 'detNo',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('备注'),
      field: 'det',
      component: 'Input',
      componentProps: {
        maxlength: 500,
      },
    },
    {
      label: t('实际交易日期'),
      field: 'realTranDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('关联账号'),
      field: 'rltvAccNo',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('对方账户开户行名称'),
      field: 'cadBankNm',
      component: 'Input',
      componentProps: {
        maxlength: 500,
      },
    },
    {
      label: t('全局跟踪号'),
      field: 'ovrlsttnTrckno',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('外系统支付备注'),
      field: 'exoStmRyRmrk',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('对账起止日期'),
      field: 'dzBeginEndDate',
      component: 'Input',
      componentProps: {
        maxlength: 50,
      },
    },
    {
      label: t('U8单据号'),
      field: 'u8Djno',
      component: 'Input',
      componentProps: {
        maxlength: 120,
      },
    },
    {
      label: t('推送状态'),
      field: 'pushState',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
    },
    {
      label: t('推送时间'),
      field: 'pushDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('推送结果'),
      field: 'pushResult',
      component: 'Input',
      componentProps: {
        maxlength: 2000,
      },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await billmanagerReconciliationMiddleForm(data);
    record.value = (res.billmanagerReconciliationMiddle || {}) as BillmanagerReconciliationMiddle;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await billmanagerReconciliationMiddleSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
