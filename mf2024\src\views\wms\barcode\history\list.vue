<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable" @fetchSuccess="fetchSuccess">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #firstColumn="{ record }">
        <span class="cursor-pointer" @click="expandCollapse(record)">
          ( {{ record.posCode }} )
        </span>
        <a @click="handleForm({ posCode: record.posCode })">
          {{ record.posCode }}
        </a>
      </template>
    </BasicTable>
    <!-- <InputForm @register="registerDrawer" @success="handleSuccess" /> -->
    <PrintModal @register="registerPrintModal" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsWmsBarcodeHistoryList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, watch, nextTick } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { barcodeHistoryList } from '/@/api/wms/barcode/history/history';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';
  import PrintModal from '/@/components/print/printModal.vue';

  const props = defineProps({
    treeCode: String,
  });

  const { ctxPath, ctxAdminPath } = useGlobSetting();

  const { t } = useI18n('bas.pos.basPosition');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: router.currentRoute.value.meta.title || t('产品标签导出管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('文件名称'),
        field: 'pdfName',
        component: 'Input',
      },
      {
        label: t('打印人'),
        field: 'createByName',
        component: 'Input',
      },
      {
        label: t('文件来源'),
        field: 'ctype',
        component: 'Select',
        componentProps: {
          dictType: 'bar_source_type',
          allowClear: true,
        },
      },
      {
        label: t('打印时间'),
        field: 'updateDate',
        component: 'RangePicker',
        componentProps: { },
      },
    ],
    fieldMapToTime: [['updateDate', ['updateDate_gte', 'updateDate_lte']]],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('文件名称'),
      dataIndex: 'pdfName',
      sorter: true,
      key: 'a.pdf_name',
      width: 200,
      align: 'left',
    },
    {
      title: t('打印日期'),
      dataIndex: 'ddate',
      sorter: true,
      key: 'a.ddate',
      width: 130,
      align: 'left',
    },
    {
      title: t('打印人'),
      dataIndex: 'createByName',
      sorter: true,
      key: 'a.create_by_name',
      width: 100,
      align: 'left',
    },
    {
      title: t('打印时间'),
      dataIndex: 'updateDate',
      sorter: true,
      key: 'a.update_date',
      width: 150,
      align: 'left',
    },
    {
      title: t('文件来源'),
      dataIndex: 'ctype',
      sorter: true,
      key: 'a.ctype',
      dictType: 'bar_source_type',
      width: 130,
      align: 'left',
    },
    {
      title: t('打印标记'),
      dataIndex: 'remarks',
      sorter: true,
      key: 'a.remarks',
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
    {
        icon: 'i-ant-design:download-outlined',
        title: t('下载文件'),
        onClick: handleDownload.bind(this, { id: record.uploadID }),
      },
    ],
  };
  const [registerPrintModal, { openModal: openPrintModal }] = useModal();
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [
    registerTable,
    { reload, expandAll, collapseAll, expandCollapse, getForm, getSelectRows },
  ] = useTable({
    api: barcodeHistoryList,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    // isTreeTable: true,
    pagination: true,
    canResize: true,
    rowSelection: {
      type: 'checkbox',
    },
  });

  watch(
    () => props.treeCode,
    async () => {
      await getForm().setFieldsValue({
        posCode: props.treeCode,
      });
      reload();
    },
  );

  function fetchSuccess() {
    if (props.treeCode) {
      nextTick(expandAll);
    }
  }

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }
  // 货位打印
  async function handleDownload(record: Recordable) {
    downloadByUrl({ url: ctxPath + record.id });
  }

  function handleSuccess() {
    reload();
  }
</script>
