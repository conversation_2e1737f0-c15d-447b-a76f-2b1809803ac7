<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'layout:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherFormTabConfigForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { LayVoucherFormTabConfig, layVoucherFormTabConfigSave, layVoucherFormTabConfigForm } from '../../../../api/layout/form/layVoucherFormTabConfig';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('layout.layVoucherFormTabConfig');
  const { showMessage } = useMessage();
  const record = ref<LayVoucherFormTabConfig>({} as LayVoucherFormTabConfig);
  const getTitle = computed(() => ({
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增多页签') : t('编辑多页签'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('布局标志'),
      field: 'viewCode',
      dynamicDisabled: true,
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
      required: true,
    },
    {
      label: t('标签页名称'),
      field: 'tabName',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      required: true,
    },
    {
      label: t('标签页地址'),
      field: 'tabAddress',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('顺序号'),
      field: 'sortNum',
      component: 'Input',
      componentProps: {
        maxlength: 8,
      },
      
      rules: [{required: true,},{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 24, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await layVoucherFormTabConfigForm(data);
    record.value = (res.layVoucherFormTabConfig || {}) as LayVoucherFormTabConfig;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await layVoucherFormTabConfigSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
