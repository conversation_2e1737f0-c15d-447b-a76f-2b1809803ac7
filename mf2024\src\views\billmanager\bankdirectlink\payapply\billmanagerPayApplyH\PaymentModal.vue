<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicModal
    v-bind="$attrs"
    :showFooter="true"
    @register="registerPaymentModal"
    @ok="handlePaymentSubmit"
    width="80%"
    title="付款详情"
  >
    <BasicForm @register="registerPaymentForm">
      <template #paymentDetailList>
        <div class="mb-2 p-3 bg-gray-50 rounded border">
          <div class="text-sm text-gray-600 mb-2">
            总金额: <span class="font-semibold text-blue-600">{{ amount.toFixed(2) }}</span> |
            已分配: <span class="font-semibold text-green-600">{{ allocatedAmount.toFixed(2) }}</span> |
            剩余: <span class="font-semibold" :class="remainingAmount >= 0 ? 'text-orange-600' : 'text-red-600'">
              {{ remainingAmount.toFixed(2) }}
            </span>
          </div>
          <div class="text-xs" :class="Math.abs(remainingAmount) < 0.01 ? 'text-green-600' : 'text-red-600'">
            <Icon :icon="Math.abs(remainingAmount) < 0.01 ? 'i-ant-design:check-circle-outlined' : 'i-ant-design:exclamation-circle-outlined'" />
            {{ Math.abs(remainingAmount) < 0.01 ? '✓ 金额分配完成，可以提交' : '⚠ 付款详情总金额必须等于总金额才能提交' }}
          </div>
        </div>
        <BasicTable
          @register="registerPaymentDetailTable"
          @row-click="handlePaymentDetailRowClick"
          @edit-change="handleEditChange"
        />
        <a-button
          class="mt-2"
          @click="handlePaymentDetailAdd"
          type="primary"
          :disabled="Math.abs(remainingAmount) < 0.01"
        >
          <Icon icon="i-ant-design:plus-circle-outlined" /> {{ t('新增') }}
        </a-button>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup name="PaymentModal">
  import { ref, computed, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useDict } from '/@/components/Dict';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { officeTreeData } from '/@/api/sys/office';
  import { OfficeTypeEnum } from '/@/enums/defEnum';
  import { billmanagerPayApplyHPayConfirmation } from '/@/api/billmanager/bankdirectlink/payapply/billmanagerPayApplyH';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('bankdirectlink.payapply.billmanagerPayApplyH');
  const { showMessage } = useMessage();
  const { getDictLabel } = useDict();

  // 当前付款记录
  const currentPaymentRecord = ref<Recordable>({});

  // 总金额和已分配金额
  const amount = ref<number>(0);
  const allocatedAmount = ref<number>(0);

  // 计算剩余可分配金额
  const remainingAmount = computed(() => {
    return amount.value - allocatedAmount.value;
  });
  // 付款详情弹窗表单配置
  const paymentFormSchemas: FormSchema[] = [
    {
      label: t('供应商'),
      field: 'vencode',
      fieldLabel: 'vendor.officeName',
      component: 'TreeSelect',
      componentProps: {
        api: officeTreeData,
        params: { isLoadUser: true, userIdPrefix: '', isAll: true, officeTypes: OfficeTypeEnum.WL },
        canSelectParent: false,
        allowClear: true,
        maxlength: 64,
      },
      required: true,
      colProps: { lg: 12, md: 24 },
      dynamicDisabled: true,
    },
    {
      label: t('币种'),
      field: 'cexchName',
      component: 'Select',
      componentProps: {
        dictType: 'mf_bizhong',
        allowClear: true,
      },
      required: true,
      colProps: { lg: 12, md: 24 },
      dynamicDisabled: true,
    },
    {
      label: t('总金额'),
      field: 'amount',
      component: 'Input',
      componentProps: {
        maxlength: 16,
        placeholder: '请输入总金额',
      },
      required: true,
      colProps: { lg: 12, md: 24 },
      dynamicDisabled: true,
    },
    {
      label: t('付款详情'),
      field: 'paymentDetailList',
      component: 'Input',
      colProps: { lg: 24, md: 24 },
      slot: 'paymentDetailList',
    },
  ];

  const [registerPaymentForm, {
    resetFields: resetPaymentFields,
    setFieldsValue: setPaymentFieldsValue,
    validate: validatePaymentForm,
  }] = useForm({
    labelWidth: 120,
    schemas: paymentFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  // 付款详情表格配置
  const [registerPaymentDetailTable, paymentDetailTable] = useTable({
    actionColumn: {
      width: 80,
      actions: (_record: Recordable) => [
        {
          icon: 'i-ant-design:delete-outlined',
          color: 'error',
          popConfirm: {
            title: '是否确认删除',
            confirm: handlePaymentDetailDelete.bind(this, _record),
          },
        },
      ],
    },
    rowKey: 'id',
    pagination: false,
    bordered: true,
    size: 'small',
    inset: true,
    // 添加表格数据变化监听
    // afterEditCancel: () => {
    //   setTimeout(() => calculateAllocatedAmount(), 100);
    // },
    // afterEditSubmit: () => {
    //   setTimeout(() => calculateAllocatedAmount(), 100);
    // },
  });

  const [registerPaymentModal, { closeModal: closePaymentModal }] = useModalInner(async (data) => {
    console.log('registerPaymentModal', data);

    // paymentDetailTable.setTableData([]);
    // 把 data.paymentDetailList 赋值给paymentDetailTable 的 tableData
    currentPaymentRecord.value = data;

    // 设置总金额
    amount.value = parseFloat(data.orgAmount || '0');

    // 设置弹窗表单初始值
    await resetPaymentFields();
    setPaymentFieldsValue({
      vencode: data.vendor.officeName || '',
      cexchName: data.cexchName || '',
      amount: data.orgAmount || '',
    });

    // 设置付款详情表格数据
    setTimeout(async () => {
      setPaymentDetailTableData();

      // 处理现有数据，标记为非新增记录
      const existingData = (data.paymentDetailList || []).map((item: any) => ({
        ...item,
        isNewRecord: false, // 现有数据标记为非新增
        editable: true,
      }));

      console.log('初始化付款详情数据:', existingData);
      paymentDetailTable.setTableData(existingData);

      // 初始化时计算已分配金额
      calculateAllocatedAmount();
    }, 100);
  });

  // 安全获取表格数据
  function getTableDataSafely(): any[] {
    try {
      const tableData = paymentDetailTable.getDataSource();
      return Array.isArray(tableData) ? tableData : [];
    } catch (error) {
      console.warn('获取表格数据失败:', error);
      return [];
    }
  }

  // 计算已分配金额
  function calculateAllocatedAmount() {
    try {
      const tableData = getTableDataSafely();
      let total = 0;

      tableData.forEach((record: any) => {
        if (record && typeof record === 'object') {
          // 尝试从多个可能的字段获取金额值
          let amount = 0;
          if (record.editValueRefs && record.editValueRefs.orgAmount) {
            // 从编辑状态的值获取
            amount = parseFloat(record.editValueRefs.orgAmount || '0');
          } else {
            // 从记录本身获取
            amount = parseFloat(record.orgAmount || '0');
          }

          if (!isNaN(amount) && amount > 0) {
            total += amount;
          }
        }
      });

      allocatedAmount.value = total;
      console.log('计算已分配金额:', total, '总金额:', amount.value, '表格数据:', tableData.map(r => ({ orgAmount: r.orgAmount, editAmount: r.editValueRefs?.orgAmount?.value })));
    } catch (error) {
      console.error('计算已分配金额失败:', error);
      allocatedAmount.value = 0;
    }
  }



  // 设置付款详情表格数据和列配置
  function setPaymentDetailTableData() {
    try {
      paymentDetailTable.setColumns([
        {
          title: t('行号'),
          dataIndex: 'irowno',
          width: 100,
          align: 'left',
        },
        {
          title: t('付款方式'),
          dataIndex: 'footType',
          width: 150,
          align: 'left',
          editRow: true,
          editComponent: 'Select',
          dictType: 'mf_pay_method',
          editComponentProps: {
            dictType: 'mf_pay_method',
            required: true,
            allowClear: true,
          },
          editRule: (text: any, record: Recordable) => {
            return new Promise<void>((resolve, reject) => {
              if (!text || text === '') {
                return reject('请选择付款方式');
              }

              console.log('text===', text);

              // 检查付款方式是否重复
              const tableData = getTableDataSafely();
              const duplicateCount = tableData.filter((r: any) => {
                if (r.id === record.id) return false; // 排除当前编辑的行
                return r.footType === text || r.editValueRefs?.footType === text;
              }).length;

              if (duplicateCount > 0) {
                const paymentMethodLabel = getDictLabel('mf_pay_method', text, text);
                return reject(`付款方式"${paymentMethodLabel}"不能重复，请选择其他付款方式`);
              }

              resolve();
            });
          },
        },
        {
          title: t('科目'),
          dataIndex: 'subject',
          width: 200,
          align: 'left',
          editRow: true,
          editComponent: 'Select',
          dictType: 'csettleType',
          editComponentProps: {
            // dictType: 'mf_subject',
            dictType: 'csettleType',
            required: true,
            allowClear: true,
          },
          editRule: (text: any, _record: Recordable) => {
            return new Promise<void>((resolve, reject) => {
              if (!text || text === '') {
                return reject('请选择科目');
              }
              resolve();
            });
          },
        },
        {
          title: t('金额'),
          dataIndex: 'orgAmount',
          width: 150,
          align: 'left',
          editRow: true,
          editComponent: 'Input',
          editComponentProps: {
            maxlength: 16,
            required: true,
            type: 'number',
            min: 0,
            step: 0.01,
            placeholder: '请输入金额',
          },
          editRule: (text: any, record: Recordable) => {
            return new Promise<void>((resolve, reject) => {
              const value = parseFloat(text || '0');

              // 验证金额必须大于0
              if (!text || value <= 0) {
                return reject('金额必须大于0');
              }
              console.log('金额:', value, '总分配金额:', allocatedAmount.value);
              // 计算所有行的总金额（包括当前正在编辑的行）
              const tableData = getTableDataSafely();
              let totalAllocated = 0;

              tableData.forEach((r: any) => {
                if (r && typeof r === 'object') {
                  console.log('当前行:', r);
                  if (r.id === record.id) {
                    // 当前编辑的行使用新输入的值
                    totalAllocated += value;
                  } else {
                    // 其他行使用现有值
                    let amount = 0;
                    if (r.editValueRefs && r.editValueRefs.orgAmount) {
                      amount = parseFloat(r.editValueRefs.orgAmount || '0');
                    } else {
                      amount = parseFloat(r.orgAmount || '0');
                    }
                    if (!isNaN(amount) && amount > 0) {
                      totalAllocated += amount;
                    }
                  }
                }
              });

              if (totalAllocated > amount.value) {
                return reject(`所有金额总和(${totalAllocated.toFixed(2)})不能超过总金额(${amount.value.toFixed(2)})`);
              }

              console.log('金额验证通过:', value, '所有行总金额:', totalAllocated.toFixed(2));

              // 验证通过后，立即更新已分配金额显示
              allocatedAmount.value = totalAllocated;

              resolve();
            });
          },
        },
      ]);
      // 初始化空数据或从当前记录加载数据
      paymentDetailTable.setTableData([]);
    } catch (error) {
      console.error('设置付款详情表格数据失败:', error);
    }
  }

  // 付款详情表格行点击事件
  function handlePaymentDetailRowClick(record: Recordable) {
    record.onEdit?.(true, false);
  }

  // 处理表格编辑变化事件
  function handleEditChange(data: any) {
    console.log('表格编辑变化:', data);
    // 如果是金额列发生变化，重新计算已分配金额
    if (data.column && data.column.dataIndex === 'orgAmount') {
      console.log('金额列变化，当前值:', data.value, '记录:', data.record);

      // 立即更新已分配金额（使用编辑中的值）
      const tableData = getTableDataSafely();
      console.log('开始计算已分配金额，表格数据:', tableData);
      let total = 0;

      tableData.forEach((record: any) => {
        if (record && typeof record === 'object') {
          if (record.id === data.record.id) {
            // 当前编辑的行使用新值
            const newValue = parseFloat(data.value || '0');
            if (!isNaN(newValue) && newValue > 0) {
              total += newValue;
            }
          } else {
            // 其他行使用现有值
            let amount = 0;
            if (record.editValueRefs && record.editValueRefs.orgAmount) {
              amount = parseFloat(record.editValueRefs.orgAmount || '0');
            } else {
              amount = parseFloat(record.amount || '0');
            }
            if (!isNaN(amount) && amount > 0) {
              total += amount;
            }
          }
        }
      });

      allocatedAmount.value = total;
      console.log('实时更新已分配金额:', total);
    }
  }

  // 新增付款详情
  function handlePaymentDetailAdd() {
    try {
      paymentDetailTable.insertTableDataRecord({
        id: new Date().getTime() + Math.floor(Math.random() * 1000),
        irowno: currentPaymentRecord.value.irowno,
        footType: '',
        subject: '',
        orgAmount: '',
        isNewRecord: true,
        editable: true,
      });
    } catch (error) {
      console.error('新增付款详情失败:', error);
      showMessage('新增付款详情失败，请重试');
    }
  }

  // 删除付款详情
  function handlePaymentDetailDelete(record: Recordable) {
    try {
      paymentDetailTable.deleteTableDataRecord(record);
      // 删除后重新计算已分配金额
      setTimeout(() => {
        calculateAllocatedAmount();
      }, 50);
    } catch (error) {
      console.error('删除付款详情失败:', error);
      showMessage('删除付款详情失败，请重试');
    }
  }

  // 付款详情弹窗提交
  async function handlePaymentSubmit() {
    try {
      const formData = await validatePaymentForm();

      // 获取表格数据
      const tableData = getTableDataSafely();
      if (tableData.length === 0) {
        showMessage('请至少添加一条付款详情');
        return;
      }

      // 验证表格数据
      for (const record of tableData) {
        if (!(await record.onEdit?.(false, true))) {
          showMessage('请完善付款详情信息');
          return;
        }
        record.parentCode = currentPaymentRecord.value.parent.id;
        record.irowno = currentPaymentRecord.value.irowno;
        record.parentId = currentPaymentRecord.value.id;
      }

      // 检查付款方式是否重复
      const paymentMethods = tableData.map((record) => record.footType).filter(Boolean);
      const uniquePaymentMethods = [...new Set(paymentMethods)];

      if (paymentMethods.length !== uniquePaymentMethods.length) {
        // 找出重复的付款方式
        const duplicates = paymentMethods.filter(
          (method, index) => paymentMethods.indexOf(method) !== index,
        );
        const uniqueDuplicates = [...new Set(duplicates)];
        // 将 key 转换为 label 显示
        const duplicateLabels = uniqueDuplicates.map((key) =>
          getDictLabel('mf_pay_method', key, key),
        );
        showMessage(`付款方式不能重复，发现重复的付款方式: ${duplicateLabels.join(', ')}`);
        return;
      }

      // 最终验证总金额 - 必须完全相等
      calculateAllocatedAmount();
      if (Math.abs(allocatedAmount.value - amount.value) > 0.01) {
        const difference = amount.value - allocatedAmount.value;
        if (difference > 0) {
          showMessage(
            `付款详情总金额(${allocatedAmount.value.toFixed(2)})必须等于总金额(${amount.value.toFixed(2)})，还需分配${difference.toFixed(2)}`,
          );
        } else {
          showMessage(
            `付款详情总金额(${allocatedAmount.value.toFixed(2)})必须等于总金额(${amount.value.toFixed(2)})，超出${Math.abs(difference).toFixed(2)}`,
          );
        }
        return;
      }
      // const data = formData;
      formData.lsit = tableData;
      formData.id = currentPaymentRecord.value.id;
      const params: any = {
        isNewRecord: true,
        id: currentPaymentRecord.value.id,
      };
      // const newFormData = Object.assign(formData, params);
      // console.log('submit', params, data, record);
      const res = await billmanagerPayApplyHPayConfirmation(params, formData);

      if (res.result === 'true') {
        console.log('付款详情提交数据:', {
          formData,
          tableData,
          originalRecord: currentPaymentRecord.value,
          amount: amount.value,
          allocatedAmount: allocatedAmount.value,
        });

        showMessage('付款详情保存成功');

        // 检查是否有新增的付款详情数据
        const hasNewRecords = tableData.some((record: any) => record.isNewRecord === true);
        const hasExistingRecords = tableData.some((record: any) => record.isNewRecord === false);
        console.log('包含新增记录:', hasNewRecords, '包含现有记录:', hasExistingRecords, '表格数据:', tableData);

        // 始终触发 success 事件，让父组件处理数据更新（新增或修改）
        console.log('触发 success 事件，让父组件处理数据更新');
        emit('success', {
          formData,
          tableData,
          originalRecord: currentPaymentRecord.value,
          hasNewRecords,
          hasExistingRecords
        });

        closePaymentModal();
      }
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    }
  }
</script>
