/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface LayVoucherView extends BasicModel<LayVoucherView> {
  code?: string; // 布局标志
  name?: string; // 布局名称
  layType?: string; // 布局类型
  vouchCode?: string; // 基础单据
}

export const layVoucherViewList = (params?: LayVoucherView | any) =>
  defHttp.get<LayVoucherView>({ url: adminPath + '/layout/layVoucherView/list', params });

export const layVoucherViewListData = (params?: LayVoucherView | any) =>
  defHttp.post<Page<LayVoucherView>>({ url: adminPath + '/layout/layVoucherView/listData', params });

export const layVoucherViewForm = (params?: LayVoucherView | any) =>
  defHttp.get<LayVoucherView>({ url: adminPath + '/layout/layVoucherView/form', params });

export const layVoucherViewSave = (params?: any, data?: LayVoucherView | any) =>
  defHttp.postJson<LayVoucherView>({ url: adminPath + '/layout/layVoucherView/save', params, data });

export const layVoucherViewDisable = (params?: LayVoucherView | any) =>
  defHttp.get<LayVoucherView>({ url: adminPath + '/layout/layVoucherView/disable', params });

export const layVoucherViewEnable = (params?: LayVoucherView | any) =>
  defHttp.get<LayVoucherView>({ url: adminPath + '/layout/layVoucherView/enable', params });

export const layVoucherViewDelete = (params?: LayVoucherView | any) =>
  defHttp.get<LayVoucherView>({ url: adminPath + '/layout/layVoucherView/delete', params });
