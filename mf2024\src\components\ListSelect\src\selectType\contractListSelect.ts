import { useI18n } from '/@/hooks/web/useI18n';
import { BasicColumn, BasicTableProps, FormProps } from '/@/components/Table';
import { viewContractListData } from '/@/api/billmanager/bankdirectlink/contract/viewContract';

const { t } = useI18n('sys.viewContract');

const modalProps = {
  title: t('合同选择'),
};

const searchForm: FormProps = {
  baseColProps: { lg: 6, md: 8 },
  labelWidth: 90,
  schemas: [
    {
      label: t('合同编码'),
      field: 'htcode',
      component: 'Input',
    },
    {
      label: t('合同名称'),
      field: 'htname',
      component: 'Input',
    },
    {
      label: t('供应商编码'),
      field: 'vencode',
      component: 'Input',
    },
    {
      label: t('供应商名称'),
      field: 'venname',
      component: 'Input',
    },
    {
      label: t('预算金额'),
      field: 'amount',
      component: 'Input',
    },
    {
      label: t('已使用金额'),
      field: 'useAmount',
      component: 'Input',
    },
    {
      label: t('使用使用金额'),
      field: 'syUseMount',
      component: 'Input',
    },
  ],
};

const tableColumns: BasicColumn[] = [
  {
    title: t('合同编码'),
    dataIndex: 'htcode',
    key: 'a.htcode',
    sorter: true,
    width: 230,
    align: 'left',
    slot: 'firstColumn',
  },
  {
    title: t('合同名称'),
    dataIndex: 'htname',
    key: 'a.htname',
    sorter: true,
    width: 130,
    align: 'left',
  },
  {
    title: t('供应商编码'),
    dataIndex: 'vencode',
    key: 'a.vencode',
    sorter: true,
    width: 130,
    align: 'left',
  },
  {
    title: t('供应商名称'),
    dataIndex: 'venname',
    key: 'a.venname',
    sorter: true,
    width: 130,
    align: 'left',
  },
  {
    title: t('预算金额'),
    dataIndex: 'amount',
    key: 'a.amount',
    sorter: true,
    width: 130,
    align: 'right',
  },
  {
    title: t('已使用金额'),
    dataIndex: 'useAmount',
    key: 'a.use_amount',
    sorter: true,
    width: 130,
    align: 'right',
  },
  {
    title: t('使用使用金额'),
    dataIndex: 'syUseMount',
    key: 'a.sy_use_mount',
    sorter: true,
    width: 130,
    align: 'right',
  },
];

const tableProps: BasicTableProps = {
  api: viewContractListData,
  beforeFetch: (params) => {
    params['isAll'] = true;
    return params;
  },
  columns: tableColumns,
  formConfig: searchForm,
  rowKey: 'htcode',
};

export default {
  modalProps,
  tableProps,
  itemCode: 'htcode',
  itemName: 'htcode',
  isShowCode: false,
};
