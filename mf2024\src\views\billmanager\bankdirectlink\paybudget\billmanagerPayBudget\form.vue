<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'bankdirectlink:paybudget:billmanagerPayBudget:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsBankdirectlinkPaybudgetBillmanagerPayBudgetForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import {
    BillmanagerPayBudget,
    billmanagerPayBudgetSave,
    billmanagerPayBudgetForm,
  } from '/@/api/billmanager/bankdirectlink/paybudget/billmanagerPayBudget';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('bankdirectlink.paybudget.billmanagerPayBudget');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<BillmanagerPayBudget>({} as BillmanagerPayBudget);

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增资金预算') : t('编辑资金预算'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('预算年月'),
      field: 'yearMonth',
      component: 'Input',
      componentProps: {
        maxlength: 6,
      },
      required: true,
    },
    {
      label: t('合同编码'),
      field: 'htno',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('合同名称'),
      field: 'htname',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('合同金额'),
      field: 'htAmount',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('资金用途'),
      field: 'useOfFunds',
      component: 'Select',
      componentProps: {
        dictType: 'mf_amount_use',
        allowClear: true,
      },
      required: true,
    },
    {
      label: t('供应商编码'),
      field: 'venCode',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('供应商名称'),
      field: 'venName',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('项目编码'),
      field: 'prjCode',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('项目名称'),
      field: 'prjName',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('部门编码'),
      field: 'deptCode',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      required: true,
    },
    {
      label: t('部门名称'),
      field: 'deptName',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('预算金额'),
      field: 'amount',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ required: true }, { pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('已使用金额'),
      field: 'useAmount',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('应付款余额'),
      field: 'syyfAmount',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('单据状态'),
      field: 'cstatus',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
      required: true,
    },
    {
      label: t('导入批号'),
      field: 'beachId',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      required: true,
    },
    {
      label: t('创建人名称'),
      field: 'createByName',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      required: true,
    },
    {
      label: t('备注'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 4000,
      },
      colProps: { lg: 24, md: 24 },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await billmanagerPayBudgetForm(data);
    record.value = (res.billmanagerPayBudget || {}) as BillmanagerPayBudget;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await billmanagerPayBudgetSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
