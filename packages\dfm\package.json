{"name": "@jeesite/dfm", "version": "5.12.1", "private": true, "type": "module", "scripts": {"type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "uninstall": "rimraf node_modules", "update": "ncu -u"}, "dependencies": {"@jeesite/dfm-lib": "5.12.1-rc.1", "@vueuse/core": "13.1.0", "dayjs": "1.11.13", "less": "4.3.0", "lodash-es": "4.17.21", "monaco-editor": "0.52.2", "vuedraggable": "4.1.0", "vue-i18n": "11.1.3", "vue-types": "6.0.0"}, "devDependencies": {"@vitejs/plugin-vue": "5.2.4", "vite-plugin-dts": "4.5.3", "vitest": "3.1.3"}}