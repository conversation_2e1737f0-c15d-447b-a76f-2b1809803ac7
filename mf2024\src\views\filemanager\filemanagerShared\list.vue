<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Tabs v-model:activeKey="activeKey" @change="handleSuccess">
          <Tabs.TabPane key="1">
            <template #tab>
              <Icon icon="i-ant-design:share-alt-outlined" />
              <span class="pr-1"> {{ t('分享给我') }} </span>
            </template>
          </Tabs.TabPane>
          <Tabs.TabPane key="2">
            <template #tab>
              <Icon icon="i-ant-design:user-switch-outlined" />
              <span class="pr-1"> {{ t('我分享的') }} </span>
            </template>
          </Tabs.TabPane>
        </Tabs>
      </template>
      <template #toolbar>
        <a-button
          type="default"
          @click="handleTagsForm({})"
          v-auth="'filemanager:filemanager:view'"
        >
          <Icon icon="i-ant-design:tags-outlined" /> {{ t('标签') }}
        </a-button>
        <ListSelect
          ref="listSelectRef"
          :configFile="import('../filemanagerTag/select')"
          :checkbox="false"
          @select="listSelectOnSelect"
          v-show="false"
        />
      </template>
      <template #firstColumn="{ record }">
        <div v-if="record.fileType == 'folder'">
          <Icon icon="i-fa:folder-o" /> &nbsp;
          <a @click="handleGoFolder({ id: record.id })">
            {{ record.fileName }}
          </a>
          <FileManagerTags :record="record" @click="handleTagClick" />
        </div>
        <div v-else>
          <Icon icon="i-fa:file-text-o" /> &nbsp;
          <a @click="handlePreview({ id: record.id })">
            {{ record.fileName }}
          </a>
          <FileManagerTags :record="record" @click="handleTagClick" />
        </div>
      </template>
    </BasicTable>
  </div>
</template>
<script lang="ts" setup name="ViewsFilemanagerFilemanagerSharedList">
  import { ref } from 'vue';
  import { Tabs } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { officeTreeData } from '/@/api/sys/office';
  import {
    filemanagerSharedDelete,
    filemanagerSharedListData,
    filemanagerSharedView,
  } from '/@/api/filemanager/filemanagerShared';
  import { FormProps } from '/@/components/Form';
  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';
  import { useGo } from '/@/hooks/web/usePage';
  import { openWindowLayer } from '/@/utils';
  import { ListSelect } from '/@/components/ListSelect';
  import FileManagerTags from '/@/views/filemanager/components/FileManagerTags.vue';
  import qs from 'qs';

  const { t } = useI18n('filemanager.filemanagerShared');
  const { showMessage } = useMessage();
  const { ctxAdminPath, filePreview } = useGlobSetting();
  const activeKey = ref<string>('1');
  const tagIdRef = ref<string>('');
  const go = useGo();

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('名称'),
        field: 'fileName',
        component: 'Input',
      },
      {
        label: t('分享人'),
        field: 'createBy',
        fieldLabel: 'createByName',
        component: 'TreeSelect',
        componentProps: {
          api: officeTreeData,
          params: { isLoadUser: true, userIdPrefix: '', isAll: true },
          canSelectParent: false,
          treeCheckable: true,
          allowClear: true,
        },
        ifShow: () => activeKey.value == '1',
      },
      {
        label: t('接受人'),
        field: 'receiveUserCode',
        fieldLabel: 'receiveUserName',
        component: 'TreeSelect',
        componentProps: {
          api: officeTreeData,
          params: { isLoadUser: true, userIdPrefix: '', isAll: true },
          canSelectParent: false,
          treeCheckable: true,
          allowClear: true,
        },
        ifShow: () => activeKey.value == '2',
      },
      {
        label: t('分享时间'),
        field: 'dateRange',
        component: 'RangePicker',
        componentProps: {},
      },
    ],
    fieldMapToTime: [['dateRange', ['createDate_gte', 'createDate_lte']]],
    showAdvancedButton: false,
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('名称'),
      dataIndex: 'fileName',
      key: 'a.file_name',
      sorter: true,
      width: 320,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('分享人'),
      dataIndex: 'createByName',
      key: 'a.create_by',
      sorter: true,
      width: 100,
      ifShow: () => activeKey.value == '1',
    },
    {
      title: t('接受人'),
      dataIndex: 'receiveUserName',
      key: 'a.receive_user_name',
      sorter: true,
      width: 100,
      ifShow: () => activeKey.value == '2',
    },
    {
      title: t('分享时间'),
      dataIndex: 'createDate',
      key: 'a.create_date',
      sorter: true,
      width: 100,
    },
    {
      title: t('备注信息'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 100,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 100,
    actions: (record: Recordable) => [
      {
        icon: 'i-ant-design:download-outlined',
        title: t('下载文件'),
        onClick: handleDownload.bind(this, { id: record.id }),
        auth: 'filemanager:filemanager:edit',
        ifShow: () => record.fileType !== 'folder',
      },
      {
        icon: 'i-ant-design:close-outlined',
        color: 'error',
        title: t('取消分享'),
        popConfirm: {
          title: t('是否确认取消分享'),
          confirm: handleDelete.bind(this, { id: record.id }),
        },
        auth: 'filemanager:filemanagerShared:edit',
      },
    ],
  };

  const [registerTable, { reload }] = useTable({
    api: filemanagerSharedListData,
    beforeFetch: (params) => {
      params.myShared = activeKey.value == '2';
      params.tagId = tagIdRef.value;
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  const listSelectRef = ref<any>(null);

  function handleTagsForm(_record: Recordable) {
    listSelectRef.value.openSelectModal();
  }

  function listSelectOnSelect(values: Recordable[]) {
    if (values && values.length > 0) {
      handleTagClick(values[0]);
    }
  }

  async function handleTagClick(tag: Recordable) {
    tagIdRef.value = tag.tagId;
    await reload();
    tagIdRef.value = '';
  }

  function handlePreview(record: Recordable) {
    openWindowLayer(
      ctxAdminPath + '/filemanager/download/' + record.id + '?preview=' + filePreview,
    );
  }

  async function handleDownload(record: Recordable) {
    downloadByUrl({ url: ctxAdminPath + '/filemanager/download/' + record.id });
  }

  async function handleGoFolder(record: Recordable) {
    const url = await filemanagerSharedView(record);
    const idx = url.indexOf('?');
    go({ path: url.substring(0, idx), query: qs.parse(url.substring(idx + 1)) });
  }

  async function handleDelete(record: Recordable) {
    const res = await filemanagerSharedDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleSuccess() {
    reload();
  }
</script>
