<!--
 * Copyright (c) 2113-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleExport({})" v-auth="'wms:pu:rd01:wmsRd01:view'">
          <Icon icon="i-ant-design:export-outlined" /> {{ t('延迟数据导出') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ djno: record.djno })">
          {{ record.djno }}
        </a>
      </template>
      <!-- 勾选 -->
      <template #checkBox="{ record, column }">
        <Checkbox :checked="record[column.dataIndex] == '1' ? true : false" />
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsWmsWhRd11WmsRd11List">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { wmsRd11Delete, wmsRd11ListData } from '/@/api/wms/wh/rd11/wmsRd11';
  import { basWarehousetreeData } from '/@/api/bas/house/basWarehouse';

  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './viewForm.vue';
  import { Checkbox } from 'ant-design-vue';
  import { downloadByUrl } from '/@/utils/file/download';
  import { useGlobSetting } from '/@/hooks/setting';

  const { t } = useI18n('wms.wh.rd11.wmsRd11');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('材料出库单管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('出库单号'),
        field: 'djno',
        component: 'Input',
      },
      {
        label: t('存货编码'),
        field: 'basInv.invCode',
        component: 'Input',
      },
      {
        label: t('存货名称'),
        field: 'basInv.invName',
        component: 'Input',
      },
      {
        label: t('规格型号'),
        field: 'basInv.invStd',
        component: 'Input',
      },
      {
        label: t('申请单号'),
        field: 'ccode',
        component: 'Input',
      },
      {
        label: t('入库日期'),
        field: 'ddate',
        component: 'RangePicker',
      },
      {
        label: t('仓库'),
        field: 'whCode',
        component: 'TreeSelect',
        componentProps: {
          api: basWarehousetreeData,
          params: { isLoadUser: true, userIdPrefix: '', isAll: true },
          canSelectParent: false,
          allowClear: true,
          // treeCheckable: true,
        },
      },
      {
        label: t('制单人'),
        field: 'createByName',
        component: 'Input',
      },
      {
        label: t(''),
        field: 'pushFlag',
        component: 'CheckboxGroup',
        componentProps: {
          options: [{ label: '是否推送U8', value: '1' }],
        },
      },
      {
        label: t(''),
        field: 'delayPushFlag',
        component: 'CheckboxGroup',
        componentProps: {
          options: [{ label: '是否延迟推送', value: '1' }],
        },
      },
      {
        label: t('状态'),
        field: 'status',
        component: 'TreeSelect',
        componentProps: {
          dictType: 'mf_sys_status',
        },
      },
    ],
    fieldMapToTime: [['ddate', ['ddate_gte', 'ddate_lte']]],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('是否推送U8'),
      dataIndex: 'pushFlag',
      key: 'a.push_flag',
      sorter: true,
      width: 100,
      align: 'center',
      slot: 'checkBox',
    },
    {
      title: t('是否延迟推送'),
      dataIndex: 'delayPushFlag',
      key: 'a.delay_push_flag',
      sorter: true,
      width: 100,
      align: 'center',
      slot: 'checkBox',
    },
    {
      title: t('计划推送时间'),
      dataIndex: 'planPushDate',
      key: 'a.plan_push_date',
      sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('出库单号'),
      dataIndex: 'djno',
      key: 'a.djno',
      sorter: true,
      width: 180,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('单据日期'),
      dataIndex: 'ddate',
      key: 'a.ddate',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('存货编码'),
      dataIndex: 'basInv.invCode',
      key: 'inv.inv_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('存货名称'),
      dataIndex: 'basInv.invName',
      key: 'inv.inv_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('规格型号'),
      dataIndex: 'basInv.invStd',
      key: 'inv.inv_std',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('数量'),
      dataIndex: 'iqty',
      key: 'a.iqty',
      sorter: true,
      width: 80,
      align: 'left',
    },
    {
      title: t('件数'),
      dataIndex: 'inum',
      key: 'a.inum',
      sorter: true,
      width: 80,
      align: 'left',
    },
    {
      title: t('单位'),
      dataIndex: 'basInv.unitName',
      key: 'inv.unit_name',
      sorter: true,
      width: 80,
      align: 'left',
    },
    {
      title: t('申请单号'),
      dataIndex: 'ccode',
      key: 'a.cccode',
      // sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: t('仓库编码'),
      dataIndex: 'whCode',
      key: 'a.wh_code',
      sorter: true,
      width: 80,
      align: 'center',
    },
    {
      title: t('仓库名称'),
      dataIndex: 'basWare.cwhname',
      key: 'a.wh_code',
      sorter: true,
      width: 100,
      align: 'center',
    },
    {
      title: t('u8单号'),
      dataIndex: 'u8Djno',
      key: 'a.u8_djno',
      sorter: true,
      width: 100,
      align: 'center',
    },
    {
      title: t('创建日期'),
      dataIndex: 'createDate',
      key: 'a.create_date',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('制单人'),
      dataIndex: 'createByName',
      key: 'a.create_by_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'a.status',
      dictType: 'mf_sys_status',
      sorter: true,
      width: 80,
      align: 'left',
      fixed: 'right',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 80,
    actions: (record: Recordable) => [
      // {
      //   icon: 'i-clarity:note-edit-line',
      //   title: t('编辑材料出库单'),
      //   onClick: handleForm.bind(this, { djno: record.djno }),
      //   auth: 'wmswh:rd11:wmsRd11:edit',
      // },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除材料出库单'),
        popConfirm: {
          title: t('是否确认删除材料出库单'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'wms:wh:rd11:wmsRd11:edit',
        ifShow: () => {
          return record.u8Djno === undefined && record.status === '0';
        },
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm }] = useTable({
    api: wmsRd11ListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { djno: record.djno };
    const res = await wmsRd11Delete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  // 延迟数据导出
  async function handleExport() {
    const { ctxAdminPath } = useGlobSetting();
    downloadByUrl({
      url: ctxAdminPath + '/wms/wh/rd11/wmsRd11/exportData',
      //params: getForm().getFieldsValue(),
    });
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
