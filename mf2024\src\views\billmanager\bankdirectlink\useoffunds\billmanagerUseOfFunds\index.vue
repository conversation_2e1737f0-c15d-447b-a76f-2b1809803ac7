<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <PageWrapper :sidebarWidth="230">
    <template #sidebar>
      <BasicTree
        :title="t('资金用途')"
        :search="true"
        :toolbar="true"
        :api="billmanagerUseOfFundsTreeData"
        :defaultExpandLevel="2"
        @select="handleSelect"
      />
    </template>
    <ListView :treeCode="treeCode" />
  </PageWrapper>
</template>
<script lang="ts" setup name="ViewsBankdirectlinkUseoffundsBillmanagerUseOfFundsIndex">
  import { ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { PageWrapper } from '/@/components/Page';
  import { BasicTree } from '/@/components/Tree';
  import { billmanagerUseOfFundsTreeData } from '/@/api/billmanager/bankdirectlink/useoffunds/billmanagerUseOfFunds';
  import ListView from './list.vue';

  const { t } = useI18n('bankdirectlink.useoffunds.billmanagerUseOfFunds');
  const treeCode = ref<string>('');

  function handleSelect(keys: string[]) {
    treeCode.value = keys[0];
  }
</script>
