import { useI18n } from '/@/hooks/web/useI18n';
import { BasicColumn, BasicTableProps, FormProps } from '/@/components/Table';
import { carVenTreeData, carVenListData } from '/@/api/wms/weighbridge/carVen';

const { t } = useI18n('sys.empUser');

const modalProps = {
  title: t('运输单位管理'),
  width: '50%',
};

const searchForm: FormProps = {
  baseColProps: { lg: 12, md: 12 },
  labelWidth: 90,
  schemas: [
    {
      label: t('运输管理'),
      field: 'parentCodes',
      component: 'TreeSelect',
      componentProps: {
        api: carVenTreeData,
        allowClear: true,
      },
      show: false,
    },
    {
      label: t('单位名称'),
      field: 'name',
      component: 'Input',
      align: 'letf',
    },
  ],
};

const tableColumns: BasicColumn[] = [
  {
    title: t('单位名称'),
    dataIndex: 'name',
    key: 'name',
    sorter: true,
    width: 100,
  },
  {
    title: t('单位编码'),
    dataIndex: 'code',
    key: 'code',
    sorter: true,
    width: 100,
  },
];

const tableProps: BasicTableProps = {
  api: carVenListData,
  beforeFetch: (params) => {
    params['isAll'] = true;

    return params;
  },
  columns: tableColumns,
  formConfig: searchForm,
  rowKey: 'code',
};

const treeProps: Recordable = {
  api: carVenTreeData,
  params: { isAll: true, isShowCode: '0', type: '1', isParent: '1' },
  title: t('运输单位'),
};

const treeTableFieldName = 'parentCodes';

export default {
  modalProps,
  tableProps,
  itemCode: 'code',
  itemName: 'name',
  isShowCode: true,
  treeProps,
  treeTableFieldName,
};
