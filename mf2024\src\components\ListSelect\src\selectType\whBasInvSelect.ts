import { useI18n } from '/@/hooks/web/useI18n';
import { BasicColumn, BasicTableProps, FormProps } from '/@/components/Table';
import { officeTreeData } from '/@/api/sys/office';
import { companyTreeData } from '/@/api/sys/company';
import { empUserListData } from '/@/api/sys/empUser';
import { basInvTreeData, basInvListData } from '/@/api/bas/inv/basInv';

const { t } = useI18n('sys.empUser');

const modalProps = {
  title: t('成品选择'),
};

const searchForm: FormProps = {
  baseColProps: { lg: 6, md: 8 },
  labelWidth: 100,
  schemas: [
    {
      label: t('存货分类'),
      field: 'basInvCls.code',
      component: 'Input',
      ifShow: false,
    },
    {
      label: t('存货编码'),
      field: 'invCode',
      component: 'Input',
    },
    {
      label: t('存货名称'),
      field: 'invName',
      component: 'Input',
    },
  ],
};

const tableColumns: BasicColumn[] = [
  {
    title: t('存货编码'),
    dataIndex: 'invCode',
    width: 100,
  },
  {
    title: t('存货名称'),
    dataIndex: 'invName',
    width: 100,
  },
  {
    title: t('规格型号'),
    dataIndex: 'cinvStd',
    width: 100,
  },
  {
    title: t('单位'),
    dataIndex: 'unitName',
    width: 130,
  },
];

const tableProps: BasicTableProps = {
  api: basInvListData,
  beforeFetch: (params) => {
    params['isAll'] = true;
    console.log('params', params);
    return params;
  },
  columns: tableColumns,
  formConfig: searchForm,
  rowKey: 'invCode',
};

const treeProps: Recordable = {
  api: basInvTreeData,
  params: { isAll: true, isShowCode: true, parentCodes: '0,5,' },
  title: t('存货'),
};

const treeTableFieldName = 'basInvCls.code';

export default {
  modalProps,
  tableProps,
  itemCode: 'invCode',
  itemName: 'invName',
  isShowCode: true,
  treeProps,
  treeTableFieldName,
};
