<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <!--<a-button type="primary" @click="handleForm({})" v-auth="'bas:house:basWarehouse:edit'">
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>-->
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ cwhcode: record.cwhcode })">
          {{ record.cwhcode }}
        </a>
      </template>
      <!-- 勾选 -->
      <template #checkBox="{ record, column }">
        <Checkbox :checked="record[column.dataIndex] == '1' ? true : false" />
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsBasHouseBasWarehouseList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { basWarehouseDelete, basWarehouseListData } from '/@/api/bas/house/basWarehouse';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import { Checkbox } from 'ant-design-vue';

  const { t } = useI18n('bas.house.basWarehouse');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: router.currentRoute.value.meta.title || t('仓库档案管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('仓库编码'),
        field: 'cwhcode',
        component: 'Input',
      },
      {
        label: t('仓库名称'),
        field: 'cwhname',
        component: 'Input',
      },
      {
        label: t('是否代管仓'),
        field: 'bproxywh',
        component: 'Select',
        componentProps: {
          dictType: 'sys_yes_no',
          allowClear: true,
        },
      },
      {
        label: t('是否货位管理'),
        field: 'bwhpos',
        component: 'Select',
        componentProps: {
          dictType: 'sys_yes_no',
          allowClear: true,
        },
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('仓库编码'),
      dataIndex: 'cwhcode',
      key: 'a.cwhcode',
      sorter: true,
      width: 110,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('仓库名称'),
      dataIndex: 'cwhname',
      key: 'a.cwhname',
      sorter: true,
      width: 230,
      align: 'left',
    },
    {
      title: t('仓库地址'),
      dataIndex: 'cwhaddress',
      key: 'a.cwhaddress',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('电话'),
      dataIndex: 'cwhphone',
      key: 'a.cwhphone',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('负责人'),
      dataIndex: 'cwhperson',
      key: 'a.cwhperson',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('是否代管仓'),
      dataIndex: 'bproxywh',
      key: 'a.bproxywh',
      sorter: true,
      width: 130,
      align: 'center',
      slot: 'checkBox',
    },
    {
      title: t('启用货位管理'),
      dataIndex: 'bwhpos',
      key: 'a.bwhpos',
      sorter: true,
      width: 130,
      align: 'center',
      slot: 'checkBox',
    },
    {
      title: t('多货位'),
      dataIndex: 'multiplepos',
      key: 'a.multiplepos',
      sorter: true,
      width: 130,
      align: 'center',
      slot: 'checkBox',
    },
    {
      title: t('默认货位'),
      dataIndex: 'basPosition.posName',
      key: 'pos.pos_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑仓库档案'),
        onClick: handleForm.bind(this, { cwhcode: record.cwhcode }),
        auth: 'bas:house:basWarehouse:edit',
      },
      // {
      //   icon: 'ant-design:delete-outlined',
      //   color: 'error',
      //   title: t('删除仓库档案'),
      //   popConfirm: {
      //     title: t('是否确认删除仓库档案'),
      //     confirm: handleDelete.bind(this, { whCode: record.whCode }),
      //   },
      //   auth: 'bas:house:basWarehouse:edit',
      // },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm }] = useTable({
    api: basWarehouseListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const res = await basWarehouseDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleSuccess() {
    reload();
  }
</script>
