<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
<div>
    <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    title="托盘打印"
    :showFooter="true"
    @ok="handleTrayPrintClick"
    width="50%">
    <!--  okText="打印托盘"  cancelText="全部关闭" -->
    <template #title>
      <span> 托盘打印 </span>
    </template>
    <BasicTable  @register="registerTable">
        <template #tableTitle>
            <div class="tableTitle">
                <span>条码尺寸</span><RadioGroup :options="optionsValues" :value="radioGroup" @click="handleRadioGroupClick" />
            </div>
      </template>
    </BasicTable>
  </BasicDrawer>
  
</div>
</template>
<script lang="ts" setup>
    import { ref, onMounted } from 'vue';
    import { useI18n } from '/@/hooks/web/useI18n';
    import { BasicModal, useModalInner  } from '/@/components/Modal';
    import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
    import { BasicTable, useTable } from '/@/components/Table';
    import {  RadioGroup } from '/@/components/Form';
    import { useMessage } from '/@/hooks/web/useMessage';
    import { printSnNumber } from '/@/api/wms/barcode/encode';
    import { useGlobSetting } from '/@/hooks/setting';
    import { dictDataListData } from '/@/api/sys/dictData';
    import { downloadByUrl } from '/@/utils/file/download';
    import { BarTypeEnum } from '/@/enums/defEnum';


  const { ctxPath  } = useGlobSetting();

    const optionsValues = [];
    const showPrintModal = ref(false);

    onMounted(async () => {
        const res = await dictDataListData({
            dictType: 'bar_size_type6',
        });
        res.forEach((item) => {
            optionsValues.push({
                label: item.dictLabelRaw,
                value: item.dictValue,
            });
        });
    });
    const { showMessage, showMessageModal,createSuccessModal } = useMessage();
    const emit = defineEmits(['success', 'register']);
    const { t } = useI18n('bas.inv.basInv');
    const radioGroup = ref<String>('6060');
    const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner((data: any) => {
        console.log(data, 'data===');
        traytable.setTableData([]);
        data.arr.forEach((element: any) => {
            console.log(element, 'element===');
            traytable.insertTableDataRecord({
                editable: true,
                djno: element.djno,
                cbatch: element.cbatch,
                id: '',
                minTpSeq: element.maxTpSeq + 1,
                maxTpSeq: '',
                packSize: element.packSize ? element.packSize  : 0
            });
        });
        
    });    
    const tableColumns: BasicColumn[] = [
    {
        title: t('单据编号'),
        dataIndex: 'djno',
        width: 120,
        align: 'left',
    },
    {
        title: t('批次'),
        dataIndex: 'cbatch',
        width: 120,
        align: 'left',
    },
    {
        title: t('托盘容量'),
        dataIndex: 'packSize',
        width: 50,
        align: 'left',
    },
    {
      title: t('开始序号'),
      dataIndex: 'minTpSeq',
      width: 120,
      align: 'center',
      editRow: true,
      editComponent: 'InputNumber',
      editRule: true,
    },
    {
      title: t('托盘数'),
      width: 120,
      dataIndex: 'maxTpSeq',
      editRow: true,
      editComponent: 'InputNumber',
      editRule: true,
    },
    ];
    const [registerTable, traytable] = useTable({
        columns: tableColumns,
        canResize: true,
        size: 'small',
        inset: true,
        pagination: false,
        bordered: true,
    });


    async function handleTrayPrintClick() {
    // 把得到的值传递给父组件
    try {
        const data = await traytable.getDataSource();
        console.log(data, 'data===');
        // 遍历查找data，如果maxTpSeq或者minTpSeq其中有一个值为空，则返回true，否则返回false
        const reqData = data.some((item) => !item.editValueRefs.maxTpSeq || !item.editValueRefs.minTpSeq);
        if (reqData) {
            showMessage('开始序号或托盘数不能为空！');
            return;
        }
        // 把data 里面的djno，minTpSeq， 取出来，用逗号隔开，然后传给后台
        const selIds = data.map((item) => item.djno).join(',');
        const minTpSeq = data.map((item) => item.editValueRefs.minTpSeq).join(',');
      const maxTpSeq = data
        .map((item) => {
          const min = parseFloat(item.editValueRefs.minTpSeq) || 0; // 转换为浮点数，如果转换失败则使用0
          const max = parseFloat(item.editValueRefs.maxTpSeq) || 0; // 同上
          console.log(min, max, 'min, max===', item.minTpSeq, item.maxTpSeq);
          return (min + max - 1).toString(); // 执行数值运算，然后转换为字符串
        })
        .join(','); // 将结果数组连接成逗号分隔的字符串
      const prtQtys = data
        .map((item) => {
          if (!item.packSize || item.packSize == '0') {
            return 1;
          } else {
            return item.packSize;
          }
        })
        .join(',');
        const params = {
            selIds : selIds,
            minTpSeq: minTpSeq,
            maxTpSeq: maxTpSeq,
            barType: BarTypeEnum.TuoPan,
            barSizeType: radioGroup.value,
            prtQtys: prtQtys
        };
        const res = await printSnNumber(params);
        if(res.result == "true"){
            console.log(res,'res===');
            createSuccessModal({ 
                content: '打印 ' + res.fileName,
                okText: '下载',
                onOk() {
                    downloadByUrl({ url: ctxPath + res.pdfUrl });
                    setTimeout(closeDrawer);
                },
            });
        } else {
            showMessage(res.message);
        }        
        } catch (error) {
            
        }
    };
    function handleRadioGroupClick(e: any = undefined) {
      const key = e?.target?.value || radioGroup.value;
      radioGroup.value = key;
    }

</script>
<style scoped>
.tableTitle {
    margin-left: 10px;
    font-size: 18px;
    span{
        margin-right: 10px;
    }
 }
</style>
  