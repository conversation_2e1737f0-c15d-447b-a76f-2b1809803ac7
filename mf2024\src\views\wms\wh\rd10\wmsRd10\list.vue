<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      
      <template #toolbar>
        <Popconfirm :title="t('确认关闭入库通知吗?')" @confirm="btnclose()">
          <a-button primary  v-auth="'wms.wh:rd10:wmsRd10:close'">
              <Icon icon="i-ant-design:close-outlined" /> {{ t('关闭单据') }}
          </a-button>
        </Popconfirm>
        <div>
          <Popconfirm :title="t('确认批量提交吗?')" @confirm="handlebtnpltj()">
            <a-button
              ghost
              type="warning"
              v-auth="'wms.wh:rd10:wmsRd10:edit'"
              class="ml-2 mr-2"
            >
              <Icon icon="i-ant-design:check-outlined" /> {{ t('批量提交') }}
            </a-button>
          </Popconfirm>
        </div>
        <a-button primary @click="handlePrintTray({})" v-auth="'wms.wh:rd10:wmsRd10:view'">
          <Icon icon="i-ant-design:printer-outlined" /> {{ t('托盘码') }}
        </a-button>
        <a-button primary @click="handleImportPrint({})" v-auth="'wms.wh:rd10:wmsRd10:view'">
          <Icon icon="i-ant-design:printer-outlined" /> {{ t('补打(托盘)') }}
        </a-button>
        <a-button primary @click="handleInvPrint({})" v-auth="'wms.wh:rd10:wmsRd10:view'">
          <Icon icon="i-ant-design:printer-outlined" /> {{ t('产品标签') }}
        </a-button>
        <!-- <a-button primary @click="handleForm({})" v-auth="'wms.wh:rd10:wmsRd10:view'">
          <Icon icon="i-ant-design:printer-outlined" /> {{ t('补打(产品)') }}
        </a-button> -->
        <a-button primary @click="handleForm({})" v-auth="'wms.wh:rd10:wmsRd10:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ djno : record.djno })">
          {{ record.djno }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
    <trayPrint @register="registerPrintDrawer" @success="handleSuccess" />
    <BpmRuntimeTrace @register="registerTraceModal" />
    <FormImport @register="registerImportModal" @success="handleSuccessFormImport" />
    <ListRds10 @register="registerRds10Modal" @success="handleSuccess" />
    <barSizeTypeForm @register="registerBarSizeTypeModal" />
  </div>
</template>
<script lang="ts" setup name="ViewsWmsWhRd10WmsRd10List">
  import { unref,ref  } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { wmsRd10Delete, wmsRd10ListData, wmsBatchNotify, wmsRd10Close } from '/@/api/wms/wh/rd10/wmsRd10';
  import { useDrawer } from '/@/components/Drawer';
  import { useModal } from '/@/components/Modal';
  import { BpmRuntimeTrace } from '/@/components/Bpm';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import trayPrint from './trayPrint.vue';
  import FormImport from './formImport.vue';
  import ListRds10 from './listRds10.vue';
  import { downloadByUrl } from '/@/utils/file/download';
  import { useGlobSetting } from '/@/hooks/setting';
  import { printSnNumber } from '/@/api/wms/barcode/encode';
  import barSizeTypeForm from '/@/views/wms/barcode/print/barSizeTypeForm.vue';
  import { BarTypeEnum } from '/@/enums/defEnum';
  import { Popconfirm } from 'ant-design-vue';

  const { t } = useI18n('wms/wh.rd10.wmsRd10');
  const { showMessage, createSuccessModal } = useMessage();
  const { meta } = unref(router.currentRoute);
  const { ctxPath } = useGlobSetting();
  const confirmStatus = ref(false);
  

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('入库通知管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('单据日期'),
        field: 'ddate',
        component: 'RangePicker',
        componentProps: {
          // format: 'YYYY-MM-DD',
          // showTime: false,
        },
      },
      // {
      //   label: t('单据日期止'),
      //   field: 'ddate_lte',
      //   component: 'DatePicker',
      //   componentProps: {
      //     format: 'YYYY-MM-DD',
      //     showTime: false,
      //   },
      // },
      {
        label: t('单据状态'),
        field: 'djStatus',
        component: 'Select',
        componentProps: {
          dictType: 'wms_rd10_status',
          allowClear: true,
        },
      },
      {
        label: t('单据号'),
        field: 'djno',
        component: 'Input',
      },
      {
        label: t('批号'),
        field: 'addCode',
        component: 'Input',
      },
      {
        label: t('存货名称'),
        field: 'basInv.invName',
        component: 'Input',
      },
      {
        label: t('规格型号'),
        field: 'basInv.invStd',
        component: 'Input',
      },
      {
        label: t('存货编码'),
        field: 'invCode',
        component: 'Input',
      },
      {
        label: t('检验员'),
        field: 'checkBy',
        component: 'Select',
        componentProps: {
          dictType: 'mf_check_person',
          allowClear: true,
        },
        ifShow: false,
      },
      {
        label: t('班组'),
        field: 'cteam',
        component: 'Select',
        componentProps: {
          dictType: 'mf_team',
          allowClear: true,
        },
        ifShow: false,
      },
      {
        label: t('批次'),
        field: 'cbatch',
        component: 'Input',
      },
      {
        label: t('状态'),
        field: 'status',
        component: 'Select',
        componentProps: {
          dictType: 'bpm_biz_status',
          allowClear: true,
          onChange: handleSuccess,
        },
      },
      {
        label: t('制单人'),
        field: 'createByName',
        component: 'Input',
        ifShow: true,
      },
      {
        label: t(''),
        field: 'showNValid',
        component: 'CheckboxGroup',
        componentProps: {
          options: [{ label: '显示作废记录', value: '1' }],
        },
      },
    ],
    fieldMapToTime: [['ddate', ['ddate_gte', 'ddate_lte']]],
  };

  const tableColumns: BasicColumn[] = [
    { 
      title: t('单据状态'),
      dataIndex: 'djStatus',
      key: 'a.dj_status',
      sorter: true,
      width: 80,
      align: 'center',
      dictType: 'wms_rd10_status',
      fixed: 'left',
    },
    {
      title: t('单据编号'),
      dataIndex: 'djno',
      key: 'a.djno',
      sorter: true,
      width: 150,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('单据日期'),
      dataIndex: 'ddate',
      key: 'a.ddate',
      sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('批次'),
      dataIndex: 'cbatch',
      key: 'a.cbatch',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('部门'),
      dataIndex: 'office.officeName',
      // key: 'a.wh_name',
      // sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('批号'),
      dataIndex: 'addCode',
      key: 'a.add_code',
      sorter: true,
      width: 80,
      align: 'center',
    },
    {
      title: t('存货编码'),
      dataIndex: 'invCode',
      key: 'a.inv_code',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('存货名称'),
      dataIndex: 'basInv.invName',
      // key: 'a.inv_code',
      // sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('规格型号'),
      dataIndex: 'basInv.invStd',
      width: 130,
      align: 'center',
    },
    {
      title: t('单位'),
      dataIndex: 'basInv.unitName',
      width: 40,
      align: 'center',
    },
    {
      title: t('检验员'),
      dataIndex: 'checkBy',
      key: 'a.check_by',
      sorter: true,
      width: 80,
      align: 'center',
      dictType: 'mf_check_person',
    },
    {
      title: t('班次'),
      dataIndex: 'teamClass',
      key: 'a.team_class',
      sorter: true,
      width: 80,
      align: 'center',
      dictType: 'mf_team_class',
    },
    {
      title: t('班组'),
      dataIndex: 'cteam',
      key: 'a.cteam',
      sorter: true,
      width: 80,
      align: 'center',
      dictType: 'mf_team',
    },
    {
      title: t('品级'),
      dataIndex: 'cgrade',
      key: 'a.cgrade',
      sorter: true,
      width: 80,
      align: 'center',
      dictType: 'mf_grade',
    },
    {
      title: t('数量'),
      dataIndex: 'iqty',
      key: 'a.iqty',
      sorter: true,
      width: 80,
      align: 'left',
    },
    {
      title: t('件数'),
      dataIndex: 'inum',
      key: 'a.inum',
      sorter: true,
      width: 60,
      align: 'left',
    },

    {
      title: t('含量'),
      dataIndex: 'ichangeRate',
      key: 'a.ichange_rate',
      sorter: true,
      width: 80,
      align: 'left',
    },
    {
      title: t('累计入库数量'),
      dataIndex: 'sumQty',
      key: 'a.sum_qty',
      sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('累计入库件数'),
      dataIndex: 'sumNum',
      key: 'a.sum_num',
      sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('最大托盘序号'),
      dataIndex: 'maxTpSeq',
      key: 'a.max_tp_seq',
      sorter: true,
      width: 130,
      align: 'center',
      ifShow: false,
    },
    {
      title: t('update_date'),
      dataIndex: 'updateDate',
      key: 'a.update_date',
      sorter: true,
      width: 130,
      align: 'center',
      ifShow: false,
    },
    {
      title: t('remarks'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 130,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('制单人'),
      dataIndex: 'createByName',
      key: 'a.create_by_name',
      sorter: true,
      width: 80,
      align: 'left',
    },
    {
      title: t('修改人'),
      dataIndex: 'updateByName',
      key: 'a.update_by_name',
      sorter: true,
      width: 130,
      align: 'left',
      ifShow: false,
    },
    {
      title: t('托盘容量'),
      dataIndex: 'packSize',
      key: 'a.pack_size',
      sorter: true,
      width: 80,
      align: 'center',
    },
    {
      title: t('单件容量'),
      dataIndex: 'pieceQty',
      key: 'a.piece_qty',
      sorter: true,
      width: 80,
      align: 'left',
    },
    {
      title: t('仓库名称'),
      dataIndex: 'basWare.cwhname',
      // key: 'a.wh_name',
      // sorter: true,
      width: 80,
      align: 'center',
    },
    {
      title: t('审批状态'),
      dataIndex: 'status',
      key: 'a.status',
      sorter: true,
      width: 100,
      align: 'center',
      dictType: 'bpm_biz_status',
      fixed: 'right',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑入库通知'),
        onClick: handleForm.bind(this, { djno: record.djno }),
        auth: 'wms.wh:rd10:wmsRd10:edit',
        ifShow: () => record.djStatus == '1',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除入库通知'),
        popConfirm: {
          title: t('是否确认删除入库通知'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'wms.wh:rd10:wmsRd10:edit',
        ifShow: () => record.status == '9',
      },
      {
        icon: 'i-fluent:flowchart-20-regular',
        title: t('流程追踪'),
        onClick: handleTrace.bind(this, record),
        ifShow: () => record.status != '9',
      },
      {
        label: t('入库明细'),
        onClick: handleListDetail.bind(this, record),
        ifShow: () => record.djStatus == '4' || record.djStatus == '9',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerPrintDrawer, { openDrawer: openPrintDrawer }] = useDrawer();
  const [registerRds10Modal, { openDrawer: openRd10Modal }] = useDrawer();
  const [registerTable, { reload, getSelectRows, getForm }] = useTable({
    api: wmsRd10ListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
    rowSelection: { type: 'checkbox' },
    
  });

  const [registerImportModal, { openModal: importModal, closeModal }] = useModal();
  const [registerBarSizeTypeModal, { openModal: openBarSizeTypeModal }] = useModal();

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { djno: record.djno };
    const res = await wmsRd10Delete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }

  const [registerTraceModal, { openModal: traceModel }] = useModal();

  function handleTrace(record: Recordable) {
    traceModel(true, { formKey: 'mf_rd10_nottify', bizKey: record.id });
  }

  async function btnpltj() {
    if (getSelectRows().length) {
      var selIds = ref('');
      getSelectRows().forEach((item) => {
        selIds.value += item.id + ',';
      });
      const res = await wmsBatchNotify({ selIds: selIds.value});
      showMessage(res.message);
      handleSuccess();
    } else {
      showMessage('请先选择一行数据');
    }
  }

  async function btnclose() {
    if (getSelectRows().length) {
      var selIds = ref('');
      getSelectRows().forEach((item) => {
        selIds.value += item.id + ',';
      });
      const res = await wmsRd10Close({ selIds: selIds.value});
      showMessage(res.message);
      handleSuccess();
    } else {
      showMessage('请先选择一行数据');
    }
  }

  async function handlebtnpltj() {
    if (getSelectRows().length) {
      btnpltj();
    } else {
      showMessage('请先选择一行数据');
     }
  }

  async function handlePrintTray() {
    let arr = await getSelectRows();
    if (getSelectRows().length == 0) {
      showMessage(t('请先选择一行数据'));
      return;
    }
    openPrintDrawer(true, { arr: arr });
  }
  function handleImportPrint() {
    importModal(true, {});
  }
  async function handleSuccessFormImport(data) {
    console.log(data);
    if(data.result  == 'true'){
      createSuccessModal({ 
        content: '打印 ' + data.fileName,
        okText: '下载',
        onOk() {
            downloadByUrl({ url: ctxPath + data.pdfUrl });
            closeModal();
        },
        });
    } else {
      showMessage(data.message);
    }
  }

  async function handleListDetail(record: Recordable) {
    openRd10Modal(true, record);
    // router.push({
    //   path: '/wms/wh/rd10/wmsRds10/list',
    //   query: {
    //     id: record.id,
    //     code: record.djno,
    //     tabTitle: `入库明细(${record.djno})`,
    //   },
    // });
  }

  async function handleInvPrint() {
    let arr = await getSelectRows();
    if (getSelectRows().length == 0) {
      showMessage(t('请先选择一行数据'));
      return;
    }else if (getSelectRows().length > 1) {
      showMessage(t('只能选择一行数据'));
      return;
    }
    openBarSizeTypeModal(true, { arr: arr, barSizeType: BarTypeEnum.MoNotify, bizKey: 'bizKey' });
  }
</script>
<style lang="less" scoped>
</style>
