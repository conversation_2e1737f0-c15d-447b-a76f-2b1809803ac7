<!--
 * 销售发货计划货位明细查看.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :showOkBtn="false"
    @register="registerDrawer"
    width="50%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm">
      <template #mfCarplanFhBList>
        <BasicTable @register="registerChildTable" />
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsWmsWeighbridgeFhInvPosForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { mfCarplanFhCformView } from '/@/api/wms/weighbridge/fh';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('test.testData');
  const { meta } = unref(router.currentRoute);
  const record = ref<TestData>({} as TestData);

  const getTitle = computed(() => ({
    icon: meta.icon || 'ant-design:book-outlined',
    value: t('货位信息查看'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t(''),
      field: 'mfCarplanFhBList',
      component: 'Input',
      colProps: { lg: 24, md: 24 },
      slot: 'mfCarplanFhBList',
    },
  ];

  const [registerForm, { resetFields, setFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    autoSubmitOnEnter: true,
    baseColProps: { lg: 24, md: 24 },
  });

  const [registerChildTable, childTable] = useTable({
    rowKey: 'id',
    pagination: false,
    bordered: true,
    size: 'small',
    inset: true,
  });

  async function setChildTableData(_res: Recordable) {
    childTable.setColumns([
      {
        title: t('确认入库重量'),
        dataIndex: 'qrWeight',
        width: 80,
        align: 'center',
      },
      {
        title: t('发货批次'),
        dataIndex: 'ibatch',
        width: 150,
        align: 'left',
      },
      {
        title: t('发货货位'),
        dataIndex: 'cposition',
        width: 150,
        align: 'left',
      },
      {
        title: t('件数'),
        dataIndex: 'qty',
        width: 80,
        align: 'center',
      },
    ]);
    childTable.setTableData(record.value.mfCarplanFhBList || []);
  }


  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await mfCarplanFhCformView(data);
    record.value = res.mfCarplanFhC || {};
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setChildTableData(res);
    setDrawerProps({ loading: false });
  });
</script>
