/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface BillManagerPayApplyC2 extends BasicModel<BillManagerPayApplyC2> {
  parentId?: string; // C1明细ID
  parentDid?: string; // 明细ID
  clink?: string; // 票据Id
  orgAmount?: number; // 原币金额
  u8Id?: string; // U8单据ID
}

export const billManagerPayApplyC2List = (params?: BillManagerPayApplyC2 | any) =>
  defHttp.get<BillManagerPayApplyC2>({ url: adminPath + '/bankdirectlink/payapply/billManagerPayApplyC2/list', params });

export const billManagerPayApplyC2ListData = (params?: BillManagerPayApplyC2 | any) =>
  defHttp.post<Page<BillManagerPayApplyC2>>({ url: adminPath + '/bankdirectlink/payapply/billManagerPayApplyC2/listData', params });

export const billManagerPayApplyC2Form = (params?: BillManagerPayApplyC2 | any) =>
  defHttp.get<BillManagerPayApplyC2>({ url: adminPath + '/bankdirectlink/payapply/billManagerPayApplyC2/form', params });

export const billManagerPayApplyC2Save = (params?: any, data?: BillManagerPayApplyC2 | any) =>
  defHttp.postJson<BillManagerPayApplyC2>({ url: adminPath + '/bankdirectlink/payapply/billManagerPayApplyC2/save', params, data });

export const billManagerPayApplyC2Delete = (params?: BillManagerPayApplyC2 | any) =>
  defHttp.get<BillManagerPayApplyC2>({ url: adminPath + '/bankdirectlink/payapply/billManagerPayApplyC2/delete', params });
