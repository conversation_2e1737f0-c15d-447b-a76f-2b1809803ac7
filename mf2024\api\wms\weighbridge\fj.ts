/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

import { UploadApiResult } from './upload';
import { UploadFileParams } from '/#/axios';
import { AxiosProgressEvent } from 'axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface CarplanFj extends BasicModel<CarplanFj> {}

export const mfCarplanFjHListData = (params?: CarplanFj | any) =>
  defHttp.post<Page<CarplanFj>>({ url: adminPath + '/mf/fj/mfCarplanFjH/listData', params });
export const mfCarplanFjCListData = (params?: CarplanFj | any) =>
  defHttp.post<Page<CarplanFj>>({
    url: adminPath + '/mf/fj/mfCarplanFjH/mfCarplanFjCListData',
    params,
  });
export const mfCarplanFjHinvalid = (params?: CarplanFj | any) =>
  defHttp.post<CarplanFj>({ url: adminPath + '/mf/fj/mfCarplanFjH/invalid', params });

export const mfCarplanFjHDelete = (params?: CarplanFj | any) =>
  defHttp.get<CarplanFj>({ url: adminPath + '/mf/fj/mfCarplanFjH/delete', params });

export const mfCarplanFjHDeleteC = (params?: CarplanFj | any) =>
  defHttp.get<CarplanFj>({ url: adminPath + '/mf/fj/mfCarplanFjH/deleteC', params });

export const mfCarplanFjHupdateCstatus = (params?: CarplanFj | any) =>
  defHttp.get<CarplanFj>({ url: adminPath + '/mf/fj/mfCarplanFjH/updateCstatus', params });

export const mfCarplanFjCDelete = (params?: CarplanFj | any) =>
  defHttp.get<CarplanFj>({ url: adminPath + '/mf/fj/mfCarplanFjH/delete', params });

export const updateCzStatusByHand = (params?: CarplanFj | any) =>
  defHttp.post<CarplanFj>({ url: adminPath + '/mf/fj/mfCarplanFjH/updateCzStatusByHand', params });

export const mfCarplanFjHimportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: AxiosProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/mf/fj/mfCarplanFjH/importData',
      onUploadProgress,
    },
    params,
  );

export const mfCarplanFjHForm = (params?: CarplanFj | any) =>
  defHttp.get<CarplanFj>({ url: adminPath + '/mf/fj/mfCarplanFjH/form', params });

export const mfCarplanFjHSave = (params?: any, data?: CarplanFj | any) =>
  defHttp.postJson<CarplanFj>({ url: adminPath + '/mf/fj/mfCarplanFjH/save', params, data });


export const mfCarplanFjForm = (params?: CarplanFj | any) =>
  defHttp.get<CarplanFj>({ url: adminPath + '/mf/fj/mfCarplanFjH/formC', params });

export const mfCarplanFjSave = (params?: any, data?: CarplanFj | any) =>
  defHttp.postJson<CarplanFj>({ url: adminPath + '/mf/fj/mfCarplanFjH/saveC', params, data });

export const mfCarplanFjCsaveChild = (params?: CarplanFj | any) =>
  defHttp.postJson<CarplanFj>({ url: adminPath + '/mf/fj/mfCarplanFjH/saveChild', params });

export const mfCarplanFjHformEdit = (params?: CarplanFj | any) =>
  defHttp.post<Page<CarplanFj>>({ url: adminPath + '/mf/fj/mfCarplanFjH/formEdit', params });


export const mfCarplanFjHListForm = (params?: CarplanFj | any) =>
  defHttp.get<CarplanFj>({ url: adminPath + '/mf/fj/mfCarplanFjH/listForm', params });

export const mfCarplanFjHListSave = (params?: any, data?: CarplanFj | any) =>
  defHttp.postJson<CarplanFj>({ url: adminPath + '/mf/fj/mfCarplanFjH/listSave', params, data });
