<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
        <span> （{{ parameter.vouchCode + '-' + parameter.vouchName }}） </span>
      </template>
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleForm({})"
          v-auth="'layout:edit'"
        >
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ code: record.code })">
          {{ record.code }}
        </a>
      </template>
      <!-- <template #viewCodeColumn="{ record }">
        <a @click="handleViewCodeData({ viewCode: record.viewCode })">
          {{ record.viewCode }}
        </a>
      </template> -->
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
    <FanDrawer @register="registerDrawer2" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherViewListQueryList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import {
    layVoucherViewListQueryDelete,
    layVoucherViewListQueryListData,
  } from '/@/api/layout/list/layVoucherViewListQuery';
  import {
    layVoucherViewListQueryDisable,
    layVoucherViewListQueryEnable,
  } from '/@/api/layout/list/layVoucherViewListQuery';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import FanDrawer from './fanDrawer.vue';

  const { t } = useI18n('layout.layVoucherViewListQuery');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: router.currentRoute.value.meta.title || t('查询方案管理'),
  };

  let parameter = ref(router.currentRoute.value.query);

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('查询标志'),
        field: 'code',
        component: 'Input',
      },
      {
        label: t('查询名称'),
        field: 'name',
        component: 'Input',
      },
      {
        label: t('布局标志'),
        field: 'viewCode',
        component: 'Input',
      },
      {
        label: t('状态'),
        field: 'status',
        component: 'Select',
        componentProps: {
          dictType: 'sys_status',
          allowClear: true,
          onChange: handleSuccess,
        },
      },
      {
        label: t('备注信息'),
        field: 'remarks',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('查询标志'),
      dataIndex: 'code',
      key: 'a.code',
      sorter: true,
      width: 230,
      align: 'left',
      slot: 'firstColumn',
      // editRow: true,
      // editComponent: 'Input',
      // editRule: true,
    },
    {
      title: t('查询名称'),
      dataIndex: 'name',
      key: 'a.name',
      sorter: true,
      width: 230,
      align: 'left',
      // slot: 'firstColumn',
      // editRow: true,
      // editComponent: 'Input',
      // editRule: true,
    },
    {
      title: t('布局标志'),
      dataIndex: 'viewCode',
      key: 'a.view_code',
      sorter: true,
      width: 130,
      align: 'left',
      // slot: 'viewCodeColumn',
      // editRow: true,
      // editComponent: 'Input',
      // editRule: true,
    },
    {
      title: t('是否默认'),
      dataIndex: 'bdefualt',
      key: 'a.bdefualt',
      sorter: true,
      width: 130,
      align: 'left',
      dictType: 'sys_yes_no',
      // slot: 'viewCodeColumn',
      // editRow: true,
      // editComponent: 'Input',
      // editRule: true,
    },
    {
      title: t('基础单据'),
      dataIndex: 'voucherCls.typeName',
      dataLabel: 'voucherCls.typeName',
      key: 'a.vouch_code',
      sorter: true,
      width: 130,
      align: 'left',
      // editRow: true,
      // editComponent: 'Input',
      // editRule: true,
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      // {
      //   icon: 'ant-design:save-outlined',
      //   title: t('保存'),
      //   onClick: handleSave.bind(this, { code: record.code }),
      //   auth: 'layout:edit',
      // },
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑页面视图'),
        onClick: handleForm.bind(this, { code: record.code }),
        auth: 'layout:edit',
      },
      // {
      //   icon: 'fa fa-search',
      //   color: 'error',
      //   title: t('查询栏目设计'),
      //   onClick: handleQuery.bind(this, record),
      //   auth: 'layout:edit',
      // },
      {
        icon: 'ant-design:stop-outlined',
        color: 'error',
        title: t('停用查询方案'),
        popConfirm: {
          title: t('是否确认停用查询方案'),
          confirm: handleDisable.bind(this, { code: record.code }),
        },
        auth: 'layout:edit',
        ifShow: () => record.status === '0',
      },
      {
        icon: 'ant-design:check-circle-outlined',
        color: 'success',
        title: t('启用查询方案'),
        popConfirm: {
          title: t('是否确认启用查询方案'),
          confirm: handleEnable.bind(this, { code: record.code }),
        },
        auth: 'layout:edit',
        ifShow: () => record.status === '2',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除查询方案'),
        popConfirm: {
          title: t('是否确认删除查询方案'),
          confirm: handleDelete.bind(this, { code: record.code }),
        },
        auth: 'layout:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerDrawer2, { openDrawer:openDrawer2 }] = useDrawer();
  const [registerTable, { reload, insertTableDataRecord, getColumns }] = useTable({
    api: layVoucherViewListQueryListData,
    beforeFetch: (params) => {
      console.log(parameter.value)
      params.viewCode = parameter.value.code;
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true, // 搜索表单
    canResize: true,
  });

  function handleForm(record: Recordable) {
    console.log(parameter.value);
    record.viewCode = parameter.value.code;
    record.vouchCode = parameter.value.vouchCode;
    record.vouchName = parameter.value.vouchName;
    openDrawer(true, record);
  }

  function handleQuery(record: Recordable) {
    openDrawer2(true, record);
    // router.push({
    //   path: '/layout/layVoucherViewListQueryCol/list',
    //   query: {
    //     // viewCode: record.viewCode,
    //     ...record,
    //   },
    // });
  }


  async function handleDisable(record: Recordable) {
    const res = await layVoucherViewListQueryDisable(record);
    showMessage(res.message);
    handleSuccess();
  }

  async function handleEnable(record: Recordable) {
    const res = await layVoucherViewListQueryEnable(record);
    showMessage(res.message);
    handleSuccess();
  }

  async function handleDelete(record: Recordable) {
    const res = await layVoucherViewListQueryDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  // function handleViewCodeData(record: Recordable) {
  //   router.push({
  //     path: '/layout/layVoucherViewListQueryCol/list',
  //     query: {
  //       viewCode: record.viewCode,
  //     },
  //   });
  // }


  function handleSuccess() {
    reload();
  }

  // 表格新增一行
  // function handleRow() {
  //   insertTableDataRecord({
  //     editable: true,
  //   });
  // }

  // // 保存表格修改
  // function handleSave() {
  //   let data = getColumns();
  //   console.log(data);
  // }
</script>
