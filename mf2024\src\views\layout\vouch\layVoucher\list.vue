<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'layout:edit'">
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ code: record.code })">
          {{ record.code }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
    <FieldTabs @register="registerDrawer2" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutVouchLayVoucherList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, watch, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { layVoucherDelete, layVoucherListData } from '../../../../api/layout/vouch/layVoucher';
  import { useDrawer } from '/@/components/Drawer';
  // import { BasicModal, useModal } from '/@/components/Modal';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import FieldTabs from './fieldTabs.vue';
  import { layVoucherClsTreeData } from '/@/api/layout/vouch/layVoucherCls';
  // import VoucherFieldsList from '/@/views/layout/voucherFields/list.vue';
  // import LayVoucherViewListQuery from '/@/views/layout/list/layVoucherViewListQuery/list.vue';
  // import LayVoucherViewListQueryCol from '/@/views/layout/layVoucherViewListQueryCol/list.vue';
  // import { Tabs } from 'ant-design-vue';

  // // 多页签切换
  // const activeKey = ref<string>('1');
  // const activeKey1 = ref<string>('1');
  // const activeKey2 = ref<string>('1');

  const props = defineProps({
    treeCode: String,
    treeName: String,
  });

  const { t } = useI18n('layout.layVoucher');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: router.currentRoute.value.meta.title || t('基础单据管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('单据类别'),
        field: 'voucherCls.typeCode',
        fieldLabel: 'voucherCls.typeName',
        component: 'TreeSelect',
        componentProps: {
          dictType: '',
          allowClear: true,
          api: layVoucherClsTreeData,
        },
      },
      {
        label: t('单据名称'),
        field: 'name',
        component: 'Input',
      },
      {
        label: t('实体类路径'),
        field: 'entityPath',
        component: 'Input',
      },
      {
        label: t('单据描述'),
        field: 'remarks',
        component: 'Input',
      },
      {
        label: t('起始前缀类型'),
        field: 'stPrefixType',
        component: 'Select',
        componentProps: {
          dictType: 'st_prefix_type',
          allowClear: true,
        },
      },
      {
        label: t('中间前缀'),
        field: 'mdPrefix',
        component: 'Input',
      },
      {
        label: t('启用日期'),
        field: 'buseDate',
        component: 'RadioGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('日期格式'),
        field: 'dateFmt',
        component: 'Input',
      },
      {
        label: t('流水位数'),
        field: 'inum',
        component: 'Input',
      },
      {
        label: t('状态'),
        field: 'status',
        component: 'Select',
        componentProps: {
          dictType: 'sys_search_status',
          allowClear: true,
          onChange: handleSuccess,
        },
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('单据标志'),
      dataIndex: 'code',
      key: 'a.code',
      sorter: true,
      width: 130,
      align: 'left',
      dictType: '',
      slot: 'firstColumn',
    },
    {
      title: t('单据名称'),
      dataIndex: 'name',
      key: 'a.name',
      sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: t('实体类路径'),
      dataIndex: 'entityPath',
      key: 'a.entity_path',
      sorter: true,
      width: 280,
      align: 'left',
    },
    {
      title: t('单据描述'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('起始前缀类型'),
      dataIndex: 'stPrefixType',
      key: 'a.st_prefix_type',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'st_prefix_type',
    },
    {
      title: t('中间前缀'),
      dataIndex: 'mdPrefix',
      key: 'a.md_prefix',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('启用日期'),
      dataIndex: 'buseDate',
      key: 'a.buse_date',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('日期格式'),
      dataIndex: 'dateFmt',
      key: 'a.date_fmt',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('流水位数'),
      dataIndex: 'inum',
      key: 'a.inum',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('数据库表名'),
      dataIndex: 'tableName',
      key: 'a.table_name',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'a.status',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_search_status',
    },
    {
      title: t('更新时间'),
      dataIndex: 'updateDate',
      key: 'a.update_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 260,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑基础单据'),
        onClick: handleForm.bind(this, { code: record.code }),
        auth: 'layout:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除基础单据'),
        popConfirm: {
          title: t('是否确认删除基础单据'),
          confirm: handleDelete.bind(this, { code: record.code }),
        },
        auth: 'layout:edit',
      },
      {
        icon: 'ant-design:zoom-in-outlined',
        color: 'error',
        title: t('页面视图'),
        onClick: handleGoPage.bind(this, {
          code: record.code,
          name: record.name,
          typeCode: record.typeCode,
        }),
        auth: 'layout:edit',
      },
      {
        icon: 'fa fa-code',
        color: 'error',
        title: t('单据字段设计'),
        onClick: handleVouchDesign.bind(this, {
          code: record.code,
          name: record.name,
          typeCode: record.typeCode,
        }),
        auth: 'layout:edit',
      },
      // {
      //   icon: 'fa fa-search',
      //   color: 'error',
      //   title: t('查询方案'),
      //   onClick: handleQuery.bind(this, { code: record.code, name: record.name }),
      //   auth: 'layout:edit',
      // },
      // {
      //   icon: 'fa fa-table',
      //   color: 'error',
      //   title: t('表格配置'),
      //   onClick: handleTable.bind(this, { code: record.code, name: record.name }),
      //   auth: 'layout:edit',
      // },
    ],
  };

  // const [register, { openModal }] = useModal();
  // const [register1, { openModal: openModal1 }] = useModal();
  // const [register2, { openModal: openModal2 }] = useModal();

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerDrawer2, { openDrawer: openDrawer2 }] = useDrawer();
  const [registerTable, { reload, getForm, getDataSource }] = useTable({
    api: layVoucherListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  watch(
    () => props.treeCode,
    async () => {
      await getForm().setFieldsValue({
        'voucherCls.typeCode': props.treeCode,
        'voucherCls.typeName': props.treeName,
      });
      reload();
    },
  );

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  function handleVouchDesign(record: Recordable) {
    console.log(record);
    record.typeName = record.name;
    openDrawer2(true, record);
  }

  async function handleDelete(record: Recordable) {
    const res = await layVoucherDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleSuccess() {
    reload();
  }

  function handleGoPage(record: Recordable) {
    router.push({
      path: '/layout/vouch/layVoucherView/list',
      query: {
        code: record.code,
        name: record.name,
        typeCode: record.typeCode,
      },
    });
  }

  // // 查询方案弹窗
  // function handleQuery(record: Recordable) {
  //   openModal(true, record);
  // }

  // // 表格弹窗
  // function handleTable(record: Recordable) {
  //   openModal2(true, record);
  // }

  // 保存表格修改
  function handleSubmit() {
    let data = getDataSource();
    console.log(data);
  }
</script>
