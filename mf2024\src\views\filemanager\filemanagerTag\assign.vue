<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicModal
    v-bind="$attrs"
    :title="t('文件标签')"
    @register="registerModal"
    @ok="handleSubmit"
    :minHeight="120"
    :width="700"
  >
    <BasicForm @register="registerForm">
      <template #fileTags>
        <div class="file-tags">
          <Tag
            v-for="(item, index) in tagList"
            :key="item.assignee"
            :color="item.tagColor"
            :class="'tag-status-' + item.status"
          >
            <span>{{ item.tagName }}</span>
            <span class="tag-button" @click="toggleTag(item, index)">
              <Icon
                v-if="item.status !== '1'"
                icon="i-ant-design:close-outlined"
                :title="t('减签')"
              />
              <Icon v-else icon="i-ant-design:plus-outlined" :title="t('恢复')" />
            </span>
          </Tag>
        </div>
      </template>
      <template #addFileTags>
        <div class="file-tags">
          <Tag v-for="(item, index) in selectListRef" :key="item.userCode" :color="item.tagColor">
            <span :class="'tag-status-' + item.status">{{ item.tagName }}</span>
            <span class="tag-button" @click="removeTag(item, index)">
              <Icon icon="i-ant-design:close-outlined" :title="t('减签')" />
            </span>
          </Tag>
          <Tag class="tag-add-button" @click="addTag()">
            <Icon icon="i-ant-design:plus-outlined" :title="t('加签')" />
          </Tag>
        </div>
        <ListSelect
          ref="listSelectRef"
          :configFile="import('./select')"
          :checkbox="true"
          :selectList="selectListRef"
          @select="onSelect"
          v-show="false"
        />
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'JeeSiteBpmTaskBack',
    inheritAttrs: false,
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref } from 'vue';
  import { Tag } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import {
    filemanagerTagAssign,
    filemanagerTagAssignSave,
  } from '/@/api/filemanager/filemanagerTag';
  import { ListSelect } from '/@/components/ListSelect';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('bpm.button');
  const { showMessage } = useMessage();
  const record = ref<Recordable>({});

  const tagList = ref<Recordable[]>([]);
  const selectListRef = ref<Recordable[]>([]);
  const listSelectRef = ref<any>(null);

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('当前标签'),
      helpMessage: t('展示你选择文件或文件夹，共同拥有的标签'),
      field: 'fileTags',
      component: 'Input',
      colProps: { lg: 23, md: 24 },
      slot: 'fileTags',
    },
    {
      label: t('新加标签'),
      helpMessage: t('选择你想要添加的的标签，将分配给你所有选择的文件或文件夹'),
      field: 'addFileTags',
      component: 'Input',
      colProps: { lg: 23, md: 24 },
      slot: 'addFileTags',
    },
  ];

  const [registerForm] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 23, md: 24 },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ loading: true });
    record.value.ids = data.ids;
    const res = await filemanagerTagAssign({ ids: data.ids });
    tagList.value = res.tagList || [];
    selectListRef.value = [];
    setModalProps({ loading: false });
  });

  const toggleTag = (item: Recordable, _index: number) => {
    item.status = item.status == '0' ? '1' : '0';
  };

  const addTag = () => {
    listSelectRef.value.openSelectModal();
  };

  const removeTag = (_item: Recordable, index: number) => {
    selectListRef.value.splice(index, 1);
  };

  const onSelect = (values: Recordable[]) => {
    selectListRef.value = values;
  };

  async function handleSubmit() {
    try {
      if (record.value.id == '') {
        showMessage(t('任务ID不能为空'));
        return;
      }
      const data: any = {};
      setModalProps({ confirmLoading: true });
      data.ids = record.value.ids;
      data.delIds = tagList.value
        .filter((e) => e.status && e.status == '1')
        .map((e) => e.tagId)
        .join(',');
      data.addIds = selectListRef.value.map((e) => e.tagId).join(',');
      // console.log('submit', data);
      const res = await filemanagerTagAssignSave(data);
      showMessage(res.message);
      setTimeout(closeModal);
      emit('success');
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
<style lang="less">
  .file-tags {
    .ant-tag {
      margin: 3px 5px 0 0 !important;
      padding: 2px 5px 2px 8px !important;
      font-size: 12px !important;
      border-radius: 15px;
      cursor: pointer;

      svg {
        color: #dadada;
      }
    }

    .tag-status-1 {
      text-decoration: line-through;
      color: #636363 !important;
      background: #dadada !important;

      svg {
        color: #636363 !important;
      }
    }

    .tag-button {
      cursor: pointer;
      padding-left: 5px;
      color: #666;
    }

    .tag-add-button {
      cursor: pointer;
      padding: 3px 8px !important;
      border-radius: 10px;

      svg {
        color: #333;
      }
    }
  }
</style>
