/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface Filemanager extends BasicModel<Filemanager> {
  parentCode?: string; // 上级文件夹
  folderId?: string; // 文件夹名
  fileUploadId; // 文件上传名
  fileName?: string; // 文件或文件夹名
  fileType?: string; // 文件分类（image图片、media媒体、file文档）
  fileExtension; // 文件扩展名
  fileSize?: number; // 文件大小(单位B)
  groupType?: string; // 分组类型
  officeCode?: string; // 部门编码
  ids?: string[]; // 主键批量处理（用来批量删除、移动、分享）
  sharedId?: string; // 分享的编码（用来验证文件查看权限）
}

export const filemanagerIndex = (params?: Filemanager | any) =>
  defHttp.get<Filemanager>({ url: adminPath + '/filemanager/index', params });

export const filemanagerList = (params?: Filemanager | any) =>
  defHttp.get<Filemanager>({ url: adminPath + '/filemanager/list', params });

export const filemanagerListData = (params?: Filemanager | any) =>
  defHttp.post<Page<Filemanager>>({ url: adminPath + '/filemanager/listData', params });

export const filemanagerForm = (params?: Filemanager | any) =>
  defHttp.get<Filemanager>({ url: adminPath + '/filemanager/form', params });

export const filemanagerSave = (params?: any, data?: Filemanager | any) =>
  defHttp.postJson<Filemanager>({ url: adminPath + '/filemanager/save', params, data });

export const filemanagerDelete = (params?: Filemanager | any) =>
  defHttp.post<Filemanager>({ url: adminPath + '/filemanager/delete', params });

export const filemanagerMoveForm = (params?: any, data?: Filemanager | any) =>
  defHttp.post<Filemanager>({ url: adminPath + '/filemanager/moveForm', params, data });

export const filemanagerMove = (params?: any, data?: Filemanager | any) =>
  defHttp.postJson<Filemanager>({ url: adminPath + '/filemanager/move', params, data });
