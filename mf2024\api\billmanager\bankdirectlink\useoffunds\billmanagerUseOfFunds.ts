/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { TreeDataModel, TreeModel } from '/@/api/model/baseModel';
import { UploadApiResult } from '/@/api/sys/upload';
import { UploadFileParams } from '/#/axios';

const { adminPath } = useGlobSetting();

export interface BillmanagerUseOfFunds extends TreeModel<BillmanagerUseOfFunds> {
  code?: string; // 编码
  name?: string; // 名称
}

export const billmanagerUseOfFundsList = (params?: BillmanagerUseOfFunds | any) =>
  defHttp.get<BillmanagerUseOfFunds>({
    url: adminPath + '/bankdirectlink/useoffunds/billmanagerUseOfFunds/list',
    params,
  });

export const billmanagerUseOfFundsListData = (params?: BillmanagerUseOfFunds | any) =>
  defHttp.post<BillmanagerUseOfFunds[]>({
    url: adminPath + '/bankdirectlink/useoffunds/billmanagerUseOfFunds/listData',
    params,
  });

export const billmanagerUseOfFundsForm = (params?: BillmanagerUseOfFunds | any) =>
  defHttp.get<BillmanagerUseOfFunds>({
    url: adminPath + '/bankdirectlink/useoffunds/billmanagerUseOfFunds/form',
    params,
  });

export const billmanagerUseOfFundsCreateNextNode = (params?: BillmanagerUseOfFunds | any) =>
  defHttp.get<BillmanagerUseOfFunds>({
    url: adminPath + '/bankdirectlink/useoffunds/billmanagerUseOfFunds/createNextNode',
    params,
  });

export const billmanagerUseOfFundsSave = (params?: any, data?: BillmanagerUseOfFunds | any) =>
  defHttp.postJson<BillmanagerUseOfFunds>({
    url: adminPath + '/bankdirectlink/useoffunds/billmanagerUseOfFunds/save',
    params,
    data,
  });

export const billmanagerUseOfFundsImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/bankdirectlink/useoffunds/billmanagerUseOfFunds/importData',
      onUploadProgress,
    },
    params,
  );

export const billmanagerUseOfFundsDisable = (params?: BillmanagerUseOfFunds | any) =>
  defHttp.get<BillmanagerUseOfFunds>({
    url: adminPath + '/bankdirectlink/useoffunds/billmanagerUseOfFunds/disable',
    params,
  });

export const billmanagerUseOfFundsEnable = (params?: BillmanagerUseOfFunds | any) =>
  defHttp.get<BillmanagerUseOfFunds>({
    url: adminPath + '/bankdirectlink/useoffunds/billmanagerUseOfFunds/enable',
    params,
  });

export const billmanagerUseOfFundsDelete = (params?: BillmanagerUseOfFunds | any) =>
  defHttp.get<BillmanagerUseOfFunds>({
    url: adminPath + '/bankdirectlink/useoffunds/billmanagerUseOfFunds/delete',
    params,
  });

export const billmanagerUseOfFundsTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({
    url: adminPath + '/bankdirectlink/useoffunds/billmanagerUseOfFunds/treeData',
    params,
  });
// billmanagerApNoteListSelect
export const billmanagerApNoteListSelect = (params?: any) =>
  defHttp.get<any[]>({
    url: adminPath + '/mf/apNote/listSelect',
    params,
  });
