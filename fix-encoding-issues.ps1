# PowerShell script to fix encoding issues in mf2024 project

Write-Host "Searching for files with encoding issues..."

# Find all Vue and TypeScript files in mf2024/src
$files = Get-ChildItem -Path "mf2024\src" -Recurse -Include *.vue,*.ts,*.tsx

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    
    # Check if file contains the problematic character sequence
    if ($content -match "修正�\?") {
        Write-Host "Fixing encoding issue in: $($file.FullName)"
        
        # Replace the problematic sequence
        $content = $content -replace "修正�\?\)", "修正。')"
        $content = $content -replace "修正�\?'", "修正。'"
        $content = $content -replace "修正�\?`"", "修正。`""
        
        # Write back with UTF8 encoding
        Set-Content $file.FullName $content -Encoding UTF8 -NoNewline
        Write-Host "Fixed: $($file.FullName)"
    }
}

Write-Host "Encoding fix completed!"
