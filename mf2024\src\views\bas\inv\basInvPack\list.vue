<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <!--<a-button type="primary" @click="handleForm({})" v-auth="'bas:inv:pack:basInvPack:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>-->
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.invCode }}
        </a>
      </template>
      <!-- 勾选 -->
      <template #checkBox="{ record, column }">
        <Checkbox :checked="record[column.dataIndex] == '1' ? true : false" />
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsBasInvBasInvPackList">
  import { unref, onMounted } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useQuery } from '/@/hooks/web/usePage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { basInvPackDelete, basInvPackListData } from '/@/api/bas/inv/pack/basInvPack';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import { Checkbox } from 'ant-design-vue';

  const getQuery = useQuery();

  const { t } = useI18n('bas.inv.pack.basInvPack');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('包装规格管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('批号'),
        field: 'addCode',
        component: 'Input',
      },
      {
        label: t('存货编码'),
        field: 'basInv.invCode',
        component: 'Input',
      },
      {
        label: t('存货名称'),
        field: 'basInv.invName',
        component: 'Input',
      },
      {
        label: t('规格型号'),
        field: 'basInv.invStd',
        component: 'Input',
      },
      {
        label: t(''),
        field: 'defBatch',
        component: 'CheckboxGroup',
        componentProps: {
          options: [{ label: '是否通用批次规则', value: '1' }],
        },
      },
      {
        label: t(''),
        field: 'bneedTuoPan',
        component: 'CheckboxGroup',
        componentProps: {
          options: [{ label: '是否托盘追溯', value: '1' }],
        },
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('存货编码'),
      dataIndex: 'invCode',
      key: 'a.inv_code',
      sorter: true,
      width: 150,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('存货名称'),
      dataIndex: 'basInv.invName',
      key: 'inv.inv_name',
      sorter: true,
      width: 230,
      align: 'left',
    },
    {
      title: t('规格型号'),
      dataIndex: 'basInv.invStd',
      key: 'inv.inv_std',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('批号'),
      dataIndex: 'addCode',
      key: 'a.add_code',
      sorter: true,
      width: 80,
      align: 'center',
    },
    {
      title: t('规格说明'),
      dataIndex: 'packName',
      key: 'a.pack_name',
      sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: t('托盘容量'),
      dataIndex: 'packSize',
      key: 'a.pack_size',
      sorter: true,
      width: 80,
      align: 'right',
    },
    {
      title: t('单件容量'),
      dataIndex: 'pieceQty',
      key: 'a.piece_qty',
      sorter: true,
      width: 80,
      align: 'right',
    },
    {
      title: t('单位'),
      dataIndex: 'basInv.unitName',
      key: 'inv.unit_name',
      sorter: true,
      width: 80,
      align: 'center',
    },
    {
      title: t('默认仓库'),
      dataIndex: 'whCode',
      key: 'a.wh_code',
      sorter: true,
      width: 80,
      align: 'center',
    },
    {
      title: t('部门编码'),
      dataIndex: 'depCode',
      key: 'a.dep_code',
      sorter: true,
      width: 80,
      align: 'center',
    },
    {
      title: t('部门名称'),
      dataIndex: 'office.officeName',
      key: 'o1.office_name',
      sorter: true,
      width: 80,
      align: 'left',
    },
    {
      title: t('附加打印数'),
      dataIndex: 'plusPrintQty',
      sorter: false,
      width: 80,
      align: 'left',
    },
    {
      title: t('是否通用批次规则'),
      dataIndex: 'defBatch',
      key: 'a.def_batch',
      sorter: true,
      width: 130,
      align: 'center',
      slot: 'checkBox',
    },
    {
      title: t('是否托盘追溯'),
      dataIndex: 'bneedTuoPan',
      key: 'a.bneed_tuoPan',
      sorter: true,
      width: 130,
      align: 'center',
      slot: 'checkBox',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑包装规格'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'bas:inv:pack:basInvPack:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除包装规格'),
        popConfirm: {
          title: t('是否确认删除包装规格'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'bas:inv:pack:basInvPack:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm }] = useTable({
    api: basInvPackListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { id: record.id };
    const res = await basInvPackDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  async function handleQuery() {
    if (getQuery.value.invCode) {
      getForm().setFieldsValue({
        'basInv.invCode': getQuery.value.invCode,
      });
      reload();
      getForm().setFieldsValue({
        'basInv.invCode': getQuery.value.invCode,
      });
    }
  }

  onMounted(async () => {
    handleQuery();
  });

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
