/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { Page } from '../model/baseModel';
import { Filemanager } from '/@/api/filemanager/filemanager';

const { adminPath } = useGlobSetting();

export const filemanagerList = (params?: Filemanager | any) =>
  defHttp.get<Filemanager>({ url: adminPath + '/filemanager/myfile/list', params });

export const filemanagerListData = (params?: Filemanager | any) =>
  defHttp.post<Page<Filemanager>>({ url: adminPath + '/filemanager/myfile/listData', params });
