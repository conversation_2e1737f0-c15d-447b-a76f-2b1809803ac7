<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'layout:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherTabToolbarForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import {
    LayVoucherTabToolbar,
    layVoucherTabToolbarSave,
    layVoucherTabToolbarForm,
  } from '../../../../api/layout/list/layVoucherTabToolbar';
  import { layVoucherClsTreeData } from '/@/api/layout/vouch/layVoucherCls';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('layout.layVoucherTabToolbar');
  const { showMessage } = useMessage();
  const record = ref<LayVoucherTabToolbar>({} as LayVoucherTabToolbar);
  const getTitle = computed(() => ({
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增表格按钮自定义') : t('编辑表格按钮自定义'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('布局标志'),
      field: 'viewCode',
      component: 'Input',
      dynamicDisabled: true,
      componentProps: {
        maxlength: 64,
      },
      rules: [
        { required: true },
        { pattern: /^[a-zA-Z0-9_]*$/, message: t('请输入字母数字下划线') },
      ],
    },
    {
      label: t('基础单据'),
      field: 'vouchCode',
      fieldLabel: 'layVoucher.name',
      dynamicDisabled: true,
      component: 'TreeSelect',
      componentProps: {
        api: layVoucherClsTreeData,
        immediate: true,
        maxlength: 64,
      },
      required: true,
    },
    {
      label: t('顺序号'),
      field: 'sortNum',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      rules: [{ required: true }, { pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('按钮标志'),
      field: 'btnKey',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      rules: [
        { required: true },
        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_\-])*[a-zA-Z0-9]$/,
          message: t('请输入2个以上字符，字母开头、允许字母数字下划线或减号、字母数字结尾'),
        },
      ],
    },
    {
      label: t('按钮类型'),
      field: 'btnType',
      component: 'Select',
      componentProps: {
        dictType: 'lay_btn_type',
        maxlength: 64,
      },
    },
    {
      label: t('权限标识'),
      field: 'auth',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('按钮图标'),
      field: 'icon',
      component: 'IconPicker',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('按钮标题'),
      field: 'title',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 24, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await layVoucherTabToolbarForm(data);
    record.value = (res.layVoucherTabToolbar || {}) as LayVoucherTabToolbar;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    updateSchema([
      {
        field: 'viewCode',
        componentProps: {
          disabled: !record.value.isNewRecord,
        },
      },
    ]);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await layVoucherTabToolbarSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
