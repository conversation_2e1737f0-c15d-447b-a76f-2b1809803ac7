<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'layout:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="80%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <Tabs v-model:activeKey="activeKey" tabPosition="left">
      <Tabs.TabPane v-if="!record.isNewRecord" key="1" :forceRender="true" tab="查询栏目设计">
        <QueryCol :colpop="colpop" ref="queryCol" />
      </Tabs.TabPane>
      <Tabs.TabPane key="2" :forceRender="true" tab="查询方案">
        <BasicForm @register="registerForm" />
      </Tabs.TabPane>
    </Tabs>
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherViewListQueryForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Tabs } from 'ant-design-vue';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import {
    LayVoucherViewListQuery,
    layVoucherViewListQuerySave,
    layVoucherViewListQueryForm,
  } from '/@/api/layout/list/layVoucherViewListQuery';
  import { layVoucherClsTreeData } from '/@/api/layout/vouch/layVoucherCls';
  import QueryCol from '../layVoucherViewListQueryCol/list.vue';
  let colpop = ref({
    vouchCode: '',
  });
  const queryCol = ref(null);

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('layout.layVoucherViewListQuery');
  const { showMessage } = useMessage();
  const record = ref<LayVoucherViewListQuery>({} as LayVoucherViewListQuery);
  const getTitle = computed(() => ({
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增查询方案') : t('编辑查询方案'),
  }));
  const activeKey = ref<string>('1');

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('基础单据'),
      field: 'vouchCode',
      fieldLabel: 'layVouch.name',
      dynamicDisabled: true,
      component: 'TreeSelect',
      componentProps: {
        maxlength: 64,
        api: layVoucherClsTreeData,
        canSelectParent: false,
      },
    },
    {
      label: t('布局标志'),
      field: 'viewCode',
      component: 'Input',
      dynamicDisabled: true,
      componentProps: {
        maxlength: 64,
      },
      required: true,
    },
    {
      label: t('查询方案标志'),
      field: 'code',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
    },
    {
      label: t('查询方案名称'),
      field: 'name',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('是否默认方案'),
      field: 'bdefualt',
      component: 'CheckboxGroup',
      componentProps: {
        // dictType: 'sys_menu_type',
        options: [{ label: '', value: '1' }],
      },
    },
    {
      label: t('备注信息'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 500,
      },
      colProps: { lg: 24, md: 24 },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 24, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    console.log(data);

    activeKey.value = '1';
    setDrawerProps({ loading: true });
    colpop.value = data;
    await resetFields();
    data.vouchCode = data.vouchCode;
    data['layVouch.name'] = data.vouchName;

    const res = await layVoucherViewListQueryForm(data);
    // res.layVoucherViewListQuery.vouchCode = data.typeName;
    record.value = (res.layVoucherViewListQuery || {}) as LayVoucherViewListQuery;

    if(record.value.isNewRecord){
      activeKey.value='2'
    }
    // record.value.vouchCode = data.typeName;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    updateSchema([
      {
        field: 'code',
        componentProps: {
          disabled: !record.value.isNewRecord,
        },
      },
    ]);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      // data.vouchCode = colpop.value.typeCode;
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        code: record.value.code,
      };
      // console.log('submit', params, data, record);
      const res = await layVoucherViewListQuerySave(params, data);
      console.log(queryCol.value);
      if (queryCol.value) {
        queryCol.value.handleSave1();
      }

      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
