/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface OaLeave extends BasicModel<OaLeave> {
  userCode?: string; // 请假人
  userName?: string; // 请假人名称
  officeCode?: string; // 部门编码
  officeName?: string; // 部门名称
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  leaveDays?: number; // 请假天数
  leaveType?: string; // 请假类型
  leaveReason?: string; // 请假原因
}

export const oaLeaveList = (params?: OaLeave | any) =>
  defHttp.get<OaLeave>({ url: adminPath + '/oa/oaLeave/list', params });

export const oaLeaveListData = (params?: OaLeave | any) =>
  defHttp.post<Page<OaLeave>>({ url: adminPath + '/oa/oaLeave/listData', params });

export const oaLeaveForm = (params?: OaLeave | any) =>
  defHttp.get<OaLeave>({ url: adminPath + '/oa/oaLeave/form', params });

export const oaLeaveSave = (params?: any, data?: OaLeave | any) =>
  defHttp.postJson<OaLeave>({ url: adminPath + '/oa/oaLeave/save', params, data });

export const oaLeaveDelete = (params?: OaLeave | any) =>
  defHttp.get<OaLeave>({ url: adminPath + '/oa/oaLeave/delete', params });
