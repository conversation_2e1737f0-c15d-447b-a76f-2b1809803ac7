{"name": "@jeesite/vite", "version": "5.12.1", "private": true, "type": "module", "scripts": {"stub": "pnpm unbuild --stub", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint:eslint": "eslint --cache --max-warnings 0  \"./**/*.{vue,ts,tsx}\" --fix", "lint:all": "npm run type:check && npm run lint:eslint", "uninstall": "rimraf node_modules", "update": "ncu -u"}, "files": ["dist"], "main": "./dist/index.mjs", "module": "./dist/index.mjs", "typings": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "types": "./dist/index.d.ts"}, "./config/getEnvConfigName": {"import": "./config/getEnvConfigName.ts", "types": "./config/getEnvConfigName.ts"}, "./theme/themeConfig": {"import": "./theme/themeConfig.ts", "types": "./theme/themeConfig.ts"}, "./dist/*": "./dist/*"}, "dependencies": {"@types/fs-extra": "11.0.4", "@vitejs/plugin-legacy": "6.1.1", "@vitejs/plugin-vue": "5.2.4", "@vitejs/plugin-vue-jsx": "4.1.2", "dotenv": "16.5.0", "fs-extra": "11.3.0", "picocolors": "1.1.1", "pkg-types": "2.1.0", "rollup-plugin-visualizer": "5.14.0", "unbuild": "3.5.0", "unocss": "66.1.1", "vite-plugin-compression": "0.5.1", "vite-plugin-html": "3.2.2", "vite-plugin-mkcert": "1.17.8", "vite-plugin-monaco-editor": "1.1.0", "vite-plugin-theme-vite3": "1.0.5", "vite-plugin-vue-setup-extend": "0.4.0"}, "homepage": "https://jeesite.com", "repository": {"type": "git", "url": "https://gitee.com/thinkgem/jeesite-vue.git"}, "bugs": {"url": "https://gitee.com/thinkgem/jeesite-vue/issues"}, "author": {"name": "ThinkGem", "email": "<EMAIL>", "url": "https://gitee.com/thinkgem"}}