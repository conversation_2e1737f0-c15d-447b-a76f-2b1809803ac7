<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Tabs v-model:activeKey="activeKey" @change="handleSuccess">
          <Tabs.TabPane key="1">
            <template #tab>
              <Icon icon="i-simple-line-icons:envelope" />
              <span class="pr-1"> {{ t('sys.msg.unreadMsgTitle') }} </span>
            </template>
          </Tabs.TabPane>
          <Tabs.TabPane key="2">
            <template #tab>
              <Icon icon="i-simple-line-icons:envelope-letter" />
              <span class="pr-1"> {{ t('sys.msg.readMsgTitle') }} </span>
            </template>
          </Tabs.TabPane>
        </Tabs>
      </template>
      <template #toolbar>
        <a-button type="default" @click="handleReadAll()">
          <Icon icon="i-ant-design:eye-outlined" /> {{ t('sys.msg.readAllMsgButton') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id, status: record.status })">
          {{ record.msgTitle }}
          <Icon v-if="record.isAttac == '1'" icon="i-fa:paperclip" />
        </a>
      </template>
    </BasicTable>
  </div>
</template>
<script lang="ts" setup name="ViewsMsgList">
  import { ref } from 'vue';
  import { Tabs } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { msgListData, msgReadAllMsg } from '/@/api/msg/msg';
  import { FormProps } from '/@/components/Form';
  import { useGo } from '/@/hooks/web/usePage';
  import { useEmitter } from '/@/store/modules/user';

  const { t } = useI18n('sys.msg');
  const { showMessage, createConfirm } = useMessage();
  const activeKey = ref<string>('1');
  const go = useGo();
  const emitter = useEmitter();

  const searchForm: FormProps = {
    baseColProps: { lg: 4, md: 8 },
    labelWidth: 80,
    schemas: [
      {
        label: t('消息标题'),
        field: 'msgTitle',
        component: 'Input',
      },
      {
        label: t('消息内容'),
        field: 'msgContent',
        component: 'Input',
      },
      {
        label: t('发送者'),
        field: 'sendUserCode',
        fieldLabel: 'sendUserName',
        component: 'ListSelect',
        componentProps: {
          selectType: 'userSelect',
        },
      },
      {
        label: t('发送时间'),
        field: 'dateRange',
        component: 'RangePicker',
        componentProps: {},
        colProps: { lg: 6 },
        labelWidth: 80,
      },
    ],
    fieldMapToTime: [['dateRange', ['sendDate_gte', 'sendDate_lte']]],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('消息标题'),
      dataIndex: 'msgTitle',
      key: 'a.msg_title',
      sorter: true,
      width: 200,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('消息内容'),
      dataIndex: 'msgContentEntity.content',
      key: 'a.msg_content',
      sorter: true,
      width: 280,
      align: 'left',
    },
    {
      title: t('发送者'),
      dataIndex: 'sendUserName',
      key: 'a.send_user_name',
      sorter: true,
      width: 100,
      align: 'center',
    },
    {
      title: t('发送时间'),
      dataIndex: 'sendDate',
      key: 'a.send_date',
      sorter: true,
      width: 150,
      align: 'center',
    },
    {
      title: t('读取时间'),
      dataIndex: 'readDate',
      key: 'a.read_date',
      sorter: true,
      width: 150,
      align: 'center',
      ifShow: () => activeKey.value == '2',
    },
  ];

  const [registerTable, { reload }] = useTable({
    api: msgListData,
    beforeFetch: (params) => {
      params.pushed = activeKey.value == '2';
      return params;
    },
    columns: tableColumns,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    go('/msg/view?id=' + record.id);
  }

  function handleReadAll() {
    createConfirm({
      content: t('sys.msg.readAllMsgConfirm'),
      iconType: 'info',
      cancelText: '取消',
      okText: '确定',
      onOk: async () => {
        const res = await msgReadAllMsg();
        showMessage(res.message);
        reload();
        emitter.emit('on-msg-notify-refresh');
      },
    });
  }

  function handleSuccess() {
    reload();
  }
</script>
