<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'mf:dh:mfCarplanDhH:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="40%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm"  @keyup.enter.native="handleEnterKey"/>
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsWmsWeighbridgeForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { carVenTreeData } from '/@/api/wms/weighbridge/carVen';
  import { CarplanDh, mfCarplanDhCForm, mfCarplanDhCSave } from '/@/api/wms/weighbridge/dh';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('sys.dh');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<CarplanDh>({} as CarplanDh);
  const carType = ref<any>('');

    


  const getTitle = computed(() => ({
    icon: meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增采购入库装车计划详情') : t('编辑采购入库装车计划详情'),
  }));

  const inputFormSchemas: FormSchema[] = [
    // {
    //   label: t('ID'),
    //   field: 'id',
    //   component: 'Input',
    //   componentProps: {
    //     maxlength: 64,
    //   },
    //   required: true,
    //   ifShow: false,
    // },
    {
      label: t('采购订单号'),
      field: 'poCode',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      show: false,
    },
    {
      label: t('存货编码'),
      field: 'invCode',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      show: false,
    },
    {
      label: t('存货名称'),
      field: 'invName',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      show: false,
    },
    {
      label: t('规格型号'),
      field: 'invStd',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
      show: false,
    },
    {
      label: t('供应商'),
      field: 'venCode',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      show: false,
    },
    {
      label: t('供应商'),
      field: 'venName',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      show: false,
    },
    {
      label: t('合同号'),
      field: 'contractCode',
      component: 'Input',
      componentProps: {
        maxlength: 100,
      },
      show: false,
    },

    {
      label: t('计划到货重量'),
      field: 'planWeight',
      component: 'InputNumber',
      componentProps: {
        maxlength: 100,
        onChange:async(e)=>{
          console.log(e);
          if(carType.value == '1'){
            if(e>0){
              let obj = await getFieldsValue()
              setFieldsValue({
                ...obj,
                planWeight:-e
              })
            }
          }else{
            if(e<0){
              let obj = await getFieldsValue()
              setFieldsValue({
                ...obj,
                planWeight:-e
              })
            }
          }
         
        }
      },
      // dynamicDisabled: true,
      required: true,
    },
    {
      label: t('建议入库重量'),
      field: 'jyWeight',
      component: 'InputNumber',
      componentProps: {
        maxlength: 100,
      },
      dynamicDisabled: true,
    },
    {
      label: t('确认入库重量'),
      field: 'qrWeight',
      component: 'InputNumber',
      componentProps: {
        maxlength: 100,
        onChange:async(e)=>{
          console.log(e);
          if(carType.value == '1'){
            if(e>0){
              let obj = await getFieldsValue()
              setFieldsValue({
                ...obj,
                qrWeight:-e
              })
            }
          }else{
            if(e<0){
              let obj = await getFieldsValue()
              setFieldsValue({
                ...obj,
                qrWeight:-e
              })
            }
          }
         
        }
      },
      required: true,
    },
    {
      label: t('入库仓库'),
      field: 'rkStore',
      component: 'Select',
      componentProps: {
        dictType: 'mf_rk_store',
      },
      required: true,
    },
    {
      label: t('件数'),
      field: 'iqty',
      component: 'InputNumber',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('供方重量'),
      field: 'gfWeight',
      component: 'InputNumber',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('海关重量'),
      field: 'hgWeight',
      component: 'InputNumber',
      componentProps: {
        maxlength: 100,
      },
    },
    {
      label: t('折算系数'),
      field: 'irate',
      component: 'InputNumber',
      componentProps: {
        maxlength: 100,
      },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, getFieldsValue, validate }] = useForm({
    labelWidth: 120,
    autoSubmitOnEnter: true,
    schemas: inputFormSchemas,
    baseColProps: { lg: 24, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await mfCarplanDhCForm(data);

    carType.value = res.carType
    record.value = (res.mfCarplanDhC || {}) as CarplanDh;
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      const res = await mfCarplanDhCSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
  function handleEnterKey() {
    handleSubmit();
  }
</script>
