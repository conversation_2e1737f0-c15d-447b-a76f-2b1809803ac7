/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../../model/baseModel';
import { UploadApiResult } from '../../../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface BillmanagerPayBudget extends BasicModel<BillmanagerPayBudget> {
  yearMonth?: string; // 预算年月
  htno?: string; // 合同编码
  htname?: string; // 合同名称
  htAmount?: number; // 合同金额
  useOfFunds?: string; // 资金用途
  venCode?: string; // 供应商编码
  venName?: string; // 供应商名称
  prjCode?: string; // 项目编码
  prjName?: string; // 项目名称
  deptCode?: string; // 部门编码
  deptName?: string; // 部门名称
  amount?: number; // 预算金额
  useAmount?: number; // 已使用金额
  syyfAmount?: number; // 应付款余额
  cstatus?: string; // 单据状态
  beachId?: string; // 导入批号
  createByName?: string; // 创建人名称
}

export const billmanagerPayBudgetList = (params?: BillmanagerPayBudget | any) =>
  defHttp.get<BillmanagerPayBudget>({
    url: adminPath + '/bankdirectlink/paybudget/billmanagerPayBudget/list',
    params,
  });

export const billmanagerPayBudgetListData = (params?: BillmanagerPayBudget | any) =>
  defHttp.post<Page<BillmanagerPayBudget>>({
    url: adminPath + '/bankdirectlink/paybudget/billmanagerPayBudget/listData',
    params,
  });

export const billmanagerPayBudgetForm = (params?: BillmanagerPayBudget | any) =>
  defHttp.get<BillmanagerPayBudget>({
    url: adminPath + '/bankdirectlink/paybudget/billmanagerPayBudget/form',
    params,
  });

export const billmanagerPayBudgetSave = (params?: any, data?: BillmanagerPayBudget | any) =>
  defHttp.postJson<BillmanagerPayBudget>({
    url: adminPath + '/bankdirectlink/paybudget/billmanagerPayBudget/save',
    params,
    data,
  });

export const billmanagerPayBudgetImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/bankdirectlink/paybudget/billmanagerPayBudget/importData',
      // 这里将 onUploadProgress 的类型从 ProgressEvent 改为 AxiosProgressEvent 以适配 defHttp.uploadFile 的类型要求
      onUploadProgress: (progressEvent: any) => {
        onUploadProgress(progressEvent as ProgressEvent);
      },
    },
    params,
  );

export const billmanagerPayBudgetDelete = (params?: BillmanagerPayBudget | any) =>
  defHttp.get<BillmanagerPayBudget>({
    url: adminPath + '/bankdirectlink/paybudget/billmanagerPayBudget/delete',
    params,
  });

export const billmanagerPayBudgetBeachDelete = (params?: BillmanagerPayBudget | any) =>
  defHttp.get<BillmanagerPayBudget>({
    url: adminPath + '/bankdirectlink/paybudget/billmanagerPayBudget/deleteAll',
    params,
  });

export const billmanagerPayBudgetBeachInvalid = (params?: BillmanagerPayBudget | any) =>
  defHttp.get<BillmanagerPayBudget>({
    url: adminPath + '/bankdirectlink/paybudget/billmanagerPayBudget/invalid',
    params,
  });

export const billmanagerPayBudgetBeachCancel = (params?: BillmanagerPayBudget | any) =>
  defHttp.get<BillmanagerPayBudget>({
    url: adminPath + '/bankdirectlink/paybudget/billmanagerPayBudget/cancel',
    params,
  });
