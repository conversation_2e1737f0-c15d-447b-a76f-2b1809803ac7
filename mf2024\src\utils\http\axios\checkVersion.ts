/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import axios from 'axios';
import { useMessage } from '/@/hooks/web/useMessage';
import { publicPath } from '/@/utils/env';
const { createConfirm } = useMessage();

let isUpdateChecked = false;
export function checkVersion(confirm = true) {
  // if (import.meta.env.DEV) return;
  const time = new Date().getTime().toString();
  const versionFile = publicPath + '/timestamp.txt?t=' + time;
  axios.get(versionFile).then((response) => {
    const storeKey = 'jeesite-build-time';
    const buildTime = response?.data + '';
    const storeTime = localStorage.getItem(storeKey);
    if (storeTime == null) {
      localStorage.setItem(storeKey, buildTime);
    } else if (storeTime !== buildTime && !isUpdateChecked) {
      const updateStore = () => {
        isUpdateChecked = true;
        localStorage.setItem(storeKey, buildTime);
        location.reload();
      };
      if (confirm) {
        createConfirm({
          content:
            '亲爱的用户，我们的系统有了新的更新，为了给您带来更好的使用体验，麻烦您刷新一下页面哦！',
          title: '更新提示',
          width: 390,
          iconType: 'info',
          maskClosable: false,
          cancelText: '稍后刷新',
          okText: '刷新页面',
          onOk: () => {
            updateStore();
          },
        });
      } else {
        updateStore();
      }
    }
  });
}
checkVersion(false);
