<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="default" @click="handleExport()">
          <Icon icon="i-ant-design:download-outlined" /> {{ t('导出') }}
        </a-button>
        <a-button type="default" @click="handleImport()">
          <Icon icon="i-ant-design:upload-outlined" /> {{ t('导入') }}
        </a-button>
        <Popconfirm :title="t('确认批量删除吗?')" @confirm="handleBeachDelete()">
          <a-button type="default">
            <Icon icon="i-ant-design:upload-outlined" /> {{ t('批量删除') }}
          </a-button>
        </Popconfirm>
        <Popconfirm :title="t('确认批量生效吗?')" @confirm="handleBeachInvalid()">
          <a-button type="default">
            <Icon icon="i-ant-design:upload-outlined" /> {{ t('批量生效') }}
          </a-button>
        </Popconfirm>
        <Popconfirm :title="t('确认批量作废吗?')" @confirm="handleBeachCancel()">
          <a-button type="default">
            <Icon icon="i-ant-design:upload-outlined" /> {{ t('批量作废') }}
          </a-button>
        </Popconfirm>
        <!-- <a-button
          type="primary"
          @click="handleForm({})"
          v-auth="'bankdirectlink:paybudget:billmanagerPayBudget:edit'"
        >
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button> -->
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.yearMonth }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
    <FormImport @register="registerImportModal" @success="handleImportSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsBankdirectlinkPaybudgetBillmanagerPayBudgetList">
  import { unref, onMounted } from 'vue';
  import { Popconfirm } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import {
    billmanagerPayBudgetDelete,
    billmanagerPayBudgetListData,
    billmanagerPayBudgetBeachDelete,
    billmanagerPayBudgetBeachInvalid,
    billmanagerPayBudgetBeachCancel,
  } from '/@/api/billmanager/bankdirectlink/paybudget/billmanagerPayBudget';
  import { useDrawer } from '/@/components/Drawer';
  import { useModal } from '/@/components/Modal';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import FormImport from './formImport.vue';

  const { t } = useI18n('bankdirectlink.paybudget.billmanagerPayBudget');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('资金预算管理'),
  };

  onMounted(async () => {
    setProps();
  });

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('预算年月'),
        field: 'yearMonth',
        component: 'Input',
      },
      {
        label: t('合同编码'),
        field: 'htno',
        component: 'Input',
      },
      {
        label: t('合同名称'),
        field: 'htname',
        component: 'Input',
      },
      // {
      //   label: t('合同金额'),
      //   field: 'htAmount',
      //   component: 'Input',
      // },
      {
        label: t('资金用途'),
        field: 'useOfFundsEntity.name',
        component: 'Input',
        // componentProps: {
        //   dictType: 'mf_amount_use',
        //   allowClear: true,
        // },
      },
      // {
      //   label: t('供应商编码'),
      //   field: 'venCode',
      //   component: 'Input',
      // },
      {
        label: t('供应商名称'),
        field: 'venName',
        component: 'Input',
      },
      // {
      //   label: t('项目编码'),
      //   field: 'prjCode',
      //   component: 'Input',
      // },
      {
        label: t('项目名称'),
        field: 'prjName',
        component: 'Input',
      },
      // {
      //   label: t('部门编码'),
      //   field: 'deptCode',
      //   component: 'Input',
      // },
      {
        label: t('部门名称'),
        field: 'deptName',
        component: 'Input',
      },
      // {
      //   label: t('预算金额'),
      //   field: 'amount',
      //   component: 'Input',
      // },
      // {
      //   label: t('已使用金额'),
      //   field: 'useAmount',
      //   component: 'Input',
      // },
      // {
      //   label: t('应付款余额'),
      //   field: 'syyfAmount',
      //   component: 'Input',
      // },
      {
        label: t('单据状态'),
        field: 'cstatus',
        component: 'Select',
        componentProps: {
          dictType: 'mf_pay_budget_status',
          allowClear: true,
        },
      },
      {
        label: t('导入批号'),
        field: 'beachId',
        component: 'Input',
      },
      {
        label: t('创建人名称'),
        field: 'createByName',
        component: 'Input',
      },
      {
        label: t('备注'),
        field: 'remarks',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('预算年月'),
      dataIndex: 'yearMonth',
      key: 'a.year_month',
      sorter: true,
      width: 100,
      align: 'left',
      // slot: 'firstColumn',
    },
    {
      title: t('合同编码'),
      dataIndex: 'htno',
      key: 'a.htno',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('合同名称'),
      dataIndex: 'htname',
      key: 'a.htname',
      sorter: true,
      width: 220,
      align: 'left',
    },
    {
      title: t('合同金额'),
      dataIndex: 'htAmount',
      key: 'a.ht_amount',
      sorter: true,
      width: 100,
      align: 'right',
    },
    {
      title: t('资金用途'),
      dataIndex: 'useOfFundsEntity.name',
      key: 'a.use_of_funds',
      sorter: true,
      width: 150,
      align: 'center',
    },
    {
      title: t('供应商编码'),
      dataIndex: 'venCode',
      key: 'a.ven_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('供应商名称'),
      dataIndex: 'venName',
      key: 'a.ven_name',
      sorter: true,
      width: 250,
      align: 'left',
    },
    {
      title: t('项目编码'),
      dataIndex: 'prjCode',
      key: 'a.prj_code',
      sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('项目名称'),
      dataIndex: 'prjName',
      key: 'a.prj_name',
      sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('部门编码'),
      dataIndex: 'deptCode',
      key: 'a.dept_code',
      sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('部门名称'),
      dataIndex: 'deptName',
      key: 'a.dept_name',
      sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('预算金额'),
      dataIndex: 'amount',
      key: 'a.amount',
      sorter: true,
      width: 100,
      align: 'right',
    },
    {
      title: t('已使用金额'),
      dataIndex: 'useAmount',
      key: 'a.use_amount',
      sorter: true,
      width: 110,
      align: 'right',
    },
    {
      title: t('应付款余额'),
      dataIndex: 'syyfAmount',
      key: 'a.syyf_amount',
      sorter: true,
      width: 110,
      align: 'right',
    },
    {
      title: t('单据状态'),
      dataIndex: 'cstatus',
      key: 'a.cstatus',
      sorter: true,
      width: 100,
      align: 'left',
      dictType: 'mf_pay_budget_status',
    },
    {
      title: t('导入批号'),
      dataIndex: 'beachId',
      key: 'a.beach_id',
      sorter: true,
      width: 200,
      align: 'left',
    },
    {
      title: t('创建人名称'),
      dataIndex: 'createByName',
      key: 'a.create_by_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('备注'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      // {
      //   icon: 'i-clarity:note-edit-line',
      //   title: t('编辑资金预算'),
      //   onClick: handleForm.bind(this, { id: record.id }),
      //   auth: 'bankdirectlink:paybudget:billmanagerPayBudget:edit',
      // },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除资金预算'),
        popConfirm: {
          title: t('是否确认删除资金预算'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'bankdirectlink:paybudget:billmanagerPayBudget:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm, getSelectRowKeys, setProps }] = useTable({
    api: billmanagerPayBudgetListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
    rowSelection: {
      type: 'checkbox',
    },
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleExport() {
    const { ctxAdminPath } = useGlobSetting();
    downloadByUrl({
      url: ctxAdminPath + '/bankdirectlink/paybudget/billmanagerPayBudget/exportData',
      params: getForm().getFieldsValue(),
    });
  }

  const [registerImportModal, { openModal: importModal }] = useModal();

  function handleImport() {
    importModal(true, {});
  }

  async function handleDelete(record: Recordable) {
    const params = { id: record.id };
    const res = await billmanagerPayBudgetDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  /**
   * 批量删除
   */
  async function handleBeachDelete() {
    const ids = getSelectRowKeys();
    if (ids.length == 0) {
      showMessage('请选择一行数据');
      return;
    }
    const params = { id: ids.join(',') };
    const res = await billmanagerPayBudgetBeachDelete(params);
    showMessage(res.message);
    handleSuccess({});
  }

  /**
   * 批量生效
   */
  async function handleBeachInvalid() {
    const ids = getSelectRowKeys();
    if (ids.length == 0) {
      showMessage('请选择一行数据');
      return;
    }
    const params = { id: ids.join(',') };
    const res = await billmanagerPayBudgetBeachInvalid(params);
    showMessage(res.message);
    handleSuccess({});
  }

  /**
   * 批量作废
   */
  async function handleBeachCancel() {
    const ids = getSelectRowKeys();
    if (ids.length == 0) {
      showMessage('请选择一行数据');
      return;
    }
    const params = { id: ids.join(',') };
    const res = await billmanagerPayBudgetBeachCancel(params);
    showMessage(res.message);
    handleSuccess({});
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }

  async function handleImportSuccess(record: Recordable) {
    const { setFieldsValue } = getForm();
    await setFieldsValue({
      beachId: record,
    });
    reload({ record });
  }
</script>
