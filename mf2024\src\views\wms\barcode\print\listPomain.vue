<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable" @fetchSuccess="fetchSuccess">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="default" @click="handlePrint({})" v-auth="'bas:pos:basPosition:edit'">
          <Icon icon="simple-line-icons:printer" /> {{ t('采购标签打印') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <span class="cursor-pointer" @click="expandCollapse(record)">
          ( {{ record.posCode }} )
        </span>
        <a @click="handleForm({ posCode: record.posCode })">
          {{ record.posCode }}
        </a>
      </template>
    </BasicTable>
    <!-- <InputForm @register="registerDrawer" @success="handleSuccess" /> -->
    <PrintModal @register="registerPrintModal" />
    <BasicModal
      v-bind="$attrs"
      @register="registerModal"
      title="标签打印"
      @ok="handleModalInvClick"
      :width="500"
      :height="300"
      okText="下载文件"
      cancelText="全部关闭"
    > 
       <div class="flex" style="justify-content: center;align-items: center;height: 100px;font-weight: bold;font-size: 20px;">
        {{ fileName }}
       </div>
      <!-- <BasicTable @register="registerPuArrTable" @row-click="handlePuArrRowClick" /> -->
    </BasicModal>
    <barSizeTypeForm @register="registerBarSizeTypeModal" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsWmsBarcodePrintListPomain',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, watch, nextTick, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import {
    basPositionDelete,
    basPositionListData,
    basPositionListDataLevel,
  } from '/@/api/bas/pos/basPosition';
  import { pomainsubListData } from '/@/api/wms/pu/order/pomain';
  import { printSnNumber } from '/@/api/wms/barcode/encode';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';
  import PrintModal from '/@/components/print/printModal.vue';
  import barSizeTypeForm from './barSizeTypeForm.vue';
  import { BarTypeEnum } from '/@/enums/defEnum';
  const props = defineProps({
    treeCode: String,
  });

  const { ctxPath } = useGlobSetting();
  const fileName = ref('');
  const pdfUrl = ref('');

  const { t } = useI18n('bas.pos.basPosition');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: router.currentRoute.value.meta.title || t('货位档案管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('订单号'),
        field: 'parent.cpoid',
        component: 'Input',
      },
      {
        label: t('订单日期'),
        field: 'parent.ddate',
        component: 'Input',
      },

      {
        label: t('供应商名称'),
        field: 'parent.venName',
        component: 'Input',
      },
      {
        label: t('供应商编码'),
        field: 'parent.venCode',
        component: 'Input',
      },
      {
        label: t('部门'),
        field: 'parent.depName',
        component: 'Input',
      },
      {
        label: t('合同号'),
        field: 'parent.htno',
        component: 'Input',
      },
      {
        label: t('存货编码'),
        field: 'invcode',
        component: 'Input',
      },
      {
        label: t('存货名称'),
        field: 'invName',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('订单号'),
      dataIndex: 'parent.cpoid',
      width: 100,
      align: 'left',
      //slot: 'firstColumn',
    },
    {
      title: t('订单日期'),
      dataIndex: 'parent.ddate',
      width: 120,
      align: 'left',
    },
    {
      title: t('供应商名称'),
      dataIndex: 'parent.venName',
      width: 200,
      align: 'left',
    },
    {
      title: t('供应商编码'),
      dataIndex: 'parent.venCode',
      width: 130,
      align: 'left',
    },

    {
      title: t('合同号'),
      dataIndex: 'parent.htno',
      width: 130,
      align: 'left',
    },
    {
      title: t('行号'),
      dataIndex: 'ivouchrowno',
      width: 130,
      align: 'left',
    },
    {
      title: t('是否称重'),
      dataIndex: 'bweight',
      width: 130,
      align: 'left',
      dictType: 'sys_yes_no',
    },
    {
      title: t('存货编码'),
      dataIndex: 'invcode',
      width: 130,
      align: 'left',
    },
    {
      title: t('存货名称'),
      dataIndex: 'invName',
      width: 130,
      align: 'left',
    },
    {
      title: t('规格型号'),
      dataIndex: 'invStd',
      width: 130,
      align: 'left',
    },
    {
      title: t('单位名称'),
      dataIndex: 'unitName',
      width: 130,
      align: 'left',
    },
    {
      title: t('订单数量'),
      dataIndex: 'iqty',
      width: 130,
      align: 'left',
    },
    {
      title: t('累计入库'),
      dataIndex: 'inQty',
      width: 130,
      align: 'left',
    },
    {
      title: t('部门名称'),
      dataIndex: 'parent.depName',
      width: 130,
      align: 'left',
    },
    {
      title: t('仓库名称'),
      dataIndex: 'parent.whName?',
      width: 130,
      align: 'left',
    },
    {
      title: t('付款条件'),
      dataIndex: 'extend.cdefine22',
      width: 130,
      align: 'left',
    },
    {
      title: t('采购物资名称'),
      dataIndex: 'extend.cdefine23',
      width: 150,
      align: 'left',
    },
    {
      title: t('采购物资规格'),
      dataIndex: 'extend.cdefine24',
      width: 150,
      align: 'left',
    },
    {
      title: t('付款方式'),
      dataIndex: 'extend.cdefine29',
      width: 130,
      align: 'left',
    },
    {
      title: t('计重方式'),
      dataIndex: 'extend.cdefine30',
      width: 130,
      align: 'left',
    },

  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑货位档案'),
        onClick: handleForm.bind(this, { posCode: record.posCode }),
        auth: 'bas:pos:basPosition:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除货位档案'),
        popConfirm: {
          title: t('是否确认删除货位档案'),
          confirm: handleDelete.bind(this, { posCode: record.posCode }),
        },
        auth: 'bas:pos:basPosition:edit',
      },
      {
        icon: 'fluent:add-circle-24-regular',
        title: t('新建下级货位档案'),
        onClick: handleForm.bind(this, {
          parentCode: record.id,
          parentName: record.posName,
        }),
        auth: 'bas:pos:basPosition:edit',
      },
    ],
  };

  const [registerPrintModal, { openModal: openPrintModal }] = useModal();
  const [registerModal, { openModal, closeModal }] = useModal();
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerBarSizeTypeModal, { openModal: openBarSizeTypeModal }] = useModal();
  const [registerTable, { reload, expandAll, expandCollapse, getForm, getSelectRows }] = useTable({
    api: pomainsubListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    //actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    pagination: true,
    canResize: true,
    rowSelection: {
      type: 'checkbox',
    },
  });

  watch(
    () => props.treeCode,
    async () => {
      await getForm().setFieldsValue({
        posCode: props.treeCode,
      });
      reload();
    },
  );

  function fetchSuccess() {
    if (props.treeCode) {
      nextTick(expandAll);
    }
  }

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const res = await basPositionDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleModalInvClick(record: Recordable) {
    // 下载
    // pdfUrl
    downloadByUrl({ url: ctxPath + pdfUrl.value });
    closeModal();
  }

  // 货位打印
  async function handlePrint() {
    let arr = await getSelectRows();
    if (getSelectRows().length == 0) {
      showMessage(t('请先选择一行数据'));
      return;
    }
    openBarSizeTypeModal(true, { arr: arr, barSizeType: BarTypeEnum.PoOrder });
    // const idsArr = arr.map((item) => {
    //   return item.id;
    // });
    // const prtqty = arr.map((item) => {
    //   return 1;
    // });
    // let params = {
    //   selIds: idsArr.join(','),
    //   barType: orderBarType,
    //   barSizeType: getForm().getFieldsValue().barSizeType,
    //   prtQtys: prtqty.join(','),
    // };
    // // 查询条件
    // const res = await printSnNumber(params);
    // if(res.result == "true"){
    //   fileName.value = res.fileName;
    //   pdfUrl.value = res.pdfUrl;
    //   await openModal(true, res);
    // } else {
    //   showMessage(res.message);
    // }
  }

  function handleSuccess() {
    reload();
  }
</script>
