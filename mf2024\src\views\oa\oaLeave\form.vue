<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'oa:oaLeave:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
    <template #footer>
      <BpmButton
        v-model:bpmEntity="record"
        bpmEntityKey="id"
        formKey="leave"
        completeText="提交"
        :completeModal="true"
        :loading="loadingRef"
        :auth="'oa:oaLeave:edit'"
        @user-select="handleUserSelect"
        @validate="handleValidate"
        @complete="handleSubmit"
        @success="handleSuccess"
        @close="closeDrawer"
      />
    </template>
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsOaOaLeaveForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { OaLeave, oaLeaveSave, oaLeaveForm } from '/@/api/oa/oaLeave';
  import { officeTreeData } from '/@/api/sys/office';
  import { BpmButton } from '/@/components/Bpm';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('oa.oaLeave');
  const { showMessage } = useMessage();
  const record = ref<OaLeave>({} as OaLeave);

  const { meta } = unref(router.currentRoute);
  const getTitle = computed(() => ({
    icon: meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('请假申请') : t('请假申请'),
  }));

  const loadingRef = ref(false);

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('请假人'),
      field: 'userCode',
      fieldLabel: 'userName',
      component: 'TreeSelect',
      componentProps: {
        api: officeTreeData,
        params: { isLoadUser: true, userIdPrefix: '' },
        canSelectParent: false,
        allowClear: true,
      },
      required: true,
    },
    {
      label: t('所属部门'),
      field: 'officeCode',
      fieldLabel: 'officeName',
      component: 'TreeSelect',
      componentProps: {
        api: officeTreeData,
        canSelectParent: false,
        allowClear: true,
      },
    },
    {
      label: t('开始时间'),
      field: 'startTime',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
      required: true,
    },
    {
      label: t('结束时间'),
      field: 'endTime',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
      required: true,
    },
    {
      label: t('请假天数'),
      field: 'leaveDays',
      component: 'Input',
      componentProps: {
        maxlength: 10,
      },
      rules: [{ required: true }, { pattern: /^\d+$/, message: t('请输入一个正整值') }],
    },
    {
      label: t('请假类型'),
      field: 'leaveType',
      component: 'Select',
      defaultValue: '2',
      componentProps: {
        dictType: 'oa_leave_type',
        allowClear: true,
      },
    },
    {
      label: t('请假原因'),
      field: 'leaveReason',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 500,
      },
      colProps: { lg: 24, md: 24 },
    },
    // {
    //   label: t('审批意见'),
    //   field: 'bpm.comment',
    //   component: 'InputTextArea',
    //   componentProps: {
    //     maxlength: 500,
    //   },
    //   colProps: { lg: 24, md: 24 },
    //   show: () => record.value.bpm.status != '2',
    // },
    // {
    //   label: t('下一步流程信息'),
    //   field: 'nextTaskInfo',
    //   component: 'FormGroup',
    //   colProps: { lg: 24, md: 24 },
    // },
    // {
    //   label: t('要求完成时间'),
    //   field: 'bpm.dueDate',
    //   component: 'DatePicker',
    //   componentProps: {
    //     format: 'YYYY-MM-DD HH:mm',
    //     showTime: { format: 'HH:mm' },
    //   },
    // },
    // {
    //   label: t('任务优先级'),
    //   field: 'bpm.priority',
    //   component: 'Select',
    //   componentProps: {
    //     dictType: 'bpm_task_priority',
    //     allowClear: true,
    //   },
    // },
    // {
    //   label: t('下一步处理人'),
    //   field: 'bpm.nextUserCodes',
    //   component: 'ListSelect',
    //   componentProps: {
    //     selectType: 'empUserSelect',
    //   },
    // },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await oaLeaveForm(data);
    record.value = (res.oaLeave || {}) as OaLeave;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleUserSelect({ task }, openUserSelectModal) {
    try {
      const data = await validate();
      openUserSelectModal(true, {
        // 流程定义Key
        prodDefKey: 'leave',
        // 当前任务节点ID，默认为第一个节点ID
        activityId: task?.activityId || 'edit',
        // 提交给流程的变量，用于线条的条件计算
        variables: {
          leaveDays: Number(data.leaveDays),
        },
      });
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    }
  }

  async function handleValidate(_event: any, formData: any) {
    try {
      const data = await validate();
      formData(true, data); // 将表单数据传递给 BpmButton
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    }
  }

  async function handleSubmit(event: any) {
    try {
      loadingRef.value = true;
      const data = event?.formData || (await validate()); // 接受 BpmButton 传递过来的表单数据
      data.bpm = Object.assign(data.bpm || {}, record.value.bpm); // 流程信息
      data.status = record.value.status; // 提交状态
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await oaLeaveSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      loadingRef.value = false;
      setDrawerProps({ confirmLoading: false });
    }
  }

  async function handleSuccess() {
    emit('success');
  }
</script>
