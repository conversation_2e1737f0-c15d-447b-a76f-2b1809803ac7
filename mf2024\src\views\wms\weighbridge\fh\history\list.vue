<template>
  <div>
    <div>
      <BasicTable @register="registerTable" @row-click="handleTestDataChildRowClick">
        <!-- 表格标题 -->
        <template #tableTitle>
          <Icon :icon="getTitle.icon" class="m-1 pr-1" />
          <span> {{ getTitle.value }} </span> 
        </template>
        <!-- 表格右上角自定义按钮（新增...） -->
        <template #toolbar>
          <div>
            <a-button
                type="primary"
                @click="btnPrint()"
                class="ml-2 mr-2"
              >
                <Icon icon="i-ant-design:import-outlined" /> {{ t('磅单导出') }}
              </a-button>
          </div>
        </template>
      </BasicTable>
      <InputForm @register="registerDrawer" @success="handleSuccess" />
      <InvPosForm @register="registerDrawer3" @success="handleSuccess" />
    </div>
    <!-- 子表 -->
    <BasicModal
      v-bind="$attrs"
      :showFooter="true"
      :okAuth="'mf:fh:mfCarplanFhH:edit'"
      @register="registerModal"
      @ok="handleOverSubmit"
    >
      <template #title>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> 修改原因：  </span> {{ overData.djNo }}
      </template>
      <BasicForm @register="registerForm" />
    </BasicModal>
  </div>
</template>

<script lang="ts">
  export default defineComponent({
    name: 'ViewsWmsWeighbridgeFhHistoryList',
  });
</script>
<script lang="ts" setup>
  import { Popconfirm } from 'ant-design-vue';
  import { defineComponent, watch, ref, onMounted, unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { listPushData, pushdata, mfCarplanFhCListData , mfCarplanFhCDelete } from '/@/api/wms/weighbridge/fh';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps,BasicForm, FormSchema, useForm } from '/@/components/Form';
  import InputForm from './../form2.vue';
  import InvPosForm from './../invPosForm.vue';
  import { useModal } from '/@/components/Modal';

  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';

  const emit = defineEmits(['success']);

  const { t } = useI18n('test.testData');
  const { showMessage } = useMessage();
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: router.currentRoute.value.meta.title || t('销售装车历史'),
  };
  const props = defineProps({
    data: { type: Object, default: {} },
  });
  watch(
    () => props.data,
    () => { },
    { immediate: true },
  );
  let zbData = ref < string > ('');
  let djNo = ref < string > ('');
  let hid = ref < string > ('');
  let overData = ref < any > ({});
  let queryParams = ref< any >({});




  const [registerModal, { openModal, closeModal,setModalProps }] = useModal();


  //配置表单内容
  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 120,
    schemas: [
      {
        label: t('发货车次'),
        field: 'mfCarplanFhH.djNo',
        component: 'Input',
      },
      {
        label: t('车牌号'),
        field: 'mfCarplanFhH.carNo',
        component: 'Input',
      },
      {
        label: t('计划发货日期'),
        field: 'mfCarplanFhH.planDate',
        component: 'RangePicker',
        // component: 'DatePicker',
        componentProps: {
          // format: 'YYYY-MM-DD HH:mm',
          // valueFormat: 'YYYY-MM-DD HH:mm',
          // showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('客户'),
        field: 'cusName',
        component: 'Input',
      },
      {
        label: t('发货单号'),
        field: 'soCode',
        component: 'Input',
      },
      {
        label: t('合同号'),
        field: 'contractCode',
        component: 'Input',
      },
      {
        label: t('存货'),
        field: 'invName',
        component: 'Input',
      },
      // {
      //   label: t('状态'),
      //   field: 'mfCarplanFhH.cstatus',
      //   component: 'Select',
      //   componentProps: {
      //     dictType: 'mf_plan_status',
      //     allowClear: true,
      //   },
      // },
      // {
      //   label: t('装车类型'),
      //   field: 'mfCarplanFhH.planType',
      //   component: 'Select',
      //   componentProps: {
      //     dictType: 'mf_plan_type',
      //     allowClear: true,
      //   },
      // },
      // {
      //   label: t('司机'),
      //   field: 'mfCarplanFhH.cdriver',
      //   component: 'Input',
      // },
      // {
      //   label: t('司机电话'),
      //   field: 'mfCarplanFhH.driverPhone',
      //   component: 'Input',
      // },
      // {
      //   label: t('运输单位'),
      //   field: 'mfCarplanFhH.carVenName',
      //   component: 'Input',
      // },
      // {
      //   label: t('U8单号'),
      //   field: 'u8Code',
      //   component: 'Input',
      // },
      {
        label: t('推送状态'),
        field: 'cstatus',
        component: 'Select',
        componentProps: {
          dictType: 'mf_plan_detail_status',
          allowClear: true,
        },
      },
      {
        label: t('类型'),
        field: 'mfCarplanFhH.carType',
        component: 'Select',
        componentProps: {
          dictType: 'mf_carplan_type',
          allowClear: true,
        },
      },
      {
        label: t('导出状态'),
        field: 'bprint',
        component: 'Select',
        componentProps: {
          dictType: 'sys_yes_no',
          allowClear: true,
        },
      },
    ],
    fieldMapToTime: [['mfCarplanFhH.planDate', ['planDate_gte', 'planDate_lte']]],
  };

  //配置表格表头菜单
  const tableColumns: BasicColumn[] = [
    {
      title: t('发货车次'),
      dataIndex: 'mfCarplanFhH.djNo',
      key: 'mfCarplanFhH.dj_no',
      sorter: true,
      width: 100,
      align: 'left',
      fixed: 'left',
    },
    {
      title: t('车牌号'),
      dataIndex: 'mfCarplanFhH.carNo',
      key: 'mfCarplanFhH.car_no',
      sorter: true,
      width: 150,
      align: 'center',
    },
    {
      title: t('类型'),
      dataIndex: 'mfCarplanFhH.carType',
      key: 'mfCarplanFhH.car_type',
      sorter: true,
      width: 80,
      dictType: 'mf_carplan_type',
    },
    {
      title: t('发货单号'),
      dataIndex: 'soCode',
      key: 'a.so_code',
      sorter: true,
      width: 150,
    },
    {
      title: t('状态'),
      dataIndex: 'cstatus',
      key: 'a.cstatus',
      sorter: true,
      width: 80,
      dictType: 'mf_plan_detail_status',
    },
    {
      title: t('确认发货重量'),
      dataIndex: 'qrWeight',
      key: 'a.qr_weight',
      sorter: true,
      width: 150,
      customRender: ({ record }) => {
        // 保留两位小数
        return record.qrWeight ? Number(record.qrWeight).toFixed(4) : '';
      },
    },
    {
      title: t('计重方式'),
      dataIndex: 'cinvdefine1',
      key: 'a.cinvdefine1',
      width: 120,
    },
    {
      title: t('计划发货日期'),
      dataIndex: 'mfCarplanFhH.planDate',
      key: 'mfCarplanFhH.plan_date',
      sorter: true,
      width: 150,
      align: 'center',
    },
    {
      title: t('客户编码'),
      dataIndex: 'cusCode',
      key: 'a.cus_code',
      sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('客户'),
      dataIndex: 'cusName',
      key: 'a.cus_name',
      sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('存货编码'),
      dataIndex: 'invCode',
      key: 'a.inv_code',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('存货名称'),
      dataIndex: 'invName',
      key: 'a.inv_name',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('规格型号'),
      dataIndex: 'invStd',
      key: 'a.inv_std',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('计划发货重量'),
      dataIndex: 'planWeight',
      key: 'a.plan_weight',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('建议发货重量'),
      dataIndex: 'jyWeight',
      key: 'a.jy_weight',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('出库仓库'),
      dataIndex: 'cwhname',
      key: 'a.cwhname',
      sorter: true,
      width: 120,
      dictType: 'mf_rk_store',
    },

    {
      title: t('发货批次'),
      dataIndex: 'ibatch',
      key: 'a.ibatch',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('发货货位'),
      dataIndex: 'cposition',
      key: 'a.cposition',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('客户重量'),
      dataIndex: 'cusWeight',
      key: 'a.cus_weight',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('合同号'),
      dataIndex: 'contractCode',
      key: 'a.contract_code',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('承运单位'),
      dataIndex: 'cyAddress',
      key: 'a.cy_address',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('承运单价'),
      dataIndex: 'cyPrice',
      key: 'a.cy_price',
      sorter: true,
      width: 120,
      align: 'left',
    },
    {
      title: t('备注'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 120,
      align: 'left',
    },
  ];

  //配置表格右边操作按钮
  const actionColumn: BasicColumn = {
    width: 120,
    align: 'left',
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'mf:fh:mfCarplanFhH:edit',
        ifShow: () => record.cstatus != '1',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除数据'),
        popConfirm: {
          title: t('是否确认删除数据'),
          confirm: handleDelete.bind(this, { id: record.id }),
        },
        auth: 'mf:fh:mfCarplanFhH:edit',
        ifShow: () => record.cstatus != '1',
      },
      {
        icon: 'i-simple-line-icons:magnifier',
        title: t('查看货位信息'),
        onClick: handlePos.bind(this, { id: record.id }),
        auth: 'mf:fh:mfCarplanFhH:edit',
      },
    ],
  };

  const inputFormSchemas: FormSchema[] = [
    {
      label: t(''),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 500,
        rows:5
      },
      colProps: { lg: 24, md: 24 },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    // schemas: inputFormSchemas,
    baseColProps: { lg: 24, md: 24 },
  });

  const selectedRowKeysRef = ref<string[]>([]);
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerDrawer3, { openDrawer:openDrawer3 }] = useDrawer();

  const [registerTable, { reload, setProps, getSelectRows, getForm }] = useTable({
    api: mfCarplanFhCListData,
    beforeFetch: (params) => {
      params['mfCarplanFhH.planType']= 2;
      return params;
    },
    afterFetch: (params) => {
      return params;
    },
    columns: tableColumns, //配置表格内容数组对象
    formConfig: searchForm, //配置表单内容
    showTableSetting: true, //表格右上角3个默认按钮
    useSearchForm: true, //表单是否展示
    canResize: true, //表格是否flex布局
    // scroll: {
    //   y: 250,
    // },
    rowSelection: {
      type: 'checkbox',
    },
    resizeHeightOffset: 80,
    defaultRowSelection: {
      onChange: (selectedRowKeys: string[], _selectedRows: Recordable[]) => {
        selectedRowKeysRef.value = selectedRowKeys;
      },
    },
    showSummary: true,
    summaryFunc: handleSummary,
  });

  function handleSummary(tableData: Recordable[]) {
    const totalqrWeight = tableData.reduce((prev, next) => {
      prev += next.qrWeight ? next.qrWeight : 0;
      return prev;
    }, 0);
    // planWeight
    const totalPlanWeight = tableData.reduce((prev, next) => {
      prev += next.planWeight ? next.planWeight : 0;
      return prev;
    }, 0);
    return [
      {
        _row: '合计',
        qrWeight: totalqrWeight.toFixed(2),
        planWeight: totalPlanWeight.toFixed(2),
      },
    ];
  }

  onMounted(() => {
    if (!props.data.picno) {
      setProps({
        actionColumn: actionColumn,
      });
    }
  });


  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }
  
  async function btnSdPush() {
    if (getSelectRows().length) {

      var selIds = ref('');
      getSelectRows().forEach((item) => {
        selIds.value += item.id + ',';
      });
      const res = await pushdata({ ids: selIds.value, planType: 2 });
      selectedRowKeysRef.value = [];
      showMessage(res.message);
      handleSuccess();
    } else {
      showMessage('请先选择车次！！');
    }
  }

  // 鼠标行点击事件，获取上表数据
  function handleTestDataChildRowClick(record: Recordable) {
    // totalWeight.value = record.planWeight;
    // 把选中的数据里面的确认发货重量全部累加给totalWeight
  }

  async function handleOverSubmit(record: Recordable) {
    try {
      const data = await validate();
      data.id = overData.value.id
      data.djNo = overData.value.djNo
      data.cstatus = '2'
      setModalProps({ confirmLoading: true });
      const res = await updateCzStatusByHand(data);
      selectedRowKeysRef.value = [];
      showMessage(res.message);
      handleSuccess();
      closeModal();
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  async function handleDelete(record: Recordable) {
    const res = await mfCarplanFhCDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleSuccess() {
    emit('success');
    reload();
  }

  function handlePos(record: Recordable) {
    openDrawer3(true, record);
  }
  async function btnPrint() {
    // const res = await printForm(data);
    // showMessage(res.message);
    const { ctxAdminPath } = useGlobSetting();
    downloadByUrl({
      url: ctxAdminPath + '/mf/fh/mfCarplanFhC/printForm',
      params: getForm().getFieldsValue(),
    });
  }
</script>