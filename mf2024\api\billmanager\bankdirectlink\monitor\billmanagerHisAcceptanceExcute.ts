/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface BillmanagerHisAcceptanceExcute extends BasicModel<BillmanagerHisAcceptanceExcute> {
  beginDate?: string; // begin_date
  endDate?: string; // end_date
  createByName?: string; // create_by_name
}

export const billmanagerHisAcceptanceExcuteList = (params?: BillmanagerHisAcceptanceExcute | any) =>
  defHttp.get<BillmanagerHisAcceptanceExcute>({ url: adminPath + '/billmanager/bankdirectlink/monitor/billmanagerHisAcceptanceExcute/list', params });

export const billmanagerHisAcceptanceExcuteListData = (params?: BillmanagerHisAcceptanceExcute | any) =>
  defHttp.post<Page<BillmanagerHisAcceptanceExcute>>({ url: adminPath + '/billmanager/bankdirectlink/monitor/billmanagerHisAcceptanceExcute/listData', params });

export const billmanagerHisAcceptanceExcuteForm = (params?: BillmanagerHisAcceptanceExcute | any) =>
  defHttp.get<BillmanagerHisAcceptanceExcute>({ url: adminPath + '/billmanager/bankdirectlink/monitor/billmanagerHisAcceptanceExcute/form', params });

export const billmanagerHisAcceptanceExcuteSave = (params?: any, data?: BillmanagerHisAcceptanceExcute | any) =>
  defHttp.postJson<BillmanagerHisAcceptanceExcute>({ url: adminPath + '/billmanager/bankdirectlink/monitor/billmanagerHisAcceptanceExcute/save', params, data });

export const billmanagerHisAcceptanceExcuteDelete = (params?: BillmanagerHisAcceptanceExcute | any) =>
  defHttp.get<BillmanagerHisAcceptanceExcute>({ url: adminPath + '/billmanager/bankdirectlink/monitor/billmanagerHisAcceptanceExcute/delete', params });
