/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WmsRd01 extends BasicModel<WmsRd01> {
  djno?: string; // 入库单号
  invCode?: string; // 存货编码
  podid?: string; // 订单子表ID
  poid?: string; // 订单ID
  ddate?: string; // 入库日期
  whCode?: string; // 仓库
  planType?: string; // 装车类型
  btaxcost?: string; // 取价方式
  itaxrate?: number; // 税率
  iexchrate?: number; // 汇率
  createByName?: string; // create_by_name
  updateByName?: string; // update_by_name
  poCode?: string; // 采购订单号
  childList?: any[]; // 子表列表
}

export interface WmsRds01 extends BasicModel<WmsRds01> {
  parentId?: string; // 父ID
  iqty?: number; // 数量
  inum?: number; // 件数
  cbatch?: string; // 批次
  posCode?: string; // 货位
  ioritaxcost?: number; // 原币含税单价
  ioricost?: number; // 原币无税单价
  btaxcost?: string; // 取价方式
  itaxrate?: number; // 税率
  iexchrate?: number; // 汇率
  iorimoney?: number; // 原币无税金额
  iorisum?: number; // 原币价税合计
  ioritaxprice?: number; // 原币税额
}

export const wmsRd01List = (params?: WmsRd01 | any) =>
  defHttp.get<WmsRd01>({ url: adminPath + '/wms/pu/rd01/wmsRd01/list', params });

export const wmsRd01ListData = (params?: WmsRd01 | any) =>
  defHttp.post<Page<WmsRd01>>({ url: adminPath + '/wms/pu/rd01/wmsRd01/listData', params });

export const wmsRd01subListData = (params?: WmsRd01 | any) =>
  defHttp.get<WmsRd01>({ url: adminPath + '/wms/pu/rd01/wmsRd01/subListData', params });

export const wmsRd01Form = (params?: WmsRd01 | any) =>
  defHttp.get<WmsRd01>({ url: adminPath + '/wms/pu/rd01/wmsRd01/form', params });

export const wmsRd01Save = (params?: any, data?: WmsRd01 | any) =>
  defHttp.postJson<WmsRd01>({ url: adminPath + '/wms/pu/rd01/wmsRd01/save', params, data });

export const wmsRd01Delete = (params?: WmsRd01 | any) =>
  defHttp.get<WmsRd01>({ url: adminPath + '/wms/pu/rd01/wmsRd01/delete', params });
