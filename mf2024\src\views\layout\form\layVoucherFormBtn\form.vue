<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'layout:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="pr-1 m-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherFormBtnForm',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { LayVoucherFormBtn, layVoucherFormBtnSave, layVoucherFormBtnForm } from '../../../../api/layout/form/layVoucherFormBtn';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('layout.layVoucherFormBtn');
  const { showMessage } = useMessage();
  const record = ref<LayVoucherFormBtn>({} as LayVoucherFormBtn);
  const getTitle = computed(() => ({
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增按钮') : t('编辑按钮'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('布局标志'),
      field: 'viewCode',
      dynamicDisabled: true,
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
      required: true,
    },
    {
      label: t('按钮类型'),
      field: 'btnType',
      component: 'Select',
      componentProps: {
        dictType: 'lay_btn_type',
        allowClear: true,
      },
    },
    {
      label: t('权限'),
      field: 'auth',
      component: 'Input',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      label: t('图标'),
      field: 'icon',
      component: 'IconPicker',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('按钮标题'),
      field: 'title',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
    },
    {
      label: t('按钮ID'),
      field: 'btnId',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate, updateSchema }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await layVoucherFormBtnForm(data);
    record.value = (res.layVoucherFormBtn || {}) as LayVoucherFormBtn;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    updateSchema([
      {
        field: 'viewCode',
        componentProps: {
          disabled: !record.value.isNewRecord,
        },
      },
    ]);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        id: record.value.id,
      };
      // console.log('submit', params, data, record);
      const res = await layVoucherFormBtnSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('您填写的信息有误，请根据提示修正。'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
