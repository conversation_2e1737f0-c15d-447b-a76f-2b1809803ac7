<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleForm({ vouchCode: props.colpop.code })"
          v-auth="'layout:edit'"
        >
          <Icon icon="fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.vouchCode }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'ViewsLayoutLayVoucherFieldsList',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { layVoucherFieldsDelete, layVoucherFieldsListData } from '../../../../api/layout/vouch/layVoucherFields';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('layout.layVoucherFields');
  const { showMessage } = useMessage();
  const props = defineProps({
    colpop: { type: Object, default: {} },
  });
  const getTitle = {
    icon: router.currentRoute.value.meta.icon || 'ant-design:book-outlined',
    value: props.colpop.activeKey == '1'?'扩展字段':'基础字段',
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('单据标志'),
        field: 'vouchCode',
        component: 'Input',
      },
      {
        label: t('物理名称'),
        field: 'physicalName',
        component: 'Input',
      },
      {
        label: t('逻辑名称'),
        field: 'logicalName',
        component: 'Input',
      },
      {
        label: t('类型'),
        field: 'type',
        component: 'Input',
      },
      {
        label: t('字段长度'),
        field: 'length',
        component: 'Input',
      },
      {
        label: t('小数位数'),
        field: 'decimal',
        component: 'Input',
      },
      {
        label: t('描述'),
        field: 'description',
        component: 'Input',
      },
      {
        label: t('默认值'),
        field: 'defaultValue',
        component: 'Input',
      },
      {
        label: t('是否主键'),
        field: 'primaryKey',
        component: 'CheckboxGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否必填'),
        field: 'notNull',
        component: 'CheckboxGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否强制更新'),
        field: 'isUpdateForce',
        component: 'CheckboxGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
      {
        label: t('是否查询字段'),
        field: 'isQuery',
        component: 'CheckboxGroup',
        componentProps: {
          dictType: 'sys_yes_no',
        },
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('单据标志'),
      dataIndex: 'vouchCode',
      key: 'a.vouch_code',
      sorter: true,
      width: 130,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('字段名称'),
      dataIndex: 'physicalName',
      key: 'a.physicalname',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('显示名称'),
      dataIndex: 'logicalName',
      key: 'a.logicalname',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('字段类型'),
      dataIndex: 'type',
      key: 'a.type',
      sorter: true,
      width: 130,
      align: 'left',
      dictType: 'extend_column_type',
    },
    {
      title: t('字段长度'),
      dataIndex: 'length',
      key: 'a.length',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('小数位数'),
      dataIndex: 'decimal',
      key: 'a.decimal',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('默认值'),
      dataIndex: 'defaultValue',
      key: 'a.defaultvalue',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('是否插入字段'),
      dataIndex: 'isInsert',
      key: 'a.isinsert',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否更新字段'),
      dataIndex: 'isUpdate',
      key: 'a.isupdate',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否强制更新'),
      dataIndex: 'isUpdateForce',
      key: 'a.isupdateforce',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
    },
    {
      title: t('是否查询字段'),
      dataIndex: 'isQuery',
      key: 'a.isquery',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_yes_no',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'clarity:note-edit-line',
        title: t('编辑字段扩展'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'layout:edit',
      },
      {
        icon: 'ant-design:delete-outlined',
        color: 'error',
        title: t('删除字段扩展'),
        popConfirm: {
          title: t('是否确认删除字段扩展'),
          confirm: handleDelete.bind(this, { id: record.id }),
        },
        auth: 'layout:edit',
      },
    ],
  };

  
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm }] = useTable({
    api: layVoucherFieldsListData,
    beforeFetch: (params) => {
      params.vouchCode = props.colpop.code;
      params.colType = props.colpop.activeKey == '1'?1:0;
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: false,
    canResize: true,
    pagination: false,
  });
  watch(
    () => props.colpop,
    () => {
      reload();
    },
    // { immediate: true },
  );

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const res = await layVoucherFieldsDelete(record);
    showMessage(res.message);
    handleSuccess();
  }

  function handleSuccess() {
    reload();
  }
</script>
